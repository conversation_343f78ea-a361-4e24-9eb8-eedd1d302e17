#!/usr/bin/env bash
# Test script to verify we can stop and restart services

set -e

echo "=== Testing Service Control ==="
echo ""

# Function to check if a port is listening
port_is_listening() {
  local PORT=$1
  ss -ltnp 2>/dev/null | grep -E ":${PORT}\\b" >/dev/null 2>&1
}

# Function to count processes on a port
count_port_processes() {
  local PORT=$1
  ss -ltnp 2>/dev/null | grep -E ":${PORT}\\b" | wc -l
}

# Ports to check
PORTS=(8080 9080 8081 8083)

echo "1. Checking initial state..."
for PORT in "${PORTS[@]}"; do
  if port_is_listening "$PORT"; then
    COUNT=$(count_port_processes "$PORT")
    echo "   ✓ Port $PORT is listening ($COUNT process(es))"
  else
    echo "   ✗ Port $PORT is NOT listening"
  fi
done

echo ""
echo "2. Attempting to stop services..."
/workspaces/server/stop-codespace.sh

echo ""
echo "3. Verifying services are stopped..."
sleep 2
ALL_STOPPED=true
for PORT in "${PORTS[@]}"; do
  if port_is_listening "$PORT"; then
    COUNT=$(count_port_processes "$PORT")
    echo "   ✗ FAIL: Port $PORT still listening ($COUNT process(es))"
    ALL_STOPPED=false
  else
    echo "   ✓ Port $PORT is stopped"
  fi
done

if [ "$ALL_STOPPED" = "true" ]; then
  echo ""
  echo "✅ SUCCESS: All services stopped successfully!"
  exit 0
else
  echo ""
  echo "❌ FAILURE: Some services failed to stop"
  exit 1
fi
