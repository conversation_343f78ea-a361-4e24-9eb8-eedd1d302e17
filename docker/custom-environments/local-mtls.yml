version: '3.8'

x-mtls-base: &mtls-base
  volumes:
    - ../deploy/scripts/certs/mtls:/etc/divinci/certs/mtls:ro
  environment:
    - MTLS_ENABLED=true
    - MTLS_CA_CERT=/etc/divinci/certs/mtls/ca.crt
    - MTLS_SERVER_CERT=/etc/divinci/certs/mtls/server.crt
    - MTLS_SERVER_KEY=/etc/divinci/certs/mtls/server.key

services:
  public-api:
    <<: *mtls-base
    ports:
      - "8443:8443"

  public-api-live:
    <<: *mtls-base
    ports:
      - "8444:8443"

  public-api-webhook:
    <<: *mtls-base
    ports:
      - "8445:8443"