#!/bin/bash
set -euo pipefail

# Configuration
CERT_DIR="${MTLS_CERT_DIR:-/workspaces/server/deploy/scripts/certs/mtls}"
KEY_SIZE=4096
DAYS_VALID=365
CA_PASSPHRASE="${CA_PASSPHRASE:-}"

if [ -z "$CA_PASSPHRASE" ]; then
    echo "Warning: CA_PASSPHRASE not set. Using less secure unencrypted private key."
fi

# Create directories with secure permissions
echo "Creating certificates directory..."
mkdir -p "$CERT_DIR"
chmod 700 "$CERT_DIR"

cd "$CERT_DIR" || {
    echo "Error: Failed to change to certificate directory"
    exit 1
}

echo "Generating CA key and certificate..."
if [ -n "$CA_PASSPHRASE" ]; then
    openssl genrsa -aes256 -passout env:CA_PASSPHRASE -out ca.key $KEY_SIZE || {
        echo "Error: Failed to generate CA key"
        exit 1
    }
    openssl req -x509 -new -passin env:CA_PASSPHRASE -key ca.key -sha256 -days $DAYS_VALID -out ca.crt \
        -subj "/C=US/ST=California/L=San Francisco/O=Divinci AI/OU=Engineering/CN=Divinci AI CA" || {
        echo "Error: Failed to generate CA certificate"
        exit 1
    }
else
    openssl genrsa -out ca.key $KEY_SIZE || {
        echo "Error: Failed to generate CA key"
        exit 1
    }
    openssl req -x509 -new -nodes -key ca.key -sha256 -days $DAYS_VALID -out ca.crt \
        -subj "/C=US/ST=California/L=San Francisco/O=Divinci AI/OU=Engineering/CN=Divinci AI CA" || {
        echo "Error: Failed to generate CA certificate"
        exit 1
    }
fi

echo "Generating server key and CSR..."
openssl genrsa -out server.key $KEY_SIZE || {
    echo "Error: Failed to generate server key"
    exit 1
}
openssl req -new -key server.key -out server.csr -config server.conf || {
    echo "Error: Failed to generate server CSR"
    exit 1
}

echo "Signing server certificate..."
openssl x509 -req -in server.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out server.crt -days $DAYS_VALID -sha256 -extensions v3_req -extfile server.conf

echo "Generating client key and CSR..."
openssl genrsa -out client.key $KEY_SIZE
openssl req -new -key client.key -out client.csr -config client.conf

echo "Signing client certificate..."
openssl x509 -req -in client.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out client.crt -days $DAYS_VALID -sha256 -extensions v3_req -extfile client.conf

echo "Converting client key to PKCS8 format..."
openssl pkcs8 -topk8 -inform PEM -outform PEM -nocrypt -in client.key -out client_pkcs8.key

echo "Cleaning up CSR files..."
rm *.csr

echo "Setting correct permissions..."
# Set restrictive permissions on private keys
chmod 600 ca.key server.key client.key client_pkcs8.key || {
    echo "Error: Failed to set key file permissions"
    exit 1
}
# Set read-only permissions on certificates
chmod 644 *.crt || {
    echo "Error: Failed to set certificate file permissions"
    exit 1
}

echo "Done! Generated certificates in $CERT_DIR:"
ls -la

# Add newlines at end of files if missing
for f in *.{key,crt,conf}; do
    if [ -f "$f" ] && [ -n "$(tail -c1 "$f")" ]; then
        echo "" >> "$f"
    fi
done