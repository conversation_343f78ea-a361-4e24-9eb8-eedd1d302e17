#!/usr/bin/env bash
# Stop all main services started by start-codespace.sh

set -u

# Ports that typically host long-running dev services in this repo.
# Add or remove ports here as your setup changes.
PORTS=(8080 8081 8082 9080 8083)

echo "Stopping services on ports: ${PORTS[*]}"

# Kill any start-codespace.sh processes first (these may respawn services)
START_PIDS=$(pgrep -f "start-codespace.sh" || true)
if [ -n "$START_PIDS" ]; then
  echo "Stopping start-codespace.sh processes: $START_PIDS"
  kill $START_PIDS 2>/dev/null || true
  sleep 1
fi

# Kill pnpm/node parent processes that spawn child processes
echo "Stopping pnpm and node processes..."
pkill -f "pnpm start" 2>/dev/null || true
pkill -f "pnpm run build" 2>/dev/null || true
sleep 1

# Prefer PID files created by start-codespace.sh if present
PIDS_DIR="${PIDS_DIR:-/workspaces/server/run}"
if [ -d "$PIDS_DIR" ]; then
  echo "Using PID files from $PIDS_DIR to stop services (if present)"
  for pidfile in "$PIDS_DIR"/*.pid; do
    [ -f "$pidfile" ] || continue
    pid=$(cat "$pidfile" | tr -d '[:space:]' || true)
    if [ -n "$pid" ]; then
      if kill -0 "$pid" 2>/dev/null; then
        echo "Stopping PID $pid (from $pidfile)"
        # Kill the process group to catch child processes
        kill -- -"$pid" 2>/dev/null || kill "$pid" 2>/dev/null || true
        sleep 1
        if kill -0 "$pid" 2>/dev/null; then
          echo "Forcing kill on $pid"
          kill -9 -- -"$pid" 2>/dev/null || kill -9 "$pid" 2>/dev/null || true
        fi
      else
        echo "PID $pid in $pidfile is not running"
      fi
      # Remove the pidfile
      rm -f "$pidfile" 2>/dev/null || true
    fi
  done
fi

# Final sweep: kill any remaining processes on our ports
for PORT in "${PORTS[@]}"; do
  # Use ss to extract PIDs from the output
  PIDS=$(ss -ltnp 2>/dev/null | grep -E ":${PORT}\\b" | sed -n 's/.*pid=\([0-9]*\).*/\1/p' || true)

  if [ -n "$PIDS" ]; then
    echo "Found remaining processes on port $PORT: $PIDS"
    # Try graceful shutdown first
    for pid in $PIDS; do
      kill -TERM "$pid" 2>/dev/null || true
    done
    sleep 1

    # Force kill if still running
    for pid in $PIDS; do
      if kill -0 "$pid" 2>/dev/null; then
        echo "Force killing PID $pid on port $PORT"
        kill -9 "$pid" 2>/dev/null || true
      fi
    done
  fi
done

echo "Stopped all Codespace services."

