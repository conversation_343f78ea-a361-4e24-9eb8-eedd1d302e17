#!/usr/bin/env bash
# Test full restart cycle

set -e

echo "=== Testing Full Restart Cycle ==="
echo ""

# Function to check if a port is listening
port_is_listening() {
  local PORT=$1
  ss -ltnp 2>/dev/null | grep -E ":${PORT}\\b" >/dev/null 2>&1
}

# Ports to check
PORTS=(8080 9080 8081 8083)

echo "1. Starting services in background..."
/workspaces/server/start-codespace.sh > /tmp/restart-test.log 2>&1 &
START_PID=$!

# Wait for services to start
echo "   Waiting for services to start (max 2 minutes)..."
TIMEOUT=120
ELAPSED=0
ALL_UP=false

while [ $ELAPSED -lt $TIMEOUT ]; do
  sleep 5
  ELAPSED=$((ELAPSED + 5))

  # Check if all ports are up
  UP_COUNT=0
  for PORT in "${PORTS[@]}"; do
    if port_is_listening "$PORT"; then
      UP_COUNT=$((UP_COUNT + 1))
    fi
  done

  if [ $UP_COUNT -eq ${#PORTS[@]} ]; then
    ALL_UP=true
    echo "   ✓ All services started after ${ELAPSED}s"
    break
  fi

  echo "   ... ${UP_COUNT}/${#PORTS[@]} services up (${ELAPSED}s elapsed)"
done

if [ "$ALL_UP" != "true" ]; then
  echo "   ✗ FAIL: Services failed to start within timeout"
  exit 1
fi

echo ""
echo "2. Testing restart script..."
/workspaces/server/restart-services.sh > /tmp/restart-test2.log 2>&1 &
RESTART_PID=$!

# Wait for restart to complete
echo "   Waiting for restart to complete (max 2 minutes)..."
ELAPSED=0
ALL_UP=false

while [ $ELAPSED -lt $TIMEOUT ]; do
  sleep 5
  ELAPSED=$((ELAPSED + 5))

  # Check if all ports are up
  UP_COUNT=0
  for PORT in "${PORTS[@]}"; do
    if port_is_listening "$PORT"; then
      UP_COUNT=$((UP_COUNT + 1))
    fi
  done

  if [ $UP_COUNT -eq ${#PORTS[@]} ]; then
    ALL_UP=true
    echo "   ✓ All services restarted after ${ELAPSED}s"
    break
  fi

  echo "   ... ${UP_COUNT}/${#PORTS[@]} services up (${ELAPSED}s elapsed)"
done

if [ "$ALL_UP" != "true" ]; then
  echo "   ✗ FAIL: Restart failed"
  exit 1
fi

echo ""
echo "3. Testing stop script..."
/workspaces/server/stop-codespace.sh

sleep 2

echo ""
echo "4. Verifying all services are stopped..."
ALL_STOPPED=true
for PORT in "${PORTS[@]}"; do
  if port_is_listening "$PORT"; then
    echo "   ✗ FAIL: Port $PORT still listening"
    ALL_STOPPED=false
  else
    echo "   ✓ Port $PORT is stopped"
  fi
done

if [ "$ALL_STOPPED" = "true" ]; then
  echo ""
  echo "✅ SUCCESS: Full restart cycle works correctly!"
  exit 0
else
  echo ""
  echo "❌ FAILURE: Some services failed to stop"
  exit 1
fi
