Development Codespace quickstart

This repository includes convenience scripts and a devcontainer to make running the services locally (or in GitHub Codespaces) easy.

## AUTO_START_SERVICES

The devcontainer supports an optional automatic service startup after the container finishes its `postStart` setup. This is controlled by the `AUTO_START_SERVICES` environment variable in `.devcontainer/devcontainer.json`.

- Location: `.devcontainer/devcontainer.json` (containerEnv)
- Default: `"AUTO_START_SERVICES": "true"` (can be changed to `false` if you prefer manual control)

What it does

- When enabled, the postStart script will run `/workspaces/server/start-codespace.sh` in the background and write logs to `/workspaces/server/start.log`.

Why you might disable it

- You may want to prevent long background processes from starting automatically (for faster Codespace startup or to avoid resource usage).
- You may want to start services only when you're ready or when you have particular environment settings in place.

How to disable

- Edit `.devcontainer/devcontainer.json` and set:

  "containerEnv": {
  "AUTO_START_SERVICES": "false",
  ...
  }

- Rebuild/reopen the Codespace (or set the environment variable in the Codespaces UI) so the change takes effect.

Manual commands (if AUTO_START_SERVICES is disabled)

- Start services:

  ./start-codespace.sh

- Stop services:

  ./stop-codespace.sh

- Restart (uses updated CORS and other config):

  ./restart-services.sh

Logging & troubleshooting

- Start log: `/workspaces/server/start.log`
- Individual service logs: `/workspaces/server/*.log`
- PID files: `/workspaces/server/run/*.pid`

If you'd like I can add a small README section showing how to turn the feature on and off from the Codespaces UI as well.

## Using the Codespaces UI

You can change `AUTO_START_SERVICES` without editing the repository files by setting an environment variable for Codespaces, or you can edit `.devcontainer/devcontainer.json` and rebuild the container.

Option 1 — Set a repository Codespaces variable (no file edits)

- In the GitHub repository, go to Settings -> Codespaces -> Repository variables (or "Secrets and variables" -> "Codespaces").
- Add a variable named `AUTO_START_SERVICES` with value `false` (or `true`) to override the value in the devcontainer for all new Codespaces.
- Rebuild/reopen the Codespace so the new environment variable is picked up.

Option 2 — Edit the devcontainer file (version-controlled)

- Open `.devcontainer/devcontainer.json` and change the `containerEnv.AUTO_START_SERVICES` value to `"false"` or `"true"`.
- Rebuild the container (in VS Code: open the Command Palette and run "Dev Containers: Rebuild Container" or use the Codespaces web UI to rebuild/reopen).

Notes

- Changing the value requires rebuilding or reopening the Codespace so the container picks up the new environment variables.
- If you disable auto-start you can still start services manually inside the Codespace using:

```bash
./start-codespace.sh
```
