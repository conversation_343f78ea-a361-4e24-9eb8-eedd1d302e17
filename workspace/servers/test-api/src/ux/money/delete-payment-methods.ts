import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import Strip<PERSON> from "stripe";

import { getStripeInstance, getUserId } from "@divinci-ai/server-globals";

import { StripeCustomerModel } from "@divinci-ai/server-models";

export const deletePaymentMethods: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);

    const customer = await StripeCustomerModel.findOrCreate(userId);

    await deleteAllPaymentMethod(customer.stripeCustomerId);

    res.statusCode = 200;
    res.json({ status: "actively deleted" });
  }catch(e){
    next(e);
  }
};

function deleteAllPaymentMethod(stripeCustomerId: string){
  const stripe = getStripeInstance();
  return deleteAllPaginated<Stripe.PaymentMethod>(
    async (lastPreviousPaymentMethod)=>{
      const result = await stripe.customers.listPaymentMethods(
        stripeCustomerId,
        {
          limit: 100,
          starting_after: lastPreviousPaymentMethod ? lastPreviousPaymentMethod.id : void 0
        }
      );
      return { hasMore: result.has_more, list: result.data };
    },
    (paymentMethod)=>(stripe.paymentMethods.detach(paymentMethod.id))
  );
}

async function deleteAllPaginated<T>(
  getNextPage: (lastItem: T | undefined, numOffset: number)=>Promise<{ hasMore: boolean, list: Array<T> }>,
  deleteItem: (v: T)=>Promise<any>
){
  const deletePromises: Array<Promise<any>> = [];
  let numOffset = 0;
  let lastPreviousItem: T | undefined;

  do {
    const { hasMore, list } = await getNextPage(lastPreviousItem, numOffset);

    if(hasMore){
      lastPreviousItem = list.at(-1);
      numOffset += list.length;
    } else {
      lastPreviousItem = void 0;
    }

    const p = Promise.all(list.map(deleteItem));

    deletePromises.push(p);
  } while(typeof lastPreviousItem !== "undefined");

  return Promise.all(deletePromises);
}
