import { Router } from "express";

export const router = Router();

import { resetMoneyModels } from "./reset";
router.delete("/reset-models", resetMoneyModels);

import { getUsersWithdrawables } from "./withdrawable";
router.get("/withdrawables", getUsersWithdrawables);

import { cancelBasicStripeSubscription } from "./cancel-subscription";
router.delete("/stripe-subscription", cancelBasicStripeSubscription);

import { deletePaymentMethods } from "./delete-payment-methods";
router.delete("/payment-methods", deletePaymentMethods);

import { makeTranscriptValueWalletFreeGoBroke } from "./go-free-broke";
router.delete("/free-transcript-wallet", makeTranscriptValueWalletFreeGoBroke);

import { makeTranscriptValueWalletPaidGoBroke } from "./go-paid-broke";
router.delete("/paid-transcript-wallet", makeTranscriptValueWalletPaidGoBroke);

import { makeWithdrawableWalletGoBroke } from "./withdrawable-broke";
router.delete("/withdrawable-wallet", makeWithdrawableWalletGoBroke);
