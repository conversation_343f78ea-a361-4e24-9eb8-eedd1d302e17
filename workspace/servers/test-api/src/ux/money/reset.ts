import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { Model } from "mongoose";

import { isDivinciEmail } from "../../util/user";

import {
  TranscriptModel,
  ChatModel,
  WhiteLabelModel, WhiteLabelQAPromptsModel,
  DocPermissionModel,

  StripeCustomerModel,
  TranscriptValueWalletModel, TranscriptValueTransactionModel,
  WithdrawableWalletModel, WithdrawableTransactionModel,
} from "@divinci-ai/server-models";

import { getUserId, getUserById } from "@divinci-ai/server-globals";

const MODEL_LIST: Array<Model<any>> = [
  TranscriptModel,
  ChatModel,
  WithdrawableWalletModel, WithdrawableTransactionModel,
  DocPermissionModel,

  StripeCustomerModel,
  WhiteLabelModel, WhiteLabelQAPromptsModel,
  TranscriptValueWalletModel, TranscriptValueTransactionModel,
];

export const resetMoneyModels: RequestHandler = async (req, res, next)=>{
  try {
    const userId = getUserId(req);
    const user = await getUserById(userId);

    isDivinciEmail(user.email);

    await Promise.all(MODEL_LIST.map((model)=>(model.deleteMany({}))));

    res.statusCode = 200;
    res.json({ status: "ok" });

  }catch(e){
    next(e);
  }
};
