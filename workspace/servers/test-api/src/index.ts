import { setupEnv } from "@divinci-ai/server-utils";
import { resolve as pathResolve } from "path";

setupEnv({ envPath: pathResolve(__dirname, "../env") });

import { setupGlobalErrorCatchers } from "./setup/error";
import { setupDBs } from "./setup/database";
import { setupHttpApp } from "./setup/http";

Promise.resolve().then(async () => {

  await setupGlobalErrorCatchers();

  await attemptExternal();

  await setupDBs();

  const httpApp = await setupHttpApp();

  const server = new Server();

  server.on("request", function(req: IncomingMessage, res: ServerResponse){
    httpApp(req, res);
  });

  const PORT = process.env.HTTP_PORT ? parseInt(process.env.HTTP_PORT) : 8081;
  server.listen(PORT, () => {
    console.log(`✅ Server is running on port ${PORT}`);
  });

})
.catch((error) => {
  console.error("❌ Error while starting the server:", error);
});

import { delay } from "@divinci-ai/utils";
import { IncomingMessage, Server, ServerResponse } from "http";

function attemptExternal(){
  return Promise.race([
    Promise.resolve().then(async ()=>{
      await delay(5 * 1000);
      throw new Error("⌛️ Timed out making external call");
    }),
    Promise.resolve().then(async ()=>{
      const resp = await fetch("https://pokeapi.co/api/v2/pokemon/ditto");
      await resp.json();
      if(!resp.ok) {
        console.error("🙅🏻‍♂️ public-api can't make external calls.");
        throw new Error("🫣 Bad external Call.");
      }
      console.log("🙌 Successful external call to: https://pokeapi.co/api/v2/pokemon/ditto");
    })
  ]);
}
