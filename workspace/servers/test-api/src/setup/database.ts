import { getMongoose, getRedis, getChatRedisClient } from "@divinci-ai/server-globals";

export async function setupDBs() {

  const [mongoose, { redisClient, chatRedisClient }] = await Promise.all([
    connectToMongodb(),
    connectToRedis(),
  ])

  return {
    mongoose,
    redisClient,
    chatRedisClient,
  };
}

async function connectToMongodb(){
  try {
    console.log("Attempting Mongoose Connection");
    const { mongoose, connect: connectToMongoose } = getMongoose();
    await connectToMongoose();
    console.log("✅🌱 Successfully connected to MongoDB. ");
    return mongoose;
  }catch(err){
    console.error("❌ Failed to connect to MongoDB: \n", err);
    throw err;
  }
}

async function connectToRedis(){
  try {
    console.log("Attempting Redis Connection");
    const { redisClient } = getRedis();
    const { chatRedisClient } = getChatRedisClient();
    await Promise.all([
      redisClient.connect(),
      chatRedisClient.connect()
    ]);
    console.log("✅🌱 Successfully connected to Redis. ");
    return { redisClient, chatRedisClient };
  }catch(err){
    console.error("❌ Failed to connect to Redis: \n", err);
    throw err;
  }
}