import { Server as HttpServer } from "http";
import { Server as HttpsServer } from "https";
import { readFileSync } from "fs";

export interface ServerSetupOptions {
  port?: number;
  defaultPort: number;
}

export function createServer(
  options: ServerSetupOptions
): HttpServer | HttpsServer {
  const httpsEnabled = process.env.HTTPS === "true";
  const port =
    options.port || process.env.HTTP_PORT
      ? parseInt(process.env.HTTP_PORT!)
      : options.defaultPort;

  if (httpsEnabled) {
    const sslCertPath = process.env.SSL_CERT_PATH;
    const sslKeyPath = process.env.SSL_KEY_PATH;

    if (!sslCertPath || !sslKeyPath) {
      console.warn(
        "⚠️  HTTPS enabled but SSL_CERT_PATH or SSL_KEY_PATH not provided, falling back to HTTP"
      );
      return new HttpServer();
    }

    try {
      const cert = readFileSync(sslCertPath, "utf8");
      const key = readFileSync(sslKeyPath, "utf8");

      console.log(`🔒 Creating HTTPS server on port ${port}`);
      return new HttpsServer({ cert, key });
    } catch (error) {
      console.error(
        "❌ Failed to load SSL certificates, falling back to HTTP:",
        error
      );
      return new HttpServer();
    }
  }

  console.log(`🌐 Creating HTTP server on port ${port}`);
  return new HttpServer();
}

export function getServerPort(defaultPort: number): number {
  return process.env.HTTP_PORT ? parseInt(process.env.HTTP_PORT) : defaultPort;
}
