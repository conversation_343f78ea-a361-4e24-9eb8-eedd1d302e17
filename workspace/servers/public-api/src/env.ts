import { setupEnv } from "@divinci-ai/server-utils";
import { resolve as pathResolve } from "path";

const addedVars = setupEnv({ envPath: pathResolve(__dirname, "../../../../private-keys/local-fast"), quiet: true });

if(isMain()){
  console.log(
    Object.entries(addedVars)
    .filter(([, value])=>(typeof value !== "undefined"))
    .map(([key, value])=>{
      // Escape any existing single quotes in the value
      const escapedValue = String(value).replace(/'/g, "'\\''");

      // Always quote values that contain commas or spaces, or if it's CORS_PORTS
      if(key === "CORS_PORTS" ||
          (typeof value === "string" && (value.includes(",") || value.includes(" ")))) {
        return `${key}='${escapedValue}'`;
      }
      return `${key}=${escapedValue}`;
    })
    .join(" ")
  );
}

export { addedVars };

function isMain(){
  // If running in a CommonJS environment
  if(typeof require !== "undefined" && typeof module !== "undefined"){
    return require.main === module;
  }

  /*
  // If running in an ES Module environment
  // @ts-expect-error 1470
  if(typeof import.meta !== 'undefined'){
    // @ts-expect-error 1470
    return import.meta.url === new URL(import.meta.url, import.meta.url).href;
  }
  */

  // Default to false if we can't determine the environment
  return false;
}
