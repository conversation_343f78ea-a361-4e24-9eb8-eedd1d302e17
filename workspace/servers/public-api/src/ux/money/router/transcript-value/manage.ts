import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { jsonExtraResponse } from "@divinci-ai/server-utils";

import { TranscriptValueWalletModel, TranscriptValueTransactionModel } from "@divinci-ai/server-models";
import { getUserId } from "@divinci-ai/server-globals";

export const getTranscriptValueWallet: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);

    const [wallet, status] = await Promise.all([
      TranscriptValueWalletModel.findOrCreate(userId),
      TranscriptValueWalletModel.getSubscriptionStatus(userId),
    ]);

    res.statusCode = 200;
    jsonExtraResponse(res, { wallet, status });

  }catch(e){
    next(e);
  }
};

export const getTranscriptValueTransactions: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);

    const [owner, runner] = await Promise.all([
      TranscriptValueTransactionModel.find({
        walletOwner: userId,
      }).sort({ timestamp: 1 }).exec(),
      TranscriptValueTransactionModel.find({
        runnerUser: userId,
      }).sort({ timestamp: 1 }).exec(),
    ]);

    res.statusCode = 200;
    jsonExtraResponse(res, { owner, runner });

  }catch(e){
    next(e);
  }
};


