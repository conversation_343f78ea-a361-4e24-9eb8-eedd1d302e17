import { Router } from "express";

import {
  getTranscriptValueWallet, getTranscriptValueTransactions,
} from "./manage";

import { transcriptValueSubscriptionStart, transcriptValueSubscriptionManage } from "./subscription";

export const router = Router();

router.get("/wallet", getTranscriptValueWallet);
router.get("/transactions", getTranscriptValueTransactions);

router.get("/start", transcriptValueSubscriptionStart);
router.get("/manage", transcriptValueSubscriptionManage);



