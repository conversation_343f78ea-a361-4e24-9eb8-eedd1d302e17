import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";

import { jsonExtraResponse, jsonBody, } from "@divinci-ai/server-utils";

import { WithdrawableWalletModel, WithdrawableTransactionModel } from "@divinci-ai/server-models";
import { getUserId } from "@divinci-ai/server-globals";
import { castShallowObject } from "@divinci-ai/utils";

export const getWithdrawableWallet: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);

    const wallet = await WithdrawableWalletModel.findOrCreate(userId);

    res.statusCode = 200;
    jsonExtraResponse(res, wallet);

  }catch(e){
    next(e);
  }
};

export const getWithdrawableTransactions: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);

    const wallet = await WithdrawableTransactionModel.find({
      $or: [
        { toUser: userId, transactionType: "send" },
        { fromUser: userId, transactionType: "withdraw" },
      ]
    });

    res.statusCode = 200;
    jsonExtraResponse(res, wallet);

  }catch(e){
    next(e);
  }
};

export const runPayout: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);

    const [wallet, body] = await Promise.all([
      WithdrawableWalletModel.findOrCreate(userId),
      jsonBody(req)
    ]);

    const { amount } = castShallowObject(body, { amount: "number" });

    const updated = await wallet.withdrawValue(amount);

    res.statusCode = 200;
    jsonExtraResponse(res, updated);

  }catch(e){
    next(e);
  }
};
