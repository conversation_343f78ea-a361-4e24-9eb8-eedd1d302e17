import { Router } from "express";

export const router = Router();

import { getWithdrawableWallet, getWithdrawableTransactions, runPayout } from "./manage";
router.get("/wallet", getWithdrawableWallet);
router.get("/transactions", getWithdrawableTransactions);
router.post("/payout", runPayout);

import { setupPaymentMethod, listPaymentMethods, managePaymentMethods } from "./payment-methods";
router.get("/payment-method/create", setupPaymentMethod);
router.get("/payment-method/list", listPaymentMethods);
router.get("/payment-method/manage", managePaymentMethods);
