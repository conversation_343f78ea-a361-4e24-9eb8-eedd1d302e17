import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { WhiteLabelModel, MessagePrefixModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS, jsonBody } from "@divinci-ai/server-utils";
import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";
import { castShallowObject } from "@divinci-ai/utils";


export const createPrefix: RequestHandler = async function(req, res, next){
  try {
    const whitelabelId = getParam(req, "whitelabelId");
    console.log("🐸 1 create prefix hit whitelabelId: ", whitelabelId);

    const whitelabel = await WhiteLabelModel.findById(whitelabelId);
    console.log("🐸 1 create prefix whitelabel:", whitelabel);

    const body = await jsonBody(req);

    // const [whitelabel, body] = await Promise.all([
    //   jsonBody(req),
    // ]);
    console.log("🐸 2 create prefix body:", body);

    if(whitelabel === null) {
      throw HTTP_ERRORS.NOT_FOUND;
    }

    const { prefix } = castShallowObject(body, {
      prefix: "string",
    });
    console.log("🐸 3 create prefix prefix:", prefix);

    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION,
      id: whitelabelId,
    });
    console.log("🐸 4 create prefix target:", target);

    const doc = new MessagePrefixModel({
      target: target,
      prefix,
    });
    console.log("🐸 5 create prefix doc:", doc);

    await doc.save();
    console.log("🐸 6 create prefix saved:", doc);

    res.statusCode = 200;
    res.json(doc);
 }catch(e){
    console.error("❌ [createPrefix] Error:", e);
    next(e);
  }
};
