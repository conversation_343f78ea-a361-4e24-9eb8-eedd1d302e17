

import { Router } from "express";

export const router = Router({ mergeParams: true });

router.use((req, res, next)=>{console.log("hitting threads"); next(); });

import { getThreadListUsingWhiteLabel } from "./routes/getThreadList";
router.get(
  "/",
  getThreadListUsingWhiteLabel,
);

import { getThreadItemUsingWhiteLabel } from "./routes/getThreadItem";
router.get(
  "/:chatId",
  getThreadItemUsingWhiteLabel,
);
