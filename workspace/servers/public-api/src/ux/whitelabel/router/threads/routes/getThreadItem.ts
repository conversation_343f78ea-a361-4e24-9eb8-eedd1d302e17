import { ChatModel, TranscriptModel, WhiteLabelModel, WhiteLabelReleaseModel } from "@divinci-ai/server-models";
import { Request<PERSON>and<PERSON> } from "express";

import { getUserId } from "@divinci-ai/server-globals";
import {
  HTTP_ERRORS_WITH_CONTEXT,
  getParam,
} from "@divinci-ai/server-utils";

export const getThreadItemUsingWhiteLabel: RequestHandler = async (req, res, next)=>{
  try {
    getUserId(req);

    const whitelabelId = getParam(req, "whitelabelId");
    const chatId = getParam(req, "chatId");

    const whitelabel = await WhiteLabelModel.findById(whitelabelId);

    if(whitelabel === null) {
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("White Label Doesn't Exist");
    }

    const allReleases = await WhiteLabelReleaseModel.find({ whitelabel: whitelabelId })
    .select(["_id", "slug", "version", "title", "description", "category", "status"]);
    if(allReleases.length === 0){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("No Releases for this Whitelabel");
    }

    const releaseIds = allReleases.map((r)=>(r._id.toString()));

    const chat = await ChatModel.findOne({
      _id: chatId, releases: { $in: releaseIds }
    });

    if(chat === null){
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("Chat Thread Doesn't Exist");
    }

    const transcript = await TranscriptModel.findById(chat.transcriptId);
    if(transcript === null){
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("Transcript Doesn't Exist");
    }
    transcript.messages = transcript.messages.filter((m)=>{
      if(!m.release) return false;
      return releaseIds.includes(m.release);
    });
    const releases = allReleases.filter((r)=>(chat.releases.includes(r._id.toString())));

    res.status(200);
    res.json({ chat, transcript, releases });

  }catch(e){
    next(e);
  }
};
