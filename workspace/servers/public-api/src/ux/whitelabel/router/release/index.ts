import { Router } from "express";

export const router = Router({ mergeParams: true });

import { createReleaseDraft } from "./root/create-release-draft";
router.post("/", createReleaseDraft);

import { listAllRelease } from "./root/list-all-release";
router.get("/", listAllRelease);

import { listAvailableRelease } from "./root/list-available-release";
router.get("/available", listAvailableRelease);

import { listDraftReleases } from "./root/list-drafts";
router.get("/drafts", listDraftReleases);


import { getReleaseById } from "./item/get-by-id";
router.get("/:releaseId", getReleaseById);

import { updateReleaseDraft } from "./item/update-draft";
router.post("/:releaseId", updateReleaseDraft);

import { deleteDraft } from "./item/delete-draft";
router.delete("/:releaseId", deleteDraft);

import { forkReleaseAsDraft } from "./item/fork-as-draft";
router.post("/:releaseId/fork", forkReleaseAsDraft);

import { releaseReleaseDraft } from "./item/release";
router.get("/:releaseId/release", releaseReleaseDraft);

import { updateActiveRelease } from "./item/release";
router.post("/:releaseId/update", updateActiveRelease);

import { updateActiveReleaseModeration } from "./item/release";
router.post("/:releaseId/moderation", updateActiveReleaseModeration);

import { deprecateRelease } from "./item/deprecate";
router.get("/:releaseId/deprecate", deprecateRelease);

