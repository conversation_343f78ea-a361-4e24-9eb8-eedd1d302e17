import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { castShallowObject, castToObject, JSON_Unknown } from "@divinci-ai/utils";

import { castPromptModerationConfig } from "../prompt-moderation/cast-config";

export { castPromptModerationConfig };

export function castDraftBody(rawBody: JSON_Unknown){
  const body = castToObject(rawBody, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Body must be an object"));
  const {
    slug, title, description,
    allowAnonymousChat, maxAnonymousChatMessages
  } = castShallowObject(body, {
    slug: "string", title: "string", description: "string",
    allowAnonymousChat: "boolean",
    maxAnonymousChatMessages: "number",
  });


  const assistant = castAssistant(body.assistant);
  const promptModeration = castPromptModerationConfig(body.promptModeration);
  const threadPrefix = castOptionalObjectWithId(body.threadPrefix, "threadPrefix");
  const msgPrefix = castOptionalObjectWithId(body.msgPrefix, "msgPrefix");
  const ragVectorIndex = castOptionalObjectWithId(body.ragIndex, "ragIndex");

  return {
    slug, title, description,
    allowAnonymousChat, maxAnonymousChatMessages,
    assistant,
    promptModeration,
    threadPrefix,
    msgPrefix,
    ragVectorIndex,
  };
}

function castAssistant(assistantUncasted: undefined | JSON_Unknown){
  if(typeof assistantUncasted === "undefined"){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Bad assistant");
  }
  const assistantObj = castToObject(
    assistantUncasted, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Bad assistant")
  );

  return castShallowObject(
    assistantObj,
    { finetune: "boolean", id: "string" },
    HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Bad assistant")
  );
}

function castOptionalObjectWithId(uncasted: undefined | JSON_Unknown, property: string){
  if(!uncasted) return undefined;

  return castShallowObject(
    uncasted, { id: "string" }, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Bad " + property)
  );
}
