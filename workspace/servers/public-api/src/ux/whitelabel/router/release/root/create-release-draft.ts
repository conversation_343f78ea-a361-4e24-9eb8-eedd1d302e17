import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { WhiteLabelModel, WhiteLabelReleaseModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS, jsonBody } from "@divinci-ai/server-utils";

import { castDraftBody } from "../util";

export const createReleaseDraft: RequestHandler = async function(req, res, next){
  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const [whitelabel, body] = await Promise.all([
      WhiteLabelModel.findById(whitelabelId),
      jsonBody(req),
    ]);

    if(whitelabel === null) throw HTTP_ERRORS.NOT_FOUND;

    const {
      slug, title, description,
      allowAnonymousChat, maxAnonymousChatMessages,

      assistant, promptModeration, threadPrefix, msgPrefix, ragVectorIndex,
    } = castDraftBody(body);

    const doc = await WhiteLabelReleaseModel.createReleaseDraft(
      { whitelabelId, slug, },
      { title, description },
      { allowAnonymousChat, maxAnonymousChatMessages },
      {
        assistant, promptModeration, threadPrefix, msgPrefix, ragIndex: ragVectorIndex
      }
    );

    res.statusCode = 200;
    res.json(doc);
 }catch(e){
    next(e);
  }
};
