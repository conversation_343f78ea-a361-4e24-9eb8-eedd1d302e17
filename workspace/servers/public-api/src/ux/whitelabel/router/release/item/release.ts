import { WhiteLabelModel, WhiteLabelReleaseModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";
import { RequestHandler } from "express";

export const releaseReleaseDraft: RequestHandler = async function(req, res, next){
  try {

    const whitelabelId = getParam(req, "whitelabelId");
    const releaseId = getParam(req, "releaseId");
    const [whitelabel, release] = await Promise.all([
      WhiteLabelModel.findById(whitelabelId),
      WhiteLabelReleaseModel.findById(releaseId),
    ]);

    if(!whitelabel || !release){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    if(release.whitelabel !== whitelabel._id.toString()){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    await release.finalizeRelease();

    res.statusCode = 200;
    res.json({ status: "ok" });
  }catch(e){
    next(e);
  }
};

export const updateActiveRelease: RequestHandler = async function(req, res, next){
  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const releaseId = getParam(req, "releaseId");
    const [whitelabel, release, body] = await Promise.all([
      WhiteLabelModel.findById(whitelabelId),
      WhiteLabelReleaseModel.findById(releaseId),
      jsonBody(req),
    ]);

    if(!whitelabel || !release){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    if(release.whitelabel !== whitelabel._id.toString()){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    if(release.status === "draft"){
      throw HTTP_ERRORS_WITH_CONTEXT.LOCKED("Release is a draft");
    }


    const { title, description, allowAnonymousChat, maxAnonymousChatMessages } = castShallowObject(body, {
      title: "string", description: "string",
      allowAnonymousChat: "boolean", maxAnonymousChatMessages: "number",
    });

    release.title = title;
    release.description = description;
    release.allowAnonymousChat = allowAnonymousChat;
    release.maxAnonymousChatMessages = maxAnonymousChatMessages;

    await release.save();

    res.statusCode = 200;
    res.json(release);
  }catch(e){
    next(e);
  }
};

import { castPromptModerationConfig } from "../util";
export const updateActiveReleaseModeration: RequestHandler = async function(req, res, next){
  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const releaseId = getParam(req, "releaseId");
    const [whitelabel, release, body] = await Promise.all([
      WhiteLabelModel.findById(whitelabelId),
      WhiteLabelReleaseModel.findById(releaseId),
      jsonBody(req),
    ]);

    if(!whitelabel || !release){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    if(release.whitelabel !== whitelabel._id.toString()){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    if(release.status === "draft"){
      throw HTTP_ERRORS_WITH_CONTEXT.LOCKED("Release is a draft");
    }

    const promptModeration = castPromptModerationConfig(body);

    release.promptModeration = promptModeration;

    await release.save();

    res.statusCode = 200;
    res.json(release);
  }catch(e){
    next(e);
  }
};
