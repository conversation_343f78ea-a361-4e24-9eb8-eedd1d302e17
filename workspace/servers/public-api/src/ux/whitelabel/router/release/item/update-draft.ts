import { WhiteLabelModel, WhiteLabelReleaseModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT, jsonBody } from "@divinci-ai/server-utils";
import { RequestHandler } from "express";
import { castDraftBody } from "../util";

export const updateReleaseDraft: RequestHandler = async function(req, res, next){
  try {

    const whitelabelId = getParam(req, "whitelabelId");
    const releaseId = getParam(req, "releaseId");
    const [whitelabel, release, body] = await Promise.all([
      WhiteLabelModel.findById(whitelabelId),
      WhiteLabelReleaseModel.findById(releaseId),
      jsonBody(req),
    ]);

    if(!whitelabel || !release){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    if(release.whitelabel !== whitelabel._id.toString()){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    if(release.status !== "draft"){
      throw HTTP_ERRORS_WITH_CONTEXT.LOCKED("Release is no longer a draft");
    }

    const {
      slug, title, description,
      allowAnonymousChat, maxAnonymousChatMessages,
      assistant, promptModeration, threadPrefix, msgPrefix, ragVectorIndex
    } = castDraftBody(body);


    release.slug = slug;
    release.title = title;
    release.allowAnonymousChat = allowAnonymousChat;
    release.maxAnonymousChatMessages = maxAnonymousChatMessages;
    release.description = description;

    release.assistant = assistant;
    release.promptModeration = promptModeration;
    release.threadPrefix = threadPrefix;
    release.msgPrefix = msgPrefix;
    release.ragIndex = ragVectorIndex;

    await release.save();

    res.statusCode = 200;
    res.json({ status: "ok" });
  }catch(e){
    next(e);
  }
};
