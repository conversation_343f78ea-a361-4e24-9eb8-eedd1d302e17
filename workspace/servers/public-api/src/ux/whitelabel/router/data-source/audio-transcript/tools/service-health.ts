import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import fetch from "node-fetch-commonjs";
import { 
  OFFICIAL_PYANNOTE_HEALTH_URL,
  DIVINCI_PYANNOTE_HEALTH_URL, FFMPEG_HEALTH_URL
 } from "../../../../../system/pyannote-constants";

/**
 * Check the health of audio processing services
 */
export const checkServiceHealth: RequestHandler = async function(req, res, next) {
  try {
    const services = {
      official_pyannote: {
        url: OFFICIAL_PYANNOTE_HEALTH_URL,
        status: "checking"
      },
      divinci_pyannote: {
        url: DIVINCI_PYANNOTE_HEALTH_URL,
        status: "checking"
      },
      ffmpeg: {
        url: FFMPEG_HEALTH_URL,
        status: "checking"
      }
    };

    const results: Record<string, { status: string, message?: string }> = {};

    // Check each service
    for (const [name, service] of Object.entries(services)) {
      try {
        console.log(`🔍 Checking health of ${name} service at ${service.url}`);

        // Create an AbortController with a timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        try {
          const response = await fetch(service.url, {
            signal: controller.signal
          });

          // Clear the timeout since the request completed
          clearTimeout(timeoutId);

          if (response.ok) {
            results[name] = { status: "available" };
            console.log(`✅ ${name} service is available`);
          } else {
            results[name] = {
              status: "unavailable",
              message: `Service returned status ${response.status}`
            };
            console.log(`❌ ${name} service returned status ${response.status}`);
          }
        } catch (error: any) {
          // Clear the timeout in case of error
          clearTimeout(timeoutId);

          // Check if the error was due to timeout
          if (error && error.name === 'AbortError') {
            results[name] = {
              status: "unavailable",
              message: "Request timed out after 5 seconds"
            };
            console.log(`❌ ${name} service request timed out`);
          } else {
            // Re-throw other errors to be caught by the outer catch block
            throw error;
          }
        }
      } catch (error) {
        results[name] = {
          status: "unavailable",
          message: error instanceof Error ? error.message : String(error)
        };
        console.log(`❌ Error checking ${name} service: ${error}`);
      }
    }

    const response = {
      timestamp: new Date().toISOString(),
      services: results
    };

    console.log('Service health check response:', JSON.stringify(response, null, 2));
    res.json(response);
  } catch (error) {
    next(error);
  }
};
