import { SPEAKER_DIARIZERS, SPEECH_TO_TEXT } from "@divinci-ai/server-tools";
import { Router } from "express";
import {
  dryRunDiarizeVideo, dryRunSliceVideo, dryRunTranscribeVideo,
  dryRunSliceTranscribeVideo
} from "./dryrun";
import { checkServiceHealth } from "./service-health";

export const router = Router({ mergeParams: true });

router.get("/", async (req, res, next)=>{
  try {
    res.statusCode = 200;
    res.json({
      diarizers: SPEAKER_DIARIZERS.getAllInfo(),
      transcribers: SPEECH_TO_TEXT.getAllInfo()
    });
  }catch(e){
    next(e);
  }
});

import { doesAudioExist } from "./form-getters";
router.get("/audio-exists", doesAudioExist);
import { validAudioMimetypes } from "./form-getters";
router.get("/valid-mimetypes", validAudioMimetypes);

// Add the new dryrun endpoints
router.post("/dryrun/diarize-video", dryRunDiarizeVideo);
router.post("/dryrun/slice-video", dryRunSliceVideo);
router.post("/dryrun/transcribe-video", dryRunTranscribeVideo);
router.post("/dryrun/slice-transcribe-video", dryRunSliceTranscribeVideo);

// Add the service health check endpoint
router.get("/service-health", checkServiceHealth);
