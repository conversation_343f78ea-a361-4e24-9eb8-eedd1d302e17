import { getAudioR2Instance } from "@divinci-ai/server-globals";
import { requireEnvVar } from "@divinci-ai/server-utils";

// Use requireEnvVar with a fallback for local development
const getEnvWithFallback = (key: string, fallback: string) => {
  try {
    return requireEnvVar(key);
  } catch (error) {
    console.warn(`⚠️ Environment variable ${key} not found, using fallback value`);
    return fallback;
  }
};

export const CLOUDFLARE_AUDIO_PUBLIC_URL = getEnvWithFallback("CLOUDFLARE_AUDIO_PUBLIC_URL", "https://assets.divinci.ai");

// Add local audio public URL for local development
export const LOCAL_AUDIO_PUBLIC_URL = process.env.LOCAL_AUDIO_PUBLIC_URL || "http://minio.divinci.local:9000";

// Set this flag for detecting local mode globally
export const IS_LOCAL_MODE = process.env.ENVIRONMENT === "local" || process.env.NODE_ENV === "development" || process.env.NODE_ENV === "local";

// Force the AWS SDK to use path-style URLs for S3 buckets in local mode
if (IS_LOCAL_MODE) {
  process.env.AWS_S3_FORCE_PATH_STYLE = "true";

  // Always use the reliable MinIO hostname in local mode
  // Use the reliable DNS alias for MinIO
  process.env.MINIO_ENDPOINT = "http://minio.divinci.local:9000";

  console.log("🔧 Local mode detected, using path-style URLs for S3 buckets");
  console.log(`🔧 Using S3 endpoint: ${process.env.MINIO_ENDPOINT}`);

  // Use the reliable MinIO endpoint for local mode
  const minioEndpoint = "http://minio.divinci.local:9000";

  console.log("🔧 Using reliable MinIO endpoint:", minioEndpoint);
}

// Get the audio R2 instance with the configured endpoint
export const r2 = getAudioR2Instance();
