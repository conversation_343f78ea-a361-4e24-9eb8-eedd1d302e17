import { Router } from "express";
import multer from "multer";
import { CloudflareR2Storage, R2Config, getWhitelabelVectorR2Instance } from "@divinci-ai/server-globals";

import {
  ensureAuthMiddlewareHTTP,
} from "@divinci-ai/server-globals";

import { permissionMiddleware } from "../permission";
import { WHITELABEL_ENUM } from "@divinci-ai/models";

const TEMP_FILE_BUCKET = "temp-multer-storage";

const uploadMiddleware = multer({
  storage: new CloudflareR2Storage(R2Config, getWhitelabelVectorR2Instance(), TEMP_FILE_BUCKET),
});

import {
  getOwnWhiteLabels, listUserPermitted, listGroupPermitted,
  createWhiteLabel,
  setWhiteLabelTitle,
  setWhiteLabelDescription,
  deleteWhiteLabel,
} from "./manage";

export const router = Router();
router.get("/", ensureAuthMiddlewareHTTP, getOwnWhiteLabels);
router.get("/user-permitted", ensureAuthMiddlewareHTTP, listUserPermitted);
router.get("/group-permitted", ensureAuthMiddlewareHTTP, listGroupPermitted);

router.post("/",
  ensureAuthMiddlewareHTTP,
  uploadMiddleware.single("file-data"), // Adjust this if the file field name is different
  createWhiteLabel
);

router.post(
  "/",
  ensureAuthMiddlewareHTTP,
  uploadMiddleware.single("file-data"),
  createWhiteLabel,
);


import { getWhitelabel } from "./manage";
router.get(
  "/:whitelabelId",
  getWhitelabel,
);


router.post(
  "/:whitelabelId/title",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(WHITELABEL_ENUM.editConfig),
  setWhiteLabelTitle,
);

import { setWhiteLabelPicture } from "./manage";
router.post(
  "/:whitelabelId/picture",
  permissionMiddleware(WHITELABEL_ENUM.editConfig),
  setWhiteLabelPicture
);

router.post(
  "/:whitelabelId/description",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(WHITELABEL_ENUM.editConfig),
  setWhiteLabelDescription,
);

router.delete(
  "/:whitelabelId",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(WHITELABEL_ENUM.delete),
  deleteWhiteLabel,
);

import { router as transcriptRouter } from "./transcript";
router.use("/:whitelabelId/transcript", transcriptRouter);

import { router as releaseRouter } from "./release";
router.use("/:whitelabelId/release", releaseRouter);

import { router as datasourceRouter } from "./data-source";
router.use(
  "/:whitelabelId/data-source",
  permissionMiddleware(WHITELABEL_ENUM.dataSource),
  datasourceRouter
);

import { router as fineTuneRouter } from "./fine-tuning";
router.use(
  "/:whitelabelId/fine-tune",
  permissionMiddleware(WHITELABEL_ENUM.fineTune),
  fineTuneRouter
);

import { router as promptModerationRouter } from "./prompt-moderation";
router.use(
  "/:whitelabelId/prompt-moderation",
  permissionMiddleware(WHITELABEL_ENUM.promptModeration),
  promptModerationRouter
);

import { router as threadPrefixRouter } from "./thread-prefix";
router.use(
  "/:whitelabelId/thread-prefix",
  permissionMiddleware(WHITELABEL_ENUM.useTranscript),
  threadPrefixRouter
);

import { router as messagePrefixRouter } from "./message-prefix";
router.use(
  "/:whitelabelId/message-prefix",
  permissionMiddleware(WHITELABEL_ENUM.useTranscript),
  messagePrefixRouter
);

import { router as ragVectorRouter } from "./rag-vector";
router.use(
  "/:whitelabelId/rag-vector",
  (req, res, next) => {
    // For local development with Cloudflare Worker requests, bypass permission check
    if ((process.env.ENVIRONMENT === "local" || process.env.NODE_ENV === "development" || process.env.NODE_ENV === "local") &&
        (req.headers["cloudflare-worker-x-dev-auth"] || req.headers["x-worker-local-dev"] === "true")) {
      console.log("🔑 Bypassing permission check for Cloudflare Worker request in RAG Vector router", {
        environment: process.env.ENVIRONMENT || process.env.NODE_ENV,
        hasDevAuth: !!req.headers["cloudflare-worker-x-dev-auth"],
        hasLocalDev: req.headers["x-worker-local-dev"] === "true",
        path: req.path,
        method: req.method
      });

      // Ensure we have the right permissions for the whitelabel
      if (!req.permissions) {
        req.permissions = {
          whitelabel: {
            admin: true
          }
        };
      }

      return next();
    }

    // Otherwise, use the normal permission middleware
    return permissionMiddleware(WHITELABEL_ENUM.rag)(req, res, next);
  },
  ragVectorRouter
);

import { qaPromptRouter, qaRouter } from "./qa";

router.use(
  "/:whitelabelId/qa-prompt",
  permissionMiddleware(WHITELABEL_ENUM.qualityAsurance),
  qaPromptRouter
);
router.use(
  "/:whitelabelId/qa",
  permissionMiddleware(WHITELABEL_ENUM.qualityAsurance),
  qaRouter
);


import { router as threadsRouter } from "./threads";

router.use(
  "/:whitelabelId/threads",
  permissionMiddleware(WHITELABEL_ENUM.viewTranscript),
  threadsRouter
);

import { whitelabelPermissionRouter } from "../permission";
router.use("/:whitelabelId/permission", whitelabelPermissionRouter);



import {
  createNotification,
  getNotifications,
  getNotificationById,
  getNotificationSettings,
  updateNotificationById,
  deleteNotificationSettingById,
  updateNotificationSettings,
  createNotificationSettings,
  updateNotificationDefaultSettings,
  getNotificationDefaultSettings,
} from "./notifications";

router.post(
  "/:whitelabelId/notification",
  ensureAuthMiddlewareHTTP,
  createNotification,
);

router.get(
  "/:whitelabelId/notifications",
  ensureAuthMiddlewareHTTP,
  getNotifications,
);

router.get(
  "/:whitelabelId/notifications/:id",
  ensureAuthMiddlewareHTTP,
  getNotificationById,
);

router.post(
  "/:whitelabelId/notifications-settings",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(WHITELABEL_ENUM.editConfig),
  createNotificationSettings,
);

router.put(
  "/:whitelabelId/notifications-settings/:id",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(WHITELABEL_ENUM.editConfig),
  updateNotificationSettings,
);

router.get(
  "/:whitelabelId/notifications-settings",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(WHITELABEL_ENUM.editConfig),
  getNotificationSettings,
);

router.delete(
  "/:whitelabelId/notifications-settings/:id",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(WHITELABEL_ENUM.editConfig),
  deleteNotificationSettingById,
);

router.put(
  "/:whitelabelId/notifications/:id",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(WHITELABEL_ENUM.editConfig),
  updateNotificationById,
);

router.put(
  "/:whitelabelId/notifications-settings-default/:id",
  ensureAuthMiddlewareHTTP,
  updateNotificationDefaultSettings,
);

router.get(
  "/:whitelabelId/notifications-settings-default",
  ensureAuthMiddlewareHTTP,
  getNotificationDefaultSettings,
);

import {
  getWhitelabelEmails,
  updateVerifiedEmail,
  createVerifiedEmail,
  deleteDefaultVerifiedEmail,
} from "./verified-email";

router.get(
  "/:whitelabelId/verified-emails",
  ensureAuthMiddlewareHTTP,
  getWhitelabelEmails,
);

router.post(
  "/:whitelabelId/verified-emails/:email",
  ensureAuthMiddlewareHTTP,
  createVerifiedEmail,
);

router.put(
  "/:whitelabelId/verified-emails/:id",
  ensureAuthMiddlewareHTTP,
  updateVerifiedEmail,
);

router.delete(
  "/:whitelabelId/notifications-settings-default/verified-emails/:id",
  ensureAuthMiddlewareHTTP,
  deleteDefaultVerifiedEmail,
);
