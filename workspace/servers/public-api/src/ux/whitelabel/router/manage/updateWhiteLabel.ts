import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import {
  getUserId,
  R2<PERSON><PERSON><PERSON>ointer,
  getPublicTranscriptFileR2Instance,
  R2BusBoyFileHandler,
  CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_BUCKET,
} from "@divinci-ai/server-globals";

import { WhiteLabelModel, } from "@divinci-ai/server-models";

import {
  getParam,
  jsonBody,
  HTTP_ERRORS,
  HTTP_ERRORS_WITH_CONTEXT,
  CastedBusBoy,
} from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";



export const setWhiteLabelTitle: RequestHandler = async (req, res, next)=>{
  try {
    const user_id = getUserId(req);
    const whitelableId = getParam(req, "whitelabelId");

    const { title } = castShallowObject(
      await jsonBody(req),
      { title: "string" },
      HTTP_ERRORS.BAD_FORM,
    );

    const existing = await WhiteLabelModel.findOne({
      ownerUser: user_id,
      title: title,
    });

    if(existing) {
      throw HTTP_ERRORS.BAD_FORM;
    }

    const doc = await WhiteLabelModel.findById(whitelableId);
    if(!doc) throw HTTP_ERRORS.NOT_FOUND;
    doc.title = title;
    await doc.save();

    res.statusCode = 200;
    res.json(doc);
  }catch(e){
    next(e);
  }
};

export const setWhiteLabelDescription: RequestHandler = async (req, res, next)=>{
  try {
    const whitelableId = getParam(req, "whitelabelId");

    const { description } = castShallowObject(
      await jsonBody(req),
      { description: "string" },
      HTTP_ERRORS.BAD_FORM,
    );

    const doc = await WhiteLabelModel.findById(whitelableId);
    if(!doc) throw HTTP_ERRORS.NOT_FOUND;
    doc.description = description;
    await doc.save();

    res.statusCode = 200;
    res.json(doc);
  }catch(e){
    next(e);
  }
};

const { r2 } = getPublicTranscriptFileR2Instance();
const busboyHandleBodyPicture = CastedBusBoy.create(
  { picture: "file" },
  new R2BusBoyFileHandler(
    r2,
    CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_BUCKET,
    {},
  ),
);

import { CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_PUBLIC_HOST } from "@divinci-ai/server-globals";
function pointerToUrl(pointer: R2FilePointer){
  return new URL(
    `https://${CLOUDFLARE_PUBLIC_TRANSCRIPT_FILE_ACCESS_PUBLIC_HOST}/${pointer.objectKey}`,
  ).href;
}

const VALID_WEB_IMAGE_MIME_TYPES = [
  "image/jpeg",
  "image/png",
  "image/svg+xml",
  "image/webp",
];

export const setWhiteLabelPicture: RequestHandler = async function(req, res, next){
  try {
    const whitelableId = getParam(req, "whitelabelId");

    const { picture } = await busboyHandleBodyPicture.consumeRequest(req);

    if(!VALID_WEB_IMAGE_MIME_TYPES.includes(picture.info.mimeType)){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        `Valid Image Types are ${VALID_WEB_IMAGE_MIME_TYPES.join(", ")}`,
      );
    }

    const updated = await WhiteLabelModel.findOneAndUpdate(
      { _id: whitelableId },
      { picture: pointerToUrl(picture) },
      { returnDocument: "after" },
    );
    if(updated === null){
      console.error("🙅🏻‍♂️ no update. From setWhiteLabelDescription.");
      throw HTTP_ERRORS.NOT_FOUND;
    }

    res.statusCode = 200;
    res.json(updated);
  }catch(e){
    next(e);
  }
};
