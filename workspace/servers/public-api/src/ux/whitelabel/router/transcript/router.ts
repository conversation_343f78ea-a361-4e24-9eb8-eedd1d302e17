import { WHITELABEL_ENUM } from "@divinci-ai/models";
import { ensureAuthMiddlewareHTTP } from "@divinci-ai/server-globals";
import { Router } from "express";

import { permissionMiddleware } from "../../permission";

export const router = Router({ mergeParams: true });

import { createWhitelabelTranscript } from "./routes/create-transcript";
router.post(
  "/",
  permissionMiddleware(WHITELABEL_ENUM.viewTranscript),
  createWhitelabelTranscript,
);

import { getWhitelabelTranscripts } from "./routes/get-whitelabel-transcript-list";
router.get(
  "/",
  permissionMiddleware(WHITELABEL_ENUM.viewTranscript),
  getWhitelabelTranscripts,
);


import { getMessageAssistants } from "./routes/message-assistants";
router.get("/message-assistants", getMessageAssistants);


import { getWhiteLabelTranscript } from "./routes/get-transcript";
router.get(
  "/:transcriptId",
  permissionMiddleware(WHITELABEL_ENUM.viewTranscript),
  getWhiteLabelTranscript,
);

import { deleteWhiteLabelTranscript } from "./routes/delete-transcript";
router.delete(
  "/:transcriptId",
  permissionMiddleware(WHITELABEL_ENUM.moderateTranscript),
  deleteWhiteLabelTranscript,
);

import { addMessageToTranscript } from "./routes/message/add-message";
router.post(
  "/:transcriptId/message",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(WHITELABEL_ENUM.useTranscript),
  addMessageToTranscript,
);

import { editTranscriptMessage } from "./routes/message/edit-message";
router.post(
  "/:transcriptId/message/:messageId/edit-message",
  permissionMiddleware(WHITELABEL_ENUM.moderateTranscript),
  editTranscriptMessage,
);

import { stripMessageFromTranscript } from "./routes/message/delete-message";
router.delete(
  "/:transcriptId/message",
  permissionMiddleware(WHITELABEL_ENUM.moderateTranscript),
  stripMessageFromTranscript,
);

import { addEmojiReactionToMessage } from "./routes/message/add-emoji-to-message";
router.post(
  "/:transcriptId/message/:messageId/emoji-reaction",
  ensureAuthMiddlewareHTTP,
  permissionMiddleware(WHITELABEL_ENUM.interactTranscript),
  addEmojiReactionToMessage,
);
