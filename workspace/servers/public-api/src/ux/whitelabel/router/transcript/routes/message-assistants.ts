import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { AIAssistantInfo } from "@divinci-ai/models";
import { FineTuneAIModel } from "@divinci-ai/server-models";
import { WHITE_LABEL_AI_ASSISTANT } from "@divinci-ai/server-tools";
import { getParam } from "@divinci-ai/server-utils";
import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";

export const getMessageAssistants: RequestHandler = async (req, res, next)=>{
  try {
    const whitelabelId = getParam(req, "whitelabelId");

    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION, id: whitelabelId
    });

    const info = await FineTuneAIModel.getFineTuneInfo(target);
    const assistantConfig = WHITE_LABEL_AI_ASSISTANT.getAssistantInfo();
    const merged = mergeInfos(info, assistantConfig);
    res.statusCode = 200;
    res.json(merged);
  }catch(e){
    next(e);
  }
};

type AIMessengerInfoMap = {
  [category: string]: {
      available: Array<AIAssistantInfo>,
      default?: string,
  },
};

function mergeInfos(...infoMaps: Array<AIMessengerInfoMap>): AIMessengerInfoMap{
  const messengerInfo: AIMessengerInfoMap = {};
  for(const infoMap of infoMaps){
    for(const [category, info] of Object.entries(infoMap)){
      if(!messengerInfo[category]) messengerInfo[category] = { available: [] };
      messengerInfo[category].available = messengerInfo[category].available.concat(info.available);
      if(info.default){
        messengerInfo[category].default = info.default;
      }
    }
  }
  return messengerInfo;
}
