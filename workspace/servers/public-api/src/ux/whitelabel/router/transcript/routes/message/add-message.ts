import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import {
  getUserId,
  WEB_CLIENT_IS_SECURE,
  WEB_CLIENT_HOST,
} from "@divinci-ai/server-globals";
import { WhiteLabelModel } from "@divinci-ai/server-models";
import {
  getParam,
  jsonBody,
  HTTP_ERRORS,
  HTTP_ERRORS_WITH_CONTEXT,
} from "@divinci-ai/server-utils";

import { castShallowObject, castToObject } from "@divinci-ai/utils";
import { TranscriptMessage } from "@divinci-ai/models";
import { castPromptModerationConfig } from "../../../prompt-moderation/cast-config";

function messageToURL(
  doc: InstanceType<typeof WhiteLabelModel>,
  transcriptId: string,
  message: TranscriptMessage,
){
  const url = new URL(
    `http://${WEB_CLIENT_HOST}/white-label/${doc._id}/transcript/${transcriptId}#message-${message._id}`,
  );
  if(WEB_CLIENT_IS_SECURE) url.protocol = "https:";
  return url.href;
}

export const addMessageToTranscript: RequestHandler = async (req, res, next)=>{
  try {
    const userId = getUserId(req);

    const whitelabelId = getParam(req, "whitelabelId");
    const transcriptId = getParam(req, "transcriptId");
    const [whitelabel, bodyRaw] = await Promise.all([
      WhiteLabelModel.findById(whitelabelId),
      jsonBody(req),
    ]);

    if(whitelabel === null){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    const body = castToObject(bodyRaw, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Body must be an object"));

    const {
      threadPrefix,
      messagePrefix,
      ragVectorIndex,

      assistantName,
      content,
      replyTo,
    } = castShallowObject(
      body,
      {
        assistantName: "string?",
        threadPrefix: "string?",
        messagePrefix: "string?",
        ragVectorIndex: "string?",

        content: "string",
        replyTo: "string?",
      },
      HTTP_ERRORS_WITH_CONTEXT.BAD_FORM,
    );

    const promptModeration = !body.promptModeration ? void 0 : castPromptModerationConfig(body.promptModeration);

    await whitelabel.addMessage(
      transcriptId,
      { assistantName, promptModeration, threadPrefix, messagePrefix, ragVectorIndex },
      { userId, content, replyTo },
      (whitelabel, message)=>(messageToURL(whitelabel, transcriptId, message)),
    );

    res.statusCode = 200;
    res.send({ status: "ok" });
  }catch(e){
    next(e);
  }
};
