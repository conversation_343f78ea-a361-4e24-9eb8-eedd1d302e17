import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import {
  WhiteLabelQAPromptsModel, QAEnvironment
} from "@divinci-ai/server-models";

import {
  getParam,
  jsonBody,
  HTTP_ERRORS,
  HTTP_ERRORS_WITH_CONTEXT,
} from "@divinci-ai/server-utils";
import { getUserId } from "@divinci-ai/server-globals";
import { castShallowObject, JSON_Unknown, castToObject } from "@divinci-ai/utils";

export const runQAPrompt: RequestHandler = async (req, res, next)=>{
  try {
    const userId = getUserId(req);

    const whitelabelId = getParam(req, "whitelabelId");
    const qaId = getParam(req, "qaId");

    const [qaprompts, body] = await Promise.all([
      WhiteLabelQAPromptsModel.findOne({ whitelabel: whitelabelId }),
      jsonBody(req),
    ]);

    if(qaprompts === null){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    const response = await qaprompts.runPrompt(userId, qaId, castQABody(body));

    res
      .status(200)
      .json({ responseText: response.text, context: response.context });
  }catch(e){
    next(e);
  }
};
import { castPromptModerationConfig } from "../prompt-moderation/cast-config";
function castQABody(bodyRaw: JSON_Unknown): QAEnvironment{

  const body = castToObject(bodyRaw, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Body must be an object"));

  if(typeof body.release === "string"){
    return {
      type: "release",
      value: body.release,
    };
  }

  const { assistantName, threadPrefix, messagePrefix, ragVectorIndex, } = castShallowObject(
    body,
    {
      assistantName: "string?",
      threadPrefix: "string?",
      messagePrefix: "string?",
      ragVectorIndex: "string?",
    },
    HTTP_ERRORS.BAD_FORM,
  );

  const promptModeration = castPromptModerationConfig(body.promptModeration);

  return {
    type: "config",
    value: { assistantName, threadPrefix, messagePrefix, ragVectorIndex, promptModeration },
  };
}
