import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";

import { WhiteLabelModel, FineTuneAIModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS, jsonBody } from "@divinci-ai/server-utils";

import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";
import { castShallowObject } from "@divinci-ai/utils";


export const createCustomAI: RequestHandler = async function(req, res, next){
  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const [whitelabel, body] = await Promise.all([
      WhiteLabelModel.findById(whitelabelId),
      jsonBody(req),
    ]);

    if(whitelabel === null) throw HTTP_ERRORS.NOT_FOUND;

    const { title, description, modelName } = castShallowObject(body, {
      title: "string", description: "string", modelName: "string"
    });

    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION,
      id: whitelabelId,
    });


    const doc = await FineTuneAIModel.createFineTune(
      target,
      { title, description },
      modelName,
    );

    res.statusCode = 200;
    res.json(doc);
 }catch(e){
    next(e);
  }
};
