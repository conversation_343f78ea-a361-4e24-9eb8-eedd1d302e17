import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { FineTuneAIModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS, jsonBody } from "@divinci-ai/server-utils";

import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";
import { castShallowObject } from "@divinci-ai/utils";


export const startFineTune: RequestHandler = async function(req, res, next){
  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const customAiId = getParam(req, "customAiId");
    const [fineTuneCutsomAi, body] = await Promise.all([
      FineTuneAIModel.findById(customAiId),
      jsonBody(req),
    ]);

    if(fineTuneCutsomAi === null) throw HTTP_ERRORS.NOT_FOUND;

    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION,
      id: whitelabelId,
    });

    if(fineTuneCutsomAi.target !== target) throw HTTP_ERRORS.NOT_FOUND;


    const { fileId } = castShallowObject(body, {
      fileId: "string"
    });

    const job = await fineTuneCutsomAi.startFineTune(fileId);

    res.statusCode = 200;
    res.json(job);
 }catch(e){
    next(e);
  }
};
