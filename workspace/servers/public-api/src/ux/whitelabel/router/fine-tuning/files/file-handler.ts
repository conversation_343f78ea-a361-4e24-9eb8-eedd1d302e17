import { getFineTuneR2Instance, R2Bus<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, R2<PERSON>ilePointer } from "@divinci-ai/server-globals";

import { R2Config } from "@divinci-ai/server-globals";

export { R2FilePointer };
const PRIVATE_UPLOADS_BUCKET = "private-temporary-uploads";
const R2_INSTANCE = getFineTuneR2Instance();
export const fileHandler = new R2BusBoyFileHandler(
  R2_INSTANCE, PRIVATE_UPLOADS_BUCKET, {}
);

type R2_CONFIG = {
  accountId: string;
  accessKeyId: string;
  secretAccessKey: string;
};

export function pointerToUrl(pointer: R2FilePointer){
  const config: R2_CONFIG = R2Config;
  const endpointUrl = `https://${config.accountId}.r2.cloudflarestorage.com`;
  return `${endpointUrl}/${pointer.bucket}/${pointer.objectKey}`;
}
