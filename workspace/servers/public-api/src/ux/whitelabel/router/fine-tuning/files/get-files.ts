import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { WhiteLabelModel, FineTuneFileModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS } from "@divinci-ai/server-utils";
import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";

export const getFineTuneFiles: RequestHandler = async function(req, res, next){
  try {

    const whitelabelId = getParam(req, "whitelabelId");
    const [whitelabel] = await Promise.all([
      WhiteLabelModel.findById(whitelabelId),
    ]);

    if(whitelabel === null) throw HTTP_ERRORS.NOT_FOUND;

    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION,
      id: whitelabelId,
    });

    const fileList = await FineTuneFileModel.find({ target });

    res.statusCode = 200;
    res.json(fileList);
  }catch(e){
    next(e);
  }
};
