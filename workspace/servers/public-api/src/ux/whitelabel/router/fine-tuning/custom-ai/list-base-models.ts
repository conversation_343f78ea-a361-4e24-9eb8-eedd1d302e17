import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { WhiteLabelModel, FineTuneAIModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS } from "@divinci-ai/server-utils";
import { condenseTarget, WHITE_LABEL_LOCATION, FineTuneCustomModel, AI_CATEGORY_ENUM, FineTuneableInfo } from "@divinci-ai/models";


export const listBaseModels: RequestHandler = async function(req, res, next){
  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const [whitelabel] = await Promise.all([
      WhiteLabelModel.findById(whitelabelId),
    ]);

    if(whitelabel === null) throw HTTP_ERRORS.NOT_FOUND;

    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION,
      id: whitelabelId,
    });


    const usableModels = FineTuneAIModel.getUsableModels();
    const baseModels = FineTuneAIModel.getBaseModels();
    const finetunes = await FineTuneAIModel.find({ target });
    const fineTunedModels: Array<FineTuneCustomModel> = finetunes.map((finetune)=>{
      const base = getBaseModel(finetune.modelName, baseModels);
      return {
        _id: finetune._id.toString(),
        baseModel: finetune.baseModelName,
        title: finetune.title,
        description: finetune.description,
        assistantName: finetune.modelName,
        apiAssistantName: base.apiAssistantName,

        category: base.category,
        url: base.url,
        orgUrl: base.orgUrl,
        org: base.org,
      };
    });

    res.statusCode = 200;
    res.json({ usableModels, baseModels, fineTunedModels });
 }catch(e){
    next(e);
  }
};

function getBaseModel(assistantName: string, baseModels: Array<FineTuneableInfo>) {
  const found = baseModels.find((bm)=>(bm.assistantName === assistantName));
  if(found) return found;
  return { category: AI_CATEGORY_ENUM.UNKNOWN, url: "", orgUrl: "", org: "unknown", apiAssistantName: assistantName };
}
