
import { Router } from "express";

import { FineTuneCSVConfigModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS } from "@divinci-ai/server-utils";

import {
  getWhitelabelTarget,
  updateDocBusboyHandleBody,
  R2_INSTANCE,
} from "./util";

export const router = Router({ mergeParams: true });

import { router as createRouter } from "./create";
router.use("/", createRouter);

router.get("/", async (req, res, next)=>{
  try {

    const { target } = await getWhitelabelTarget(req);

    const docs = await (
      FineTuneCSVConfigModel
      .find({ target })
      .select({ _id: 1, title: 1, headers: 1, createdAt: 1, updatedAt: 1 })
    );

    res.status(200).json(docs);

  }catch(e){
    next(e);
  }
});

router.post("/:csvId", async (req, res, next)=>{
  try {
    const csvId = getParam(req, "csvId");
    const [unCastedBody, { target }] = await Promise.all([
      updateDocBusboyHandleBody.consumeRequest(req),
      getWhitelabelTarget(req),
    ]);

    const doc = await FineTuneCSVConfigModel.findOne({ target, _id: csvId });

    if(!doc) throw HTTP_ERRORS.NOT_FOUND;

    (function(){
      try {
        doc.title = unCastedBody.title as any;
        doc.headers = JSON.parse(String(unCastedBody.headers)) as any;
        doc.headerConfig = JSON.parse(String(unCastedBody.headerConfig)) as any;
        doc.flaggerConfig = JSON.parse(String(unCastedBody.flaggerConfig)) as any;
      }catch(e){
        throw HTTP_ERRORS.BAD_FORM;
      }
    })();

    await doc.save();

    res.status(200).json(doc);
  }catch(e){
    next(e);
  }
});

import { router as updateFileRouter } from "./update/file";
import { Readable } from "node:stream";
router.use("/:csvId/file", updateFileRouter);

router.get("/:csvId", async (req, res, next)=>{
  try {
    const csvId = getParam(req, "csvId");
    const { target } = await getWhitelabelTarget(req);

    const doc = await (
      FineTuneCSVConfigModel
      .findOne({ target, _id: csvId })
      .select({ itemPointer: 0 })
    );

    if(!doc) throw HTTP_ERRORS.NOT_FOUND;

    res.status(200).json(doc);
  }catch(e){
    next(e);
  }
});

router.get("/:csvId/file", async (req, res, next)=>{
  try {
    const csvId = getParam(req, "csvId");
    const { target } = await getWhitelabelTarget(req);

    const doc = await (
      FineTuneCSVConfigModel
      .findOne({ target, _id: csvId })
      .select({ itemPointer: 1 })
    );

    if(!doc) throw HTTP_ERRORS.NOT_FOUND;

    const response = await R2_INSTANCE.getObject({
      Bucket: doc.itemPointer.bucket,
      Key: doc.itemPointer.objectKey,
    });
    if(!response.Body) throw HTTP_ERRORS.SERVER_ERROR;

    res.status(200);
    if(typeof response.ContentLength === "number"){
      res.setHeader("Content-Length", response.ContentLength);
    }
    res.setHeader("Content-Type", "application/json");
    (response.Body as Readable).pipe(res);
  }catch(e){
    next(e);
  }
});

router.delete("/:csvId", async (req, res, next)=>{
  try {
    const csvId = getParam(req, "csvId");
    const { target } = await getWhitelabelTarget(req);

    const doc = await FineTuneCSVConfigModel.deleteOne({ target, _id: csvId });

    if(!doc) throw HTTP_ERRORS.NOT_FOUND;

    res.status(200).json(doc);
  }catch(e){
    next(e);
  }

});

