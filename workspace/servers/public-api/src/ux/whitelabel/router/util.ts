import { Model } from "mongoose";  // Add this import
import { IncomingMessage } from "node:http";
import { getParam, HTTP_ERRORS } from "@divinci-ai/server-utils";
import { WhiteLabelModel } from "@divinci-ai/server-models";
import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";
import { logDebug } from "@divinci-ai/utils";
import { IWhiteLabelModelType } from "@divinci-ai/server-models/src/white-label/white-label/types";


interface WhiteLabelContext {
  whitelabelId: string,
  target: string,
  whitelabel: InstanceType<IWhiteLabelModelType>,
}

export async function getWhiteLabelContext(req: IncomingMessage): Promise<WhiteLabelContext>{
  const whitelabelId = getParam(req, "whitelabelId");
  logDebug("📛 getWhiteLabelContext whitelabelId: ", whitelabelId);

  const target = condenseTarget({
    ...WHITE_LABEL_LOCATION,
    id: whitelabelId,
  });

  logDebug("📛🎯 getWhiteLabelContext target: ", target);

  const whitelabel = await WhiteLabelModel.findById(whitelabelId);
  if(whitelabel === null) throw HTTP_ERRORS.NOT_FOUND;

  return { whitelabelId, target, whitelabel };
}

export async function getFirstItemFromRequest<T extends { target: string }, Q, M>(
  req: IncomingMessage,
  Model: Model<T, Q, M>
){
  const context = await getWhiteLabelContext(req);
  const item = await Model.findOne({ target: context.target });

  logDebug("📛🧱 getFirstItemFromRequest item: ", item);
  if(item === null) throw HTTP_ERRORS.NOT_FOUND;

  return { item, whitelabel: context.whitelabel };
}

export async function getItemFromIdRequest<T extends { target: string }, Q, M>(
  req: IncomingMessage,
  paramString: string,
  Model: Model<T, Q, M>,
){
  console.log(`🔍 [GET ITEM] Starting getItemFromIdRequest with paramString: ${paramString}`);

  try {
    console.log(`🔍 [GET ITEM] About to get WhiteLabelContext`);
    const context = await getWhiteLabelContext(req);
    console.log(`✅ [GET ITEM] Successfully got WhiteLabelContext:`, {
      whitelabelId: context.whitelabelId,
      target: context.target
    });

    console.log(`🔍 [GET ITEM] About to get param: ${paramString}`);
    const itemId = getParam(req, paramString);
    console.log(`✅ [GET ITEM] Successfully got itemId: ${itemId}`);

    console.log(`🔍 [GET ITEM] About to find item by ID: ${itemId} using model: ${Model.modelName}`);
    const item = await Model.findById(itemId);

    if(item === null) {
      console.error(`❌ [GET ITEM] Item not found with ID: ${itemId}`);
      throw HTTP_ERRORS.NOT_FOUND;
    }

    console.log(`✅ [GET ITEM] Successfully found item:`, {
      _id: item._id,
      target: item.target
    });

    if(item.target !== context.target) {
      console.error(`❌ [GET ITEM] Item target mismatch: ${item.target} !== ${context.target}`);
      throw HTTP_ERRORS.NOT_FOUND;
    }

    console.log(`✅ [GET ITEM] Item target matches context target`);
    return { item, whitelabel: context.whitelabel };
  }catch(error) {
    console.error(`❌ [GET ITEM] Error in getItemFromIdRequest:`, error);
    throw error;
  }
}

export async function getItems<T extends { target: string }, Q, M>(
  req: IncomingMessage,
  Model: Model<T, Q, M>
){
  const context = await getWhiteLabelContext(req);
  const items = await Model.find({ target: context.target });

  logDebug("📛📚 getItems items: ", items);

  return { items, whitelabel: context.whitelabel };
}
