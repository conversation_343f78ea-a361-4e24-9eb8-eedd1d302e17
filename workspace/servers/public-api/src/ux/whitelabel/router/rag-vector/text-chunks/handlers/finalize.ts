import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";

import { TextChunksConfig } from "../types";

import { DIVINCI_TEST_PROCESS_CONFIG } from "@divinci-ai/server-globals";
export function finalizeChunks(config: TextChunksConfig): RequestHandler{
  return async function (req, res, next){
    try {
      const chunks = await config.resolveTextChunks(req);

      await chunks.finalize(DIVINCI_TEST_PROCESS_CONFIG.getHeader(req.headers));

      res.statusCode = 200;
      res.json({ status: "ok" });
    }catch(e){
      next(e);
    }
  };
}
