import { Router } from "express";

export const router = Router({ mergeParams: true });

// Existing routes
import { router as createFileRouter } from "./create-file";
router.use("/", createFileRouter);

import { getRagVectorFiles } from "./get-file";
router.get("/", getRagVectorFiles);

// Add the get-record endpoint
import { getRagVectorFileRecord } from "./get-record";
router.get("/get-record", getRagVectorFileRecord);

// Add the create-record endpoint BEFORE the parameterized routes
import { createFileRecord } from "./create-file-record";
router.post("/create-record", createFileRecord);

// Parameterized routes should come after non-parameterized routes
import { getRagVectorFile, headRagVectorFile } from "./get-file";
router.get("/:fileId", getRagVectorFile);
router.head("/:fileId", headRagVectorFile);

import { deleteRagVectorFile } from "./delete-file";
router.delete("/:fileId", deleteRagVectorFile);

import { setUserInfoRagVectorFile } from "./set-info";
router.post("/:fileId/user-info", setUserInfoRagVectorFile);

import { restartRagVectorFile } from "./restart-file";
router.post("/:fileId/restart", restartRagVectorFile);

import { finalizeRagVectorFile } from "./finalize-file";
router.post("/:fileId/finalize", finalizeRagVectorFile);

import { updateRagVectorFileStatus } from "./update-status";
router.post("/:fileId/status", updateRagVectorFileStatus);

import { router as chunkRouter } from "./chunks";
router.use("/:fileId/chunks", chunkRouter);

import { addRagVectorFileWorkflow } from "./workflow";
router.post("/workflow", addRagVectorFileWorkflow);

