import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";

import { HTTP_ERRORS_WITH_CONTEXT, jsonBody } from "@divinci-ai/server-utils";
import { ensureValidFile } from "./util/ensure-valid-file";
import { castToObject, createShallowCaster } from "@divinci-ai/utils";

import { DIVINCI_TEST_PROCESS_CONFIG } from "@divinci-ai/server-globals";

const addCaster = createShallowCaster({
  chunkerTool: "string"
});

export const restartRagVectorFile: RequestHandler = async function(req, res, next){
  try {
    const [{ file }, bodyRaw] = await Promise.all([
      ensureValidFile(req),
      jsonBody(req)
    ]);

    const bodyObject = castToObject(
      bodyRaw, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Body must be an object")
    );


    const { chunkerTool } = addCaster(bodyObject);

    const config = bodyObject.chunkerConfig;

    console.log("🗳️ restartWhitelabelFile(fileId):: " + file._id);
    const { doc } = await file.rechunkFile(
      chunkerTool, DIVINCI_TEST_PROCESS_CONFIG.getHeader(req.headers)
    );

    res.statusCode = 202;
    res.json(doc);
  }catch(e){
    next(e);
  }
};
