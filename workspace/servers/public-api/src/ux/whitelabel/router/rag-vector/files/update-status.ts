import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { RagVectorFileModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS_WITH_STACK, jsonBody } from "@divinci-ai/server-utils";
import { castToObject } from "@divinci-ai/utils";
import mongoose from "mongoose";

export const updateRagVectorFileStatus: RequestHandler = async function(req, res, next){
  try {
    const fileId = getParam(req, "fileId");
    const body = await jsonBody(req);
    const reqBody = castToObject(body);
    const { status } = reqBody;

    console.log("🔍 Updating file status:", { fileId, status });

    // Create a query with proper typing
    type QueryCondition = { fileId: string } | { _id: string };
    const query: { $or: QueryCondition[] } = { $or: [{ fileId }] };

    // Only add _id to query if it could be a valid ObjectId
    if(mongoose.Types.ObjectId.isValid(fileId)) {
      query.$or.push({ _id: fileId });
    }
    
    console.log(`🔍 [UPDATE STATUS] Query conditions:`, query);

    let updated = await RagVectorFileModel.findOneAndUpdate(
      query,
      { status },
      { new: true }
    );

    // If not found, try an additional migration-period fallback
    if(!updated) {
      // Try one more search strategy - look for files with matching object keys
      console.log(`🔄 [UPDATE STATUS] File not found with primary query, trying secondary lookup strategies...`);
      
      // Find ALL files for this whiteLabelId as a last resort
      const whitelabelId = getParam(req, "whitelabelId");
      
      // First attempt: Find any file that might have rawFileKey or fileKey that contains the fileId
      // This helps when the fileId might have been generated from an object key
      let recentFiles: Array<{ _id: any; fileId?: string; rawFileKey?: string; fileKey?: string }> = [];
      
      try {
        // Make multiple attempts to find the file using different strategies
        const attempts = [
          // Attempt 1: Check if fileId is contained within fileKey or rawFileKey
          async () => {
            if (!fileId) return null;
            const keyQuery = { 
              target: { $regex: new RegExp(`^.*${whitelabelId}.*$`, 'i') },
              $or: [
                { fileKey: { $regex: fileId, $options: 'i' } },
                { rawFileKey: { $regex: fileId, $options: 'i' } }
              ]
            };
            
            console.log(`🔄 [UPDATE STATUS] Attempting key-based query:`, keyQuery);
            return await RagVectorFileModel.find(keyQuery)
              .sort({ uploadTimestamp: -1 })
              .limit(5)
              .select('_id fileId rawFileKey fileKey');
          },
          
          // Attempt 2: Just get the most recent files
          async () => {
            const recencyQuery = { 
              target: { $regex: new RegExp(`^.*${whitelabelId}.*$`, 'i') }
            };
            
            console.log(`🔄 [UPDATE STATUS] Attempting recency-based query:`, recencyQuery);
            return await RagVectorFileModel.find(recencyQuery)
              .sort({ uploadTimestamp: -1 })
              .limit(5)
              .select('_id fileId rawFileKey fileKey');
          }
        ];
        
        // Try each strategy until we find files
        for (const attemptFn of attempts) {
          const result = await attemptFn();
          if (result && result.length > 0) {
            recentFiles = result;
            console.log(`🔄 [UPDATE STATUS] Found ${recentFiles.length} files using fallback strategy`);
            break;
          }
        }
      } catch (error) {
        console.error(`❌ [UPDATE STATUS] Error in fallback queries:`, error);
      }
      
      if (recentFiles && recentFiles.length > 0) {
        console.log(`🔄 [UPDATE STATUS] Found ${recentFiles.length} recent files to check`);
        console.log(`🔄 [UPDATE STATUS] File details:`, recentFiles.map(f => ({
          _id: f._id,
          fileId: f.fileId || 'not set',
          rawFileKey: f.rawFileKey,
          fileKey: f.fileKey
        })));
        
        // Update the first one and add fileId if missing
        if (recentFiles[0]) {
          try {
            // First, display detailed debug info about the file we're updating
            console.log(`🔄 [UPDATE STATUS] Updating file:`, {
              _id: recentFiles[0]._id,
              fileId: recentFiles[0].fileId || 'not set',
              rawFileKey: recentFiles[0].rawFileKey,
              fileKey: recentFiles[0].fileKey
            });
            
            // Perform the update
            const updateResult = await RagVectorFileModel.findOneAndUpdate(
              { _id: recentFiles[0]._id },
              { 
                status,
                // Add the fileId field if it's missing for future compatibility
                ...(recentFiles[0].fileId ? {} : { fileId })
              },
              { new: true }
            );
            
            if (updateResult) {
              console.log(`✅ [UPDATE STATUS] Successfully updated file using fallback strategy:`, { 
                _id: updateResult._id,
                fileId: updateResult.fileId || 'not set',
                status: updateResult.status
              });
              updated = updateResult;
            } else {
              console.warn(`⚠️ [UPDATE STATUS] Update operation returned null result`);
            }
          } catch (updateError) {
            console.error(`❌ [UPDATE STATUS] Error updating file:`, updateError);
          }
        } else {
          console.warn(`⚠️ [UPDATE STATUS] recentFiles array has length but no first element`);
        }
      } else {
        console.warn(`⚠️ [UPDATE STATUS] No files found using fallback strategies`);
      }
    }

    if(!updated) {
      console.error("❌ File not found after all lookup attempts:", { fileId, status });
      throw new HTTP_ERRORS_WITH_STACK.NOT_FOUND(`File not found: ${fileId}`);
    }

    console.log("✅ File status updated:", { fileId, status });
    res.json({ status: "success" });
  }catch(error) {
    console.error("❌ Error updating file status:", { error });
    next(error);
  }
};
