import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { Types } from "mongoose";

import { ensureWhitelabel } from "./util/ensure-valid-file";
import { RagVectorFileModel } from "@divinci-ai/server-models";

export const getRagVectorFiles: RequestHandler = async function(req, res, next){
  try {

    const { target } = await ensureWhitelabel(req);

    const fileList = await RagVectorFileModel.aggregate([
      {
        // 1. Filter HTMLPages by host
        $match: { target }
      },
      populateVectorsofFile(target),
      {
        // 2. Project the fields you want to include (excluding "chunks")
        $project: {
          chunks: 0, // Omitting the "chunks" field
          // You can add other fields to include if necessary
        }
      }
    ]);

    res.statusCode = 200;
    res.json(fileList);
  }catch(e){
    next(e);
  }
};


export const getRagVectorFile: RequestHandler = async function(req, res, next){
  try {
    const { target } = await ensureWhitelabel(req);

    const fileId = getParam(req, "fileId");

    const fileList = await RagVectorFileModel.aggregate([
      {
        // 1. Filter HTMLPages by host
        $match: { _id: new Types.ObjectId(fileId), target }
      },
      populateVectorsofFile(target),
    ]);

    const file = fileList[0];
    if(!file){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    res.statusCode = 200;
    res.json(file);
  }catch(e){
    next(e);
  }
};

/**
 * HEAD handler for checking if a file exists
 * This is used by the chunks-workflow service to verify file existence
 */
export const headRagVectorFile: RequestHandler = async function(req, res, next){
  try {
    console.log(`🔍 HEAD request for file: ${req.params.fileId}`);
    const { target } = await ensureWhitelabel(req);
    const fileId = getParam(req, "fileId");

    // Check if the file exists using findOne instead of aggregate for better performance
    const fileExists = await RagVectorFileModel.findOne({
      _id: new Types.ObjectId(fileId),
      target
    }).select('_id').lean();

    if(!fileExists){
      console.log(`❌ File not found: ${fileId}`);
      res.status(404).end();
      return;
    }

    console.log(`✅ File exists: ${fileId}`);
    // Just return 200 OK with no body for HEAD requests
    res.status(200).end();
  }catch(e){
    if (e instanceof Error) {
      console.error(`❌ Error in headRagVectorFile: ${e.message}`);
    } else {
      console.error(`❌ Error in headRagVectorFile:`, e);
    }
    // For HEAD requests, just return appropriate status code without body
    if(e === HTTP_ERRORS.NOT_FOUND){
      res.status(404).end();
    } else {
      res.status(500).end();
    }
  }
};


import { RagVectorModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS } from "@divinci-ai/server-utils";
function populateVectorsofFile(target: string){
  return {
    $lookup: {
      from: RagVectorModel.collection.name,
      let: { fileId: { $toString: "$_id" } },
      pipeline: [
        {
          $match: {
            target,
            $expr: {
              $in: [
                // Check if the fileId is in any of the arrays
                "$$fileId",
                {
                  $concatArrays: [
                    "$files.fileId",        // array of fileIds from files
                    "$pendingFiles.fileId", // array of fileIds from pendingFiles
                    "$removingFiles.fileId" // array of fileIds from removingFiles
                  ]
                }
              ]
            }
          }
        },
        // 2) Project only the fields you need
        {
          $project: {
            _id: 1,
            title: 1,
            // any other File fields you want to include
          }
        }
      ],
      as: "ragVectors"
    }
  };
}
