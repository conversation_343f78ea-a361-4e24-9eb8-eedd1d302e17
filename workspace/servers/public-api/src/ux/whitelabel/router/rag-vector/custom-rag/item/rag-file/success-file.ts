import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { castShallowObject } from "@divinci-ai/utils";
import { jsonBody } from "@divinci-ai/server-utils";

import { RagVectorModel, updateSuccessAtomic } from "@divinci-ai/server-models";
import { getItemFromIdRequest } from "../../../../util";

export const successFileToRag: RequestHandler = async function(req, res, next){
  try {

    // Process steps sequentially to better identify where the issue might be
    console.log(`🔍 [SUCCESS FILE] About to call getItemFromIdRequest with ragId: ${req.params.ragId}`);
    try {
      const { item } = await getItemFromIdRequest(req, "ragId", RagVectorModel);
      console.log(`✅ [SUCCESS FILE] Successfully retrieved RAG item:`, {
        _id: item._id,
        target: item.target,
        title: item.title,
        files: item.files?.length || 0,
        pendingFiles: item.pendingFiles?.length || 0
      });

      // Use req.body directly instead of jsonBody
      console.log(`🔍 [SUCCESS FILE] About to parse request body`);
      const body = await jsonBody(req);
      const { fileId } = castShallowObject(body, {
        fileId: "string"
      });
      console.log(`✅ [SUCCESS FILE] Successfully parsed fileId: ${fileId}`);

      console.log(`🔍 [SUCCESS FILE] About to update success files with fileId: ${fileId}`);
      await updateSuccessAtomic(item, fileId);
      console.log(`✅ [SUCCESS FILE] Successfully updated success files`);

      res.status(200).json({ status: "ok" });
    }catch(itemError) {
      console.error(`❌ [SUCCESS FILE] Error in getItemFromIdRequest:`, itemError);
      throw itemError;
    }
  }catch(e) {
    console.error(`❌ [SUCCESS FILE] Error in successFileToRag:`, e);
    next(e);
  }
};