import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { RagVectorFileModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS_WITH_STACK, jsonBody } from "@divinci-ai/server-utils";
import { castToObject, castShallowObject } from "@divinci-ai/utils";
import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";

export const createFileRecord: RequestHandler = async function(req, res, next){
  console.log("📝 Create file record endpoint hit", {
    method: req.method,
    params: req.params,
    isLocalDev: process.env.ENVIRONMENT === "local" || process.env.NODE_ENV === "development" || process.env.NODE_ENV === "local",
    hasWorkerDevHeader: req.headers["x-worker-local-dev"] === "true",
    hasWorkerAuthHeader: !!req.headers["cloudflare-worker-x-dev-auth"]
  });

  // For local development, explicitly log auth details
  if (process.env.ENVIRONMENT === "local" || process.env.NODE_ENV === "development" || process.env.NODE_ENV === "local") {
    console.log("🔑 Auth check in file record endpoint:", {
      hasAuth: !!(req as any).auth,
      authSub: (req as any).auth?.payload?.sub || "none",
      workerDevHeader: req.headers["x-worker-local-dev"],
      workerAuthHeader: req.headers["cloudflare-worker-x-dev-auth"] ? "present" : "missing"
    });

    // For local development with workflow calls, add auth if missing
    if (!((req as any).auth) && (req.headers["x-worker-local-dev"] === "true" || req.headers["cloudflare-worker-x-dev-auth"])) {
      console.log("🔧 Adding special auth for local workflow development");
      (req as any).auth = {
        payload: {
          sub: "local-dev-workflow-auth",
          iat: Math.floor(Date.now() / 1000),
          exp: Math.floor(Date.now() / 1000) + 3600,
          iss: "https://divinci.us.auth0.com/",
          aud: "https://api.divinci.ai"
        }
      };

      // Ensure we have the right permissions for the whitelabel
      (req as any).permissions = {
        whitelabel: {
          admin: true
        }
      };
    }
  }

  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const body = await jsonBody(req);
    const reqBody = castToObject(body);

    console.log("📦 Request body received:", reqBody);

    const { bucket, objectKey, originalName, chunkingTool, title, description } = castShallowObject(
      reqBody,
      {
        bucket: "string",
        objectKey: "string",
        originalName: "string",
        chunkingTool: "string",
        title: "string",
        description: "string?", // Make description optional
      },
      new HTTP_ERRORS_WITH_STACK.BAD_FORM("Invalid JSON body"),
    );

    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION,
      id: whitelabelId,
    });

    const { doc: fileRecord } = await RagVectorFileModel.createFileRecord(
      target,
      {
        bucket,
        objectKey,
        originalName,
      },
      chunkingTool,
      {
        title,
        description: description || "" // Provide a default empty string for description
      }
    );

    console.log("✅ File record created:", fileRecord);
    res.json({ status: "success", data: fileRecord });
  }catch(error) {
    console.error("❌ Error creating file record:", error);
    next(error);
  }
};
