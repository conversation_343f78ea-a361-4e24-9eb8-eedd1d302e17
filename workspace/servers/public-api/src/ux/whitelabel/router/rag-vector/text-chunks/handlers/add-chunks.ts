import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { jsonBody } from "@divinci-ai/server-utils";
import { castToObject, castToArray, JSON_Unknown } from "@divinci-ai/utils";
import { RagVectorTextChunk } from "@divinci-ai/models";

import { TextChunksConfig } from "../types";

interface BulkChunksBody {
  chunks: Array<Omit<RagVectorTextChunk, "_id">>,
}

function validateChunksBody(body: unknown): BulkChunksBody{
  const obj = castToObject(body as JSON_Unknown);
  if(!obj.chunks){
    throw new Error("Missing chunks array");
  }

  const chunks = castToArray(obj.chunks);
  return {
    chunks: chunks.map((chunk)=>{
      const chunkObj = castToObject(chunk);
      if(typeof chunkObj.text !== "string") {
        throw new Error("Chunk text must be a string");
      }
      if(!Array.isArray(chunkObj.tags)) {
        throw new Error("Chunk tags must be an array");
      }
      if(!chunkObj.tags.every((tag)=>(typeof tag === "string"))) {
        throw new Error("All tags must be strings");
      }
      return {
        text: chunkObj.text,
        tags: chunkObj.tags as string[]
      };
    })
  };
}

export function addChunks(config: TextChunksConfig): RequestHandler{
  return async function(req, res, next){
    try {
      // First resolve the textChunks
      const textChunks = await config.resolveTextChunks(req);

      const body = await jsonBody(req);
      const validatedBody = validateChunksBody(body);

      console.log("🚀 ~ file: add-chunks.ts:45 finished: ", validatedBody.chunks.length);

      try {
        // Process chunks one by one instead of all at once
        const newChunks = [];
        for(let i = 0; i < validatedBody.chunks.length; i++) {
          console.log(`🔍 [ADD CHUNKS] Adding chunk ${i+1}/${validatedBody.chunks.length}`);
          const chunk = validatedBody.chunks[i];
          try {
            const newChunk = await textChunks.addChunk(chunk);
            newChunks.push(newChunk);
            console.log(`✅ [ADD CHUNKS] Successfully added chunk ${i+1}/${validatedBody.chunks.length}`);
          }catch(chunkError) {
            console.error(`❌ [ADD CHUNKS] Error adding chunk ${i+1}/${validatedBody.chunks.length}:`, chunkError);
            // Continue with the next chunk
          }
        }

        console.log(`✅ [ADD CHUNKS] Successfully added ${newChunks.length}/${validatedBody.chunks.length} chunks`);

        res.json({
          status: "ok",
          chunks: newChunks
        });
      }catch(processingError) {
        console.error(`❌ [ADD CHUNKS] Error processing chunks:`, processingError);
        throw processingError;
      }
    }catch(error){
      console.error(`❌ [ADD CHUNKS] Request error:`, error);
      next(error);
    }
  };
}

// This function is used for bulk adding chunks
export function addChunksBulk(config: TextChunksConfig): RequestHandler{
  return async function(req, res, next){
    try {

      console.log(`🔍 [ADD CHUNKS BULK] About to resolve textChunks`);

      // First resolve the textChunks
      const body = await jsonBody(req);
      const textChunks = await config.resolveTextChunks(body as any);

      const validatedBody = validateChunksBody(body);

      console.log("🚀 ~ file: add-chunks.ts:45 finished: ", validatedBody.chunks.length);

      try {
        // Process chunks in bulk
        console.log(`🔍 [ADD CHUNKS BULK] Adding ${validatedBody.chunks.length} chunks in bulk`);

        // Process chunks one by one instead of all at once
        const newChunks = [];
        for(let i = 0; i < validatedBody.chunks.length; i++) {
          console.log(`🔍 [ADD CHUNKS BULK] Adding chunk ${i+1}/${validatedBody.chunks.length}`);
          const chunk = validatedBody.chunks[i];
          try {
            const newChunk = await textChunks.addChunk(chunk);
            newChunks.push(newChunk);
            console.log(`✅ [ADD CHUNKS BULK] Successfully added chunk ${i+1}/${validatedBody.chunks.length}`);
          }catch(chunkError) {
            console.error(`❌ [ADD CHUNKS BULK] Error adding chunk ${i+1}/${validatedBody.chunks.length}:`, chunkError);
            // Continue with the next chunk
          }
        }

        console.log(`✅ [ADD CHUNKS BULK] Successfully added ${newChunks.length}/${validatedBody.chunks.length} chunks`);

        res.status(200).json({
          status: "ok",
          data: newChunks
        });
      }catch(processingError) {
        console.error(`❌ [ADD CHUNKS BULK] Error processing chunks:`, processingError);
        throw processingError;
      }
    }catch(error){
      console.error(`❌ [ADD CHUNKS BULK] Request error:`, error);
      next(error);
    }
  };
}