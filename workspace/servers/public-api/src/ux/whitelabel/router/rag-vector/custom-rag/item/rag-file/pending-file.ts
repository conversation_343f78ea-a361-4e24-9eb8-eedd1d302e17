import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { castShallowObject } from "@divinci-ai/utils";
import { RagVectorModel, updatePendingAtomic } from "@divinci-ai/server-models";
import { jsonBody } from "@divinci-ai/server-utils";
import { getItemFromIdRequest } from "../../../../util";

export const pendingFileToRag: RequestHandler = async function(req, res, next){
  try {
    // Process steps sequentially to better identify where the issue might be
    console.log(`🔍 [PENDING FILE] About to call getItemFromIdRequest with ragId: ${req.params.ragId}`);
    try {
      const { item } = await getItemFromIdRequest(req, "ragId", RagVectorModel);
      console.log(`✅ [PENDING FILE] Successfully retrieved RAG item:`, {
        _id: item._id,
        target: item.target,
        title: item.title,
        files: item.files?.length || 0,
        pendingFiles: item.pendingFiles?.length || 0
      });


      console.log(`🔍 [PENDING FILE] About to parse request body`);
      const body = await jsonBody(req);
      const { fileId } = castShallowObject(body, {
        fileId: "string"
      });
      console.log(`✅ [PENDING FILE] Successfully parsed fileId: ${fileId}`);

      console.log(`🔍 [PENDING FILE] About to update pending files with fileId: ${fileId}`);
      await updatePendingAtomic(item, fileId);
      console.log(`✅ [PENDING FILE] Successfully updated pending files`);

      res.status(200).json({ status: "ok" });
    }catch(itemError) {
      console.error(`❌ [PENDING FILE] Error in getItemFromIdRequest:`, itemError);
      throw itemError;
    }
  }catch(e) {
    console.error(`❌ [PENDING FILE] Error in pendingFileToRag:`, e);
    next(e);
  }
};