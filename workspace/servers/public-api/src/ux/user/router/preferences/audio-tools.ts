import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";
import { getUserById } from "@divinci-ai/server-globals";

/**
 * Get audio tool preferences for the current user
 */
export const getAudioToolPreferences: RequestHandler = async (req, res, next) => {
  try {
    // Get user ID from the authenticated request
    const userId = req.auth?.payload.sub;
    if (!userId) {
      throw HTTP_ERRORS.UNAUTHORIZED;
    }

    // Get user from Auth0
    const user = await getUserById(userId);

    // Get preferences from user metadata
    const userMetadata = user.user_metadata || {};
    const audioTools = (userMetadata as any).audioTools || {};

    const preferences = {
      diarizerTool: audioTools.diarizerTool || "",
      transcriberTool: audioTools.transcriberTool || ""
    };

    res.json(preferences);
  } catch (error) {
    next(error);
  }
};

/**
 * Update audio tool preferences for the current user
 */
export const updateAudioToolPreferences: RequestHandler = async (req, res, next) => {
  try {
    // Get user ID from the authenticated request
    const userId = req.auth?.payload.sub;
    if (!userId) {
      throw HTTP_ERRORS.UNAUTHORIZED;
    }

    // Get the request body
    const body = req.body;
    if (!body || (typeof body.diarizerTool !== 'string' && typeof body.transcriberTool !== 'string')) {
      throw HTTP_ERRORS.BAD_FORM;
    }

    // Get user from Auth0
    const user = await getUserById(userId);

    // For now, we'll just return the preferences without actually updating them
    // since we don't have direct access to update Auth0 user metadata in this endpoint

    // In a real implementation, you would use the Auth0 Management API to update user metadata
    // This would require additional setup and permissions

    // For now, we'll just return the preferences that would be set
    const userMetadata = user.user_metadata || {};
    const audioTools = (userMetadata as any).audioTools || {};

    const updatedPreferences = {
      diarizerTool: typeof body.diarizerTool === 'string' ? body.diarizerTool : audioTools.diarizerTool || "",
      transcriberTool: typeof body.transcriberTool === 'string' ? body.transcriberTool : audioTools.transcriberTool || ""
    };

    // Log that we would update the preferences
    console.log(`Would update preferences for user ${userId}:`, updatedPreferences);

    res.json(updatedPreferences);
  } catch (error) {
    next(error);
  }
};
