import { addChunks, addChunksBulk } from "../../../../../src/ux/whitelabel/router/rag-vector/text-chunks/handlers/add-chunks";
import { castToObject, castToArray } from "@divinci-ai/utils";
import { jsonBody } from "@divinci-ai/server-utils";
import { RequestHandler } from "express";
import { vi } from "vitest";
// We don't need to import RagVectorTextChunk since we're just mocking the methods

// Mock dependencies
vi.mock("@divinci-ai/utils", ()=>({
  castToObject: vi.fn(),
  castToArray: vi.fn(),
  castShallowObject: vi.fn(),
}));

// Mock jsonBody
vi.mock("@divinci-ai/server-utils", ()=>({
  jsonBody: vi.fn(),
}));

// Create a mock for the text chunks methods
const mockTextChunksMethods = {
  addChunk: vi.fn(),
  addChunks: vi.fn(),
};

describe.skip("add-chunks", ()=>{
  // Mock request, response, and next
  const mockReq = {
    params: {
      whitelabelId: "test-whitelabel-id",
      fileId: "test-file-id",
    },
    body: {},
  } as any;

  const mockRes = {
    status: vi.fn().mockReturnThis(),
    json: vi.fn(),
  } as any;

  const mockNext = vi.fn();

  // Mock config
  const mockConfig = {
    resolveTextChunks: vi.fn().mockResolvedValue({
      _id: "test-file-id",
      target: "mongodb/WhiteLabel/test-whitelabel-id",
      title: "Test File",
      chunks: [],
      ...mockTextChunksMethods
    }),
  } as any;

  beforeEach(()=>{
    vi.clearAllMocks();
    mockReq.body = {};
  });

  describe("addChunks", ()=>{
    it("should add a single chunk successfully", async ()=>{
      // Mock jsonBody to return mock request data
      (jsonBody as ReturnType<typeof vi.fn>).mockResolvedValue({
        chunks: [{
          text: "Test chunk",
          tags: ["tag1", "tag2"],
        }]
      });

      // Mock dependencies
      (castToObject as ReturnType<typeof vi.fn>).mockReturnValue({
        text: "Test chunk",
        tags: ["tag1", "tag2"],
      });

      (mockTextChunksMethods.addChunk as ReturnType<typeof vi.fn>).mockResolvedValue({
        status: "ok",
        data: {
          _id: "test-chunk-id",
          text: "Test chunk",
          tags: ["tag1", "tag2"],
        },
      });

      // Call the handler
      const handler: RequestHandler = addChunks(mockConfig);
      await Promise.resolve(handler(mockReq, mockRes, mockNext));

      // Verify results
      expect(mockConfig.resolveTextChunks).toHaveBeenCalledWith(mockReq);
      expect(castToObject).toHaveBeenCalled();
      expect(mockTextChunksMethods.addChunk).toHaveBeenCalledWith(
        expect.objectContaining({
          _id: "test-file-id",
        }),
        expect.objectContaining({
          text: "Test chunk",
          tags: ["tag1", "tag2"],
        })
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: "ok",
        data: expect.objectContaining({
          _id: "test-chunk-id",
        }),
      });
    });

    it("should handle errors", async ()=>{
      // Mock jsonBody to return mock request data
      (jsonBody as ReturnType<typeof vi.fn>).mockResolvedValue({
        chunks: [{
          text: "Test chunk",
          tags: ["tag1", "tag2"],
        }]
      });
      
      // Mock dependencies to throw an error
      (castToObject as ReturnType<typeof vi.fn>).mockImplementation(()=>{
        throw new Error("Test error");
      });

      // Call the handler
      const handler: RequestHandler = addChunks(mockConfig);
      await Promise.resolve(handler(mockReq, mockRes, mockNext));

      // Verify results
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  describe("addChunksBulk", ()=>{
    it("should add multiple chunks successfully", async ()=>{
      // Mock request body
      mockReq.body = {
        chunks: [
          { text: "Chunk 1", tags: ["tag1"] },
          { text: "Chunk 2", tags: ["tag2"] },
        ],
      };

      // Mock jsonBody to return mock request data
      (jsonBody as ReturnType<typeof vi.fn>).mockResolvedValue({
        chunks: [
          { text: "Chunk 1", tags: ["tag1"] },
          { text: "Chunk 2", tags: ["tag2"] },
        ]
      });

      // Mock dependencies
      (castToArray as ReturnType<typeof vi.fn>).mockReturnValue([
        { text: "Chunk 1", tags: ["tag1"] },
        { text: "Chunk 2", tags: ["tag2"] },
      ]);

      (mockTextChunksMethods.addChunks as ReturnType<typeof vi.fn>).mockResolvedValue({
        status: "ok",
        data: [
          { _id: "chunk-1", text: "Chunk 1", tags: ["tag1"] },
          { _id: "chunk-2", text: "Chunk 2", tags: ["tag2"] },
        ],
      });

      // Call the handler
      const handler: RequestHandler = addChunksBulk(mockConfig);
      await Promise.resolve(handler(mockReq, mockRes, mockNext));

      // Verify results
      expect(mockConfig.resolveTextChunks).toHaveBeenCalledWith(mockReq);
      expect(castToArray).toHaveBeenCalled();
      expect(mockTextChunksMethods.addChunks).toHaveBeenCalledWith(
        expect.objectContaining({
          _id: "test-file-id",
        }),
        expect.arrayContaining([
          expect.objectContaining({ text: "Chunk 1", tags: ["tag1"] }),
          expect.objectContaining({ text: "Chunk 2", tags: ["tag2"] }),
        ])
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: "ok",
        data: expect.arrayContaining([
          expect.objectContaining({ _id: "chunk-1" }),
          expect.objectContaining({ _id: "chunk-2" }),
        ]),
      });
    });

    it("should handle large number of chunks", async ()=>{
      // Create a large array of chunks
      const largeChunksArray = Array(100).fill(null).map((_, i)=>({
        text: `Chunk ${i}`,
        tags: [`tag-${i}`],
      }));

      // Mock request body
      mockReq.body = {
        chunks: largeChunksArray,
      };

      // Mock jsonBody to return mock request data
      (jsonBody as ReturnType<typeof vi.fn>).mockResolvedValue({
        chunks: largeChunksArray
      });

      // Mock dependencies
      (castToArray as ReturnType<typeof vi.fn>).mockReturnValue(largeChunksArray);

      (mockTextChunksMethods.addChunks as ReturnType<typeof vi.fn>).mockResolvedValue({
        status: "ok",
        data: largeChunksArray.map((chunk, i)=>({
          _id: `chunk-${i}`,
          ...chunk,
        })),
      });

      // Call the handler
      const handler: RequestHandler = addChunksBulk(mockConfig);
      await Promise.resolve(handler(mockReq, mockRes, mockNext));

      // Verify results
      expect(mockConfig.resolveTextChunks).toHaveBeenCalledWith(mockReq);
      expect(castToArray).toHaveBeenCalled();
      expect(mockTextChunksMethods.addChunks).toHaveBeenCalledWith(
        expect.objectContaining({
          _id: "test-file-id",
        }),
        expect.arrayContaining(largeChunksArray)
      );
      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: "ok",
        data: expect.arrayContaining(
          largeChunksArray.map((_, i)=>expect.objectContaining({ _id: `chunk-${i}` }))
        ),
      });
    });

    it("should handle errors", async ()=>{
      // Mock jsonBody to return mock request data
      (jsonBody as ReturnType<typeof vi.fn>).mockResolvedValue({
        chunks: [
          { text: "Chunk 1", tags: ["tag1"] },
          { text: "Chunk 2", tags: ["tag2"] },
        ]
      });
      
      // Mock dependencies to throw an error
      (castToArray as ReturnType<typeof vi.fn>).mockImplementation(()=>{
        throw new Error("Test error");
      });

      // Call the handler
      const handler: RequestHandler = addChunksBulk(mockConfig);
      await Promise.resolve(handler(mockReq, mockRes, mockNext));

      // Verify results
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
  });
});
