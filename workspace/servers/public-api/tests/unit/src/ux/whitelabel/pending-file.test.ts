import { pendingFileToRag } from "../../../../../src/ux/whitelabel/router/rag-vector/custom-rag/item/rag-file/pending-file";
import { castShallowObject } from "@divinci-ai/utils";
import { jsonBody } from "@divinci-ai/server-utils";
import { vi } from "vitest";
import { getItemFromIdRequest } from "../../../../../src/ux/whitelabel/router/util";

// Mock dependencies
vi.mock("@divinci-ai/utils", ()=>({
  castShallowObject: vi.fn(),
}));

vi.mock("@divinci-ai/server-models", ()=>({
  RagVectorModel: "RagVectorModel",
  updatePendingAtomic: vi.fn(),
}));

vi.mock("@divinci-ai/server-utils", ()=>({
  jsonBody: vi.fn(),
}));

vi.mock("../../../../../src/ux/whitelabel/router/util", ()=>({
  getItemFromIdRequest: vi.fn(),
}));

// Mock values for testing
const updatePendingAtomic = vi.fn();

describe.skip("pending-file", ()=>{
  // Mock request, response, and next
  const mockReq = {
    params: {
      whitelabelId: "test-whitelabel-id",
      ragId: "test-rag-id",
    },
    body: {
      fileId: "test-file-id",
    },
    url: "/white-label/test-whitelabel-id/rag-vector/test-rag-id/pending-file",
  } as any;

  const mockRes = {
    status: vi.fn().mockReturnThis(),
    json: vi.fn(),
  } as any;

  const mockNext = vi.fn();

  beforeEach(()=>{
    vi.clearAllMocks();
  });

  it("should add a file to pending successfully", async ()=>{
    // Mock dependencies
    (getItemFromIdRequest as ReturnType<typeof vi.fn>).mockResolvedValue({
      item: {
        _id: "test-rag-id",
        target: "mongodb/WhiteLabel/test-whitelabel-id",
        title: "Test RAG",
        files: [],
        pendingFiles: [],
      },
      whitelabel: {
        _id: "test-whitelabel-id",
      },
    });

    // Mock jsonBody to return the request body
    (jsonBody as ReturnType<typeof vi.fn>).mockResolvedValue({
      fileId: "test-file-id",
    });

    (castShallowObject as ReturnType<typeof vi.fn>).mockReturnValue({
      fileId: "test-file-id",
    });

    (updatePendingAtomic as ReturnType<typeof vi.fn>).mockResolvedValue({
      status: "ok",
      data: {
        _id: "test-rag-id",
        pendingFiles: ["test-file-id"],
      },
    });

    // Call the handler
    await Promise.resolve(pendingFileToRag(mockReq, mockRes, mockNext));

    // Verify results
    expect(getItemFromIdRequest).toHaveBeenCalledWith(mockReq, "ragId", RagVectorModel);
    expect(castShallowObject).toHaveBeenCalledWith(mockReq.body, {
      fileId: "string",
    });
    expect(updatePendingAtomic).toHaveBeenCalledWith(
      expect.objectContaining({
        _id: "test-rag-id",
      }),
      "test-file-id"
    );
    expect(mockRes.status).toHaveBeenCalledWith(200);
    expect(mockRes.json).toHaveBeenCalledWith({ status: "ok" });
  });

  it("should handle errors from getItemFromIdRequest", async ()=>{
    // Mock dependencies to throw an error
    (getItemFromIdRequest as ReturnType<typeof vi.fn>).mockRejectedValue(new Error("Test error"));

    // Call the handler
    await Promise.resolve(pendingFileToRag(mockReq, mockRes, mockNext));

    // Verify results
    expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    expect(updatePendingAtomic).not.toHaveBeenCalled();
  });

  it("should handle errors from castShallowObject", async ()=>{
    // Mock dependencies
    (getItemFromIdRequest as ReturnType<typeof vi.fn>).mockResolvedValue({
      item: {
        _id: "test-rag-id",
        target: "mongodb/WhiteLabel/test-whitelabel-id",
        title: "Test RAG",
        files: [],
        pendingFiles: [],
      },
      whitelabel: {
        _id: "test-whitelabel-id",
      },
    });

    // Mock jsonBody to return the request body
    (jsonBody as ReturnType<typeof vi.fn>).mockResolvedValue({
      fileId: "test-file-id",
    });

    // Mock castShallowObject to throw an error
    (castShallowObject as ReturnType<typeof vi.fn>).mockImplementation(()=>{
      throw new Error("Test error");
    });

    // Call the handler
    await Promise.resolve(pendingFileToRag(mockReq, mockRes, mockNext));

    // Verify results
    expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    expect(updatePendingAtomic).not.toHaveBeenCalled();
  });

  it("should handle errors from updatePendingAtomic", async ()=>{
    // Mock dependencies
    (getItemFromIdRequest as ReturnType<typeof vi.fn>).mockResolvedValue({
      item: {
        _id: "test-rag-id",
        target: "mongodb/WhiteLabel/test-whitelabel-id",
        title: "Test RAG",
        files: [],
        pendingFiles: [],
      },
      whitelabel: {
        _id: "test-whitelabel-id",
      },
    });

    // Mock jsonBody to return the request body
    (jsonBody as ReturnType<typeof vi.fn>).mockResolvedValue({
      fileId: "test-file-id",
    });

    (castShallowObject as ReturnType<typeof vi.fn>).mockReturnValue({
      fileId: "test-file-id",
    });

    // Mock updatePendingAtomic to throw an error
    (updatePendingAtomic as ReturnType<typeof vi.fn>).mockRejectedValue(new Error("Test error"));

    // Call the handler
    await Promise.resolve(pendingFileToRag(mockReq, mockRes, mockNext));

    // Verify results
    expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
  });
});
