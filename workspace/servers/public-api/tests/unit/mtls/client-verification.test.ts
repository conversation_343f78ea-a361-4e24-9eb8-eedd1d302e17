import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { IncomingMessage, ServerResponse } from 'http';
import { TLSSocket } from 'tls';

// Mock the TLSSocket
vi.mock('tls', () => ({
  TLSSocket: vi.fn(),
}));

// Extract the client certificate verification logic into a testable function
// This is based on the existing code in app.ts
function verifyClientCertificate(req: IncomingMessage, res: ServerResponse) {
  // For mTLS, we can check the client certificate if verification is enabled
  if (process.env.ENABLE_MTLS === '1') {
    const socket = req.socket as TLSSocket;
    
    // Check if the socket has mTLS properties
    if (!socket || typeof socket.authorized === 'undefined') {
      console.log('⚠️ Socket does not have mTLS properties');
      return { success: false, reason: 'Socket does not have mTLS properties' };
    }
    
    // Check if the socket has requestCert and rejectUnauthorized enabled
    if ((socket as any).requestCert === true && (socket as any).rejectUnauthorized === true) {
      // Check if the client certificate is authorized
      if (socket.authorized === false) {
        console.log('❌ Unauthorized mTLS connection attempt');
        res.writeHead(403);
        res.end('Forbidden - client certificate required');
        return { success: false, reason: 'Unauthorized mTLS connection attempt' };
      }
      
      // Get client certificate information
      if (typeof (socket as any).getPeerCertificate === 'function') {
        const cert = (socket as any).getPeerCertificate();
        if (cert && Object.keys(cert).length > 0 && !(cert as any).issuer === undefined) {
          console.log('✅ Valid client certificate provided');
          return { 
            success: true, 
            certificate: cert,
            authorized: true,
          };
        } else {
          console.log('❌ No client certificate provided');
          res.writeHead(403);
          res.end('Forbidden - client certificate required');
          return { success: false, reason: 'No client certificate provided' };
        }
      } else {
        console.log('⚠️ Socket does not support getPeerCertificate');
        return { success: false, reason: 'Socket does not support getPeerCertificate' };
      }
    } else {
      console.log('⚠️ mTLS verification not enabled on socket');
      return { success: true, authorized: false, reason: 'mTLS verification not enabled on socket' };
    }
  } else {
    console.log('⚠️ mTLS is disabled');
    return { success: true, authorized: false, reason: 'mTLS is disabled' };
  }
}

describe('Client Certificate Verification', () => {
  // Save original environment variables
  const originalEnv = { ...process.env };
  
  // Mock request and response objects
  let mockReq: IncomingMessage;
  let mockRes: ServerResponse;
  let mockSocket: any;
  
  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();
    
    // Reset environment variables
    process.env = { ...originalEnv };
    
    // Create mock socket
    mockSocket = {
      authorized: false,
      requestCert: false,
      rejectUnauthorized: false,
      getPeerCertificate: vi.fn(),
      authorizationError: null,
    };
    
    // Create mock request
    mockReq = {
      socket: mockSocket,
    } as unknown as IncomingMessage;
    
    // Create mock response
    mockRes = {
      writeHead: vi.fn(),
      end: vi.fn(),
    } as unknown as ServerResponse;
    
    // Mock console methods to prevent noise in test output
    vi.spyOn(console, 'log').mockImplementation(() => {});
  });
  
  afterEach(() => {
    // Restore environment variables
    process.env = { ...originalEnv };
    
    // Restore console methods
    vi.restoreAllMocks();
  });
  
  describe('mTLS enablement', () => {
    it('should skip verification when mTLS is disabled', () => {
      process.env.ENABLE_MTLS = '0';
      
      const result = verifyClientCertificate(mockReq, mockRes);
      
      expect(result.success).toBe(true);
      expect(result.authorized).toBe(false);
      expect(result.reason).toBe('mTLS is disabled');
      expect(mockRes.writeHead).not.toHaveBeenCalled();
      expect(mockRes.end).not.toHaveBeenCalled();
    });
    
    it('should proceed with verification when mTLS is enabled', () => {
      process.env.ENABLE_MTLS = '1';
      mockSocket.requestCert = true;
      mockSocket.rejectUnauthorized = true;
      mockSocket.authorized = true;
      mockSocket.getPeerCertificate.mockReturnValue({
        subject: { CN: 'client.example.com' },
        issuer: { CN: 'Example CA' },
        valid_from: '2023-01-01T00:00:00Z',
        valid_to: '2024-01-01T00:00:00Z',
        fingerprint: '12:34:56:78:9A:BC:DE:F0',
      });
      
      const result = verifyClientCertificate(mockReq, mockRes);
      
      expect(result.success).toBe(true);
      expect(result.authorized).toBe(true);
      expect(result.certificate).toBeDefined();
      expect(mockRes.writeHead).not.toHaveBeenCalled();
      expect(mockRes.end).not.toHaveBeenCalled();
    });
  });
  
  describe('Socket verification', () => {
    beforeEach(() => {
      process.env.ENABLE_MTLS = '1';
    });
    
    it('should fail when socket does not have mTLS properties', () => {
      // Set socket to null
      mockReq.socket = null as any;
      
      const result = verifyClientCertificate(mockReq, mockRes);
      
      expect(result.success).toBe(false);
      expect(result.reason).toBe('Socket does not have mTLS properties');
      expect(mockRes.writeHead).not.toHaveBeenCalled();
      expect(mockRes.end).not.toHaveBeenCalled();
    });
    
    it('should skip verification when requestCert is not enabled', () => {
      mockSocket.requestCert = false;
      mockSocket.rejectUnauthorized = true;
      
      const result = verifyClientCertificate(mockReq, mockRes);
      
      expect(result.success).toBe(true);
      expect(result.authorized).toBe(false);
      expect(result.reason).toBe('mTLS verification not enabled on socket');
      expect(mockRes.writeHead).not.toHaveBeenCalled();
      expect(mockRes.end).not.toHaveBeenCalled();
    });
    
    it('should skip verification when rejectUnauthorized is not enabled', () => {
      mockSocket.requestCert = true;
      mockSocket.rejectUnauthorized = false;
      
      const result = verifyClientCertificate(mockReq, mockRes);
      
      expect(result.success).toBe(true);
      expect(result.authorized).toBe(false);
      expect(result.reason).toBe('mTLS verification not enabled on socket');
      expect(mockRes.writeHead).not.toHaveBeenCalled();
      expect(mockRes.end).not.toHaveBeenCalled();
    });
    
    it('should fail when socket does not support getPeerCertificate', () => {
      mockSocket.requestCert = true;
      mockSocket.rejectUnauthorized = true;
      mockSocket.getPeerCertificate = undefined;
      
      const result = verifyClientCertificate(mockReq, mockRes);
      
      expect(result.success).toBe(false);
      expect(result.reason).toBe('Socket does not support getPeerCertificate');
      expect(mockRes.writeHead).not.toHaveBeenCalled();
      expect(mockRes.end).not.toHaveBeenCalled();
    });
  });
  
  describe('Client certificate validation', () => {
    beforeEach(() => {
      process.env.ENABLE_MTLS = '1';
      mockSocket.requestCert = true;
      mockSocket.rejectUnauthorized = true;
    });
    
    it('should accept valid client certificate', () => {
      mockSocket.authorized = true;
      mockSocket.getPeerCertificate.mockReturnValue({
        subject: { CN: 'client.example.com' },
        issuer: { CN: 'Example CA' },
        valid_from: '2023-01-01T00:00:00Z',
        valid_to: '2024-01-01T00:00:00Z',
        fingerprint: '12:34:56:78:9A:BC:DE:F0',
      });
      
      const result = verifyClientCertificate(mockReq, mockRes);
      
      expect(result.success).toBe(true);
      expect(result.authorized).toBe(true);
      expect(result.certificate).toBeDefined();
      expect(result.certificate.subject.CN).toBe('client.example.com');
      expect(mockRes.writeHead).not.toHaveBeenCalled();
      expect(mockRes.end).not.toHaveBeenCalled();
    });
    
    it('should reject unauthorized client certificate', () => {
      mockSocket.authorized = false;
      mockSocket.authorizationError = 'certificate has expired';
      
      const result = verifyClientCertificate(mockReq, mockRes);
      
      expect(result.success).toBe(false);
      expect(result.reason).toBe('Unauthorized mTLS connection attempt');
      expect(mockRes.writeHead).toHaveBeenCalledWith(403);
      expect(mockRes.end).toHaveBeenCalledWith('Forbidden - client certificate required');
    });
    
    it('should reject missing client certificate', () => {
      mockSocket.authorized = false;
      mockSocket.getPeerCertificate.mockReturnValue({});
      
      const result = verifyClientCertificate(mockReq, mockRes);
      
      expect(result.success).toBe(false);
      expect(result.reason).toBe('No client certificate provided');
      expect(mockRes.writeHead).toHaveBeenCalledWith(403);
      expect(mockRes.end).toHaveBeenCalledWith('Forbidden - client certificate required');
    });
    
    it('should reject invalid client certificate', () => {
      mockSocket.authorized = false;
      mockSocket.getPeerCertificate.mockReturnValue({
        subject: { CN: 'client.example.com' },
        issuer: undefined,
      });
      
      const result = verifyClientCertificate(mockReq, mockRes);
      
      expect(result.success).toBe(false);
      expect(result.reason).toBe('No client certificate provided');
      expect(mockRes.writeHead).toHaveBeenCalledWith(403);
      expect(mockRes.end).toHaveBeenCalledWith('Forbidden - client certificate required');
    });
  });
});
