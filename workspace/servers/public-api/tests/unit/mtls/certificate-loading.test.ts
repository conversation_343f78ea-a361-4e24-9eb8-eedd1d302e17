import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';

// Mock the fs module
vi.mock('fs', () => ({
  existsSync: vi.fn(),
  readFileSync: vi.fn(),
  mkdirSync: vi.fn(),
  writeFileSync: vi.fn(),
}));

// Mock the path module
vi.mock('path', () => ({
  join: vi.fn((...args) => args.join('/')),
}));

// Extract the certificate loading logic into a testable function
// This is based on the existing code in app.ts
function loadCertificates(options: {
  enableMtls?: boolean;
  certDir?: string;
  customClientCertPath?: string;
}) {
  const {
    enableMtls = process.env.ENABLE_MTLS === '1',
    certDir = process.env.MTLS_CERT_DIR || '/private-keys/local/certs/mtls',
    customClientCertPath = process.env.MTLS_CLIENT_CERT_PATH,
  } = options;

  if (!enableMtls) {
    return { success: false, reason: 'mTLS is disabled' };
  }

  // Try to load server certificate and key
  try {
    // First try direct paths for GCP Cloud Run with mounted secrets
    let certPath = path.join(certDir, 'server.crt');
    let keyPath = path.join(certDir, 'server.key');

    // If direct paths don't exist, try the nested structure for local development
    if (!fs.existsSync(certPath)) {
      certPath = path.join(certDir, 'certs', 'server.crt');
    }

    if (!fs.existsSync(keyPath)) {
      keyPath = path.join(certDir, 'private', 'server.key');
    }

    // Check if certificate files exist
    if (!fs.existsSync(certPath)) {
      return { success: false, reason: `Server certificate not found at ${certPath}` };
    }

    if (!fs.existsSync(keyPath)) {
      return { success: false, reason: `Server key not found at ${keyPath}` };
    }

    // Read certificate files
    const cert = fs.readFileSync(certPath, 'utf8');
    const key = fs.readFileSync(keyPath, 'utf8');

    // Validate certificate format
    if (!cert.includes('-----BEGIN CERTIFICATE-----')) {
      return { success: false, reason: 'Server certificate is not in PEM format' };
    }

    if (!key.includes('-----BEGIN PRIVATE KEY-----') && !key.includes('-----BEGIN RSA PRIVATE KEY-----') && !key.includes('-----BEGIN EC PRIVATE KEY-----')) {
      return { success: false, reason: 'Server key is not in PEM format' };
    }

    // Try to load client certificate CA for verification
    const possibleClientCertPaths = [
      // New standard path
      '/etc/ssl/client/client.crt',
      // Legacy paths for backward compatibility
      '/etc/ssl/certs/client.crt',
      // Environment variable path
      customClientCertPath,
      // Local development paths
      path.join(certDir, 'client.crt'),
      path.join(certDir, 'certs', 'client.crt'),
      path.join('/private-keys/local/certs/mtls', 'client.crt')
    ].filter(Boolean); // Remove undefined/null values

    let clientCertFound = false;
    let clientCert = '';

    for (const clientCertPath of possibleClientCertPaths) {
      if (!clientCertPath) continue;

      if (fs.existsSync(clientCertPath)) {
        try {
          clientCert = fs.readFileSync(clientCertPath, 'utf8');

          // Validate client certificate format
          if (!clientCert.includes('-----BEGIN CERTIFICATE-----')) {
            continue; // Skip invalid certificate
          }

          clientCertFound = true;
          break;
        } catch (error) {
          // Continue to next path on error
        }
      }
    }

    if (!clientCertFound) {
      return {
        success: true,
        serverCert: cert,
        serverKey: key,
        clientCert: null,
        reason: 'Client certificate not found, mTLS will not require client certificates'
      };
    }

    return {
      success: true,
      serverCert: cert,
      serverKey: key,
      clientCert,
    };
  } catch (error) {
    return {
      success: false,
      reason: `Error loading certificates: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

describe('Certificate Loading', () => {
  // Save original environment variables
  const originalEnv = { ...process.env };

  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();

    // Reset environment variables
    process.env = { ...originalEnv };

    // Default mock for path.join to behave like the real function
    vi.mocked(path.join).mockImplementation((...args) => args.join('/'));
  });

  afterEach(() => {
    // Restore environment variables
    process.env = { ...originalEnv };
  });

  describe('mTLS enablement', () => {
    it('should return failure when mTLS is disabled', () => {
      process.env.ENABLE_MTLS = '0';

      const result = loadCertificates({});

      expect(result.success).toBe(false);
      expect(result.reason).toBe('mTLS is disabled');
    });

    it('should proceed when mTLS is enabled', () => {
      process.env.ENABLE_MTLS = '1';
      // Clear any existing cert dir environment variable
      delete process.env.MTLS_CERT_DIR;

      // Mock certificate files to exist - use the actual paths that will be generated
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        // Check for the actual paths that will be generated by the function
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt') ||
            path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt')) {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });

      const result = loadCertificates({});

      expect(result.success).toBe(true);
      expect(result.serverCert).toBeDefined();
      expect(result.serverKey).toBeDefined();
    });
  });

  describe('Server certificate loading', () => {
    beforeEach(() => {
      process.env.ENABLE_MTLS = '1';
      delete process.env.MTLS_CERT_DIR;
    });

    it('should load server certificate from direct path', () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt') ||
            path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt')) {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });

      const result = loadCertificates({});

      expect(result.success).toBe(true);
      expect(result.serverCert).toContain('-----BEGIN CERTIFICATE-----');
      expect(result.serverKey).toContain('-----BEGIN PRIVATE KEY-----');
    });

    it('should load server certificate from nested path', () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt')) {
          return false;
        }
        if (path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return false;
        }
        if (path.endsWith('/private-keys/local/certs/mtls/certs/server.crt') ||
            path.endsWith('/private-keys/local/certs/mtls/private/server.key')) {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/certs/server.crt')) {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path.endsWith('/private-keys/local/certs/mtls/private/server.key')) {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });

      const result = loadCertificates({});

      expect(result.success).toBe(true);
      expect(result.serverCert).toContain('-----BEGIN CERTIFICATE-----');
      expect(result.serverKey).toContain('-----BEGIN PRIVATE KEY-----');
    });

    it('should fail when server certificate is not found', () => {
      // Mock certificate files to not exist
      vi.mocked(fs.existsSync).mockReturnValue(false);

      const result = loadCertificates({});

      expect(result.success).toBe(false);
      expect(result.reason).toContain('Server certificate not found');
    });

    it('should fail when server key is not found', () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt') ||
            path.endsWith('/private-keys/local/certs/mtls/certs/server.crt')) {
          return true;
        }
        if (path.endsWith('/private-keys/local/certs/mtls/server.key') ||
            path.endsWith('/private-keys/local/certs/mtls/private/server.key')) {
          return false;
        }
        return false;
      });

      const result = loadCertificates({});

      expect(result.success).toBe(false);
      expect(result.reason).toContain('Server key not found');
    });

    it('should fail when server certificate is not in PEM format', () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt') ||
            path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt')) {
          return 'INVALID CERTIFICATE FORMAT';
        }
        if (path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });

      const result = loadCertificates({});

      expect(result.success).toBe(false);
      expect(result.reason).toBe('Server certificate is not in PEM format');
    });

    it('should fail when server key is not in PEM format', () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt') ||
            path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt')) {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return 'INVALID KEY FORMAT';
        }
        return '';
      });

      const result = loadCertificates({});

      expect(result.success).toBe(false);
      expect(result.reason).toBe('Server key is not in PEM format');
    });
  });

  describe('Client certificate loading', () => {
    beforeEach(() => {
      process.env.ENABLE_MTLS = '1';
      delete process.env.MTLS_CERT_DIR;

      // Mock server certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt') ||
            path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return true;
        }
        return false;
      });

      // Mock server certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt')) {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });
    });

    it('should load client certificate from standard path', () => {
      // Mock client certificate to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt') ||
            path.endsWith('/private-keys/local/certs/mtls/server.key') ||
            path === '/etc/ssl/client/client.crt') {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt')) {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        if (path === '/etc/ssl/client/client.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        return '';
      });

      const result = loadCertificates({});

      expect(result.success).toBe(true);
      expect(result.clientCert).toContain('-----BEGIN CERTIFICATE-----');
    });

    it('should load client certificate from legacy path', () => {
      // Mock client certificate to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt') ||
            path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return true;
        }
        if (path === '/etc/ssl/client/client.crt') {
          return false;
        }
        if (path === '/etc/ssl/certs/client.crt') {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt')) {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        if (path === '/etc/ssl/certs/client.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        return '';
      });

      const result = loadCertificates({});

      expect(result.success).toBe(true);
      expect(result.clientCert).toContain('-----BEGIN CERTIFICATE-----');
    });

    it('should load client certificate from environment variable path', () => {
      process.env.MTLS_CLIENT_CERT_PATH = '/custom/path/client.crt';

      // Mock client certificate to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt') ||
            path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return true;
        }
        if (path === '/etc/ssl/client/client.crt' ||
            path === '/etc/ssl/certs/client.crt') {
          return false;
        }
        if (path === '/custom/path/client.crt') {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt')) {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        if (path === '/custom/path/client.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        return '';
      });

      const result = loadCertificates({});

      expect(result.success).toBe(true);
      expect(result.clientCert).toContain('-----BEGIN CERTIFICATE-----');
    });

    it('should load client certificate from local development path', () => {
      // Mock client certificate to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt') ||
            path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return true;
        }
        if (path === '/etc/ssl/client/client.crt' ||
            path === '/etc/ssl/certs/client.crt') {
          return false;
        }
        if (path.endsWith('/private-keys/local/certs/mtls/client.crt')) {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt')) {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        if (path.endsWith('/private-keys/local/certs/mtls/client.crt')) {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        return '';
      });

      const result = loadCertificates({});

      expect(result.success).toBe(true);
      expect(result.clientCert).toContain('-----BEGIN CERTIFICATE-----');
    });

    it('should succeed without client certificate', () => {
      // Mock client certificate to not exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt') ||
            path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return true;
        }
        return false;
      });

      const result = loadCertificates({});

      expect(result.success).toBe(true);
      expect(result.clientCert).toBeNull();
      expect(result.reason).toContain('Client certificate not found');
    });

    it('should skip invalid client certificate', () => {
      // Mock client certificate to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt') ||
            path.endsWith('/private-keys/local/certs/mtls/server.key') ||
            path === '/etc/ssl/client/client.crt') {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt')) {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        if (path === '/etc/ssl/client/client.crt') {
          return 'INVALID CLIENT CERTIFICATE';
        }
        return '';
      });

      const result = loadCertificates({});

      expect(result.success).toBe(true);
      expect(result.clientCert).toBeNull();
      expect(result.reason).toContain('Client certificate not found');
    });
  });

  describe('Custom certificate directory', () => {
    beforeEach(() => {
      process.env.ENABLE_MTLS = '1';
    });

    it('should use custom certificate directory from environment variable', () => {
      process.env.MTLS_CERT_DIR = '/custom/cert/dir';

      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/custom/cert/dir/server.crt' ||
            path === '/custom/cert/dir/server.key') {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/custom/cert/dir/server.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/custom/cert/dir/server.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });

      const result = loadCertificates({});

      expect(result.success).toBe(true);
      expect(vi.mocked(fs.existsSync)).toHaveBeenCalledWith('/custom/cert/dir/server.crt');
      expect(vi.mocked(fs.existsSync)).toHaveBeenCalledWith('/custom/cert/dir/server.key');
    });

    it('should use custom certificate directory from function parameter', () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/param/cert/dir/server.crt' ||
            path === '/param/cert/dir/server.key') {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/param/cert/dir/server.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/param/cert/dir/server.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });

      const result = loadCertificates({ certDir: '/param/cert/dir' });

      expect(result.success).toBe(true);
      expect(vi.mocked(fs.existsSync)).toHaveBeenCalledWith('/param/cert/dir/server.crt');
      expect(vi.mocked(fs.existsSync)).toHaveBeenCalledWith('/param/cert/dir/server.key');
    });
  });

  describe('Error handling', () => {
    beforeEach(() => {
      process.env.ENABLE_MTLS = '1';
      delete process.env.MTLS_CERT_DIR;
    });

    it('should handle file read errors', () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path.endsWith('/private-keys/local/certs/mtls/server.crt') ||
            path.endsWith('/private-keys/local/certs/mtls/server.key')) {
          return true;
        }
        return false;
      });

      // Mock readFileSync to throw an error
      vi.mocked(fs.readFileSync).mockImplementation(() => {
        throw new Error('Permission denied');
      });

      const result = loadCertificates({});

      expect(result.success).toBe(false);
      expect(result.reason).toContain('Error loading certificates');
      expect(result.reason).toContain('Permission denied');
    });
  });
});
