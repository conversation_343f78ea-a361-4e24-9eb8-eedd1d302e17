import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock the fs module
const fs = vi.hoisted(() => ({
  existsSync: vi.fn(),
  readFileSync: vi.fn(),
}));

// Mock the https module
const https = vi.hoisted(() => ({
  Agent: vi.fn(),
}));

// Mock the crypto module
const crypto = vi.hoisted(() => ({
  constants: {
    SSL_OP_NO_TLSv1_3: 0x20000000,
  },
}));

vi.mock('fs', () => fs);
vi.mock('https', () => https);
vi.mock('crypto', () => crypto);

// Extract the client-side mTLS logic into a testable function
// This is based on the existing code in api.ts
async function setupMtlsAgent(options: {
  enableMtls?: boolean;
  isSecure?: boolean;
  customClientCertPath?: string;
  customClientKeyPath?: string;
}) {
  const {
    enableMtls = process.env.API_MTLS_ENABLED === 'true',
    isSecure = process.env.API_IS_SECURE === 'true',
    customClientCertPath = process.env.MTLS_CLIENT_CERT_PATH,
    customClientKeyPath = process.env.MTLS_CLIENT_KEY_PATH,
  } = options;

  if (!enableMtls || !isSecure) {
    return { success: false, reason: 'mTLS is disabled or API is not secure' };
  }

  try {
    // Use the mocked modules
    console.log("Using mocked Node.js modules");
    const { constants } = crypto;
    console.log("Successfully using Node.js modules");

    // Define possible certificate paths
    const certPaths = [
      "/etc/ssl/client/client.crt",
      "/etc/ssl/certs/client.crt",
      customClientCertPath,
      "/private-keys/local/certs/mtls/client.crt",
      "/private-keys/staging/certs/mtls/client.crt"
    ].filter(Boolean);

    const keyPaths = [
      "/etc/ssl/private/client.key",
      customClientKeyPath,
      "/private-keys/local/certs/mtls/client.key",
      "/private-keys/staging/certs/mtls/client.key"
    ].filter(Boolean);

    console.log("Searching for client certificates in these paths:", certPaths);
    console.log("Searching for client keys in these paths:", keyPaths);

    // Find valid certificate and key
    let certPath = null;
    let keyPath = null;

    // Check each certificate path
    for (const path of certPaths) {
      if (path && fs.existsSync(path)) {
        console.log(`Found client certificate at: ${path}`);
        try {
          // Verify the certificate is readable and in PEM format
          const certContent = fs.readFileSync(path, "utf8");
          if (certContent.includes("-----BEGIN CERTIFICATE-----")) {
            certPath = path;
            console.log(`Verified client certificate at ${path} is in PEM format`);
            break;
          } else {
            console.log(`Certificate at ${path} is not in PEM format, skipping`);
          }
        } catch (err) {
          console.log(`Error reading certificate at ${path}:`, err);
        }
      } else if (path) {
        console.log(`Certificate path ${path} does not exist`);
      }
    }

    // Check each key path
    for (const path of keyPaths) {
      if (path && fs.existsSync(path)) {
        console.log(`Found client key at: ${path}`);
        try {
          // Verify the key is readable and in PEM format
          const keyContent = fs.readFileSync(path, "utf8");
          if (keyContent.includes("-----BEGIN PRIVATE KEY-----") ||
              keyContent.includes("-----BEGIN RSA PRIVATE KEY-----") ||
              keyContent.includes("-----BEGIN EC PRIVATE KEY-----")) {
            keyPath = path;
            console.log(`Verified client key at ${path} is in PEM format`);
            break;
          } else {
            console.log(`Key at ${path} is not in PEM format, skipping`);
          }
        } catch (err) {
          console.log(`Error reading key at ${path}:`, err);
        }
      } else if (path) {
        console.log(`Key path ${path} does not exist`);
      }
    }

    // Configure HTTPS agent if certificates are found
    if (certPath && keyPath) {
      console.log(`Using mTLS certificates: ${certPath}, ${keyPath}`);

      try {
        // Read certificate and key content
        const certContent = fs.readFileSync(certPath, "utf8");
        const keyContent = fs.readFileSync(keyPath, "utf8");

        // Log certificate details (first few characters only for security)
        console.log(`Certificate content starts with: ${certContent.substring(0, 40)}...`);
        console.log(`Key content starts with: ${keyContent.substring(0, 40)}...`);

        // Create HTTPS agent with mTLS certificates
        const agent = new https.Agent({
          cert: certContent,
          key: keyContent,
          rejectUnauthorized: true,
          secureOptions: constants.SSL_OP_NO_TLSv1_3
        });

        return {
          success: true,
          agent,
          certPath,
          keyPath,
        };
      } catch (error) {
        return {
          success: false,
          reason: `Error creating HTTPS agent: ${error instanceof Error ? error.message : String(error)}`
        };
      }
    } else {
      return {
        success: false,
        reason: certPath ? 'Client key not found' : 'Client certificate not found'
      };
    }
  } catch (error) {
    return {
      success: false,
      reason: `Error setting up mTLS: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

describe('Client-Side mTLS', () => {
  // Save original environment variables
  const originalEnv = { ...process.env };

  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();

    // Reset environment variables
    process.env = { ...originalEnv };

    // Mock console methods to prevent noise in test output
    vi.spyOn(console, 'log').mockImplementation(() => {});

    // Mock https.Agent
    vi.mocked(https.Agent).mockImplementation((options) => {
      return { options } as any;
    });
  });

  afterEach(() => {
    // Restore environment variables
    process.env = { ...originalEnv };

    // Restore console methods
    vi.restoreAllMocks();
  });

  describe('mTLS enablement', () => {
    it('should not create agent when mTLS is disabled', async () => {
      process.env.API_MTLS_ENABLED = 'false';
      process.env.API_IS_SECURE = 'true';

      const result = await setupMtlsAgent({});

      expect(result.success).toBe(false);
      expect(result.reason).toBe('mTLS is disabled or API is not secure');
      expect(https.Agent).not.toHaveBeenCalled();
    });

    it('should not create agent when API is not secure', async () => {
      process.env.API_MTLS_ENABLED = 'true';
      process.env.API_IS_SECURE = 'false';

      const result = await setupMtlsAgent({});

      expect(result.success).toBe(false);
      expect(result.reason).toBe('mTLS is disabled or API is not secure');
      expect(https.Agent).not.toHaveBeenCalled();
    });

    it('should create agent when mTLS is enabled and API is secure', async () => {
      process.env.API_MTLS_ENABLED = 'true';
      process.env.API_IS_SECURE = 'true';

      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/etc/ssl/client/client.crt' ||
            path === '/etc/ssl/private/client.key') {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/etc/ssl/client/client.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/etc/ssl/private/client.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });

      const result = await setupMtlsAgent({});

      expect(result.success).toBe(true);
      expect(result.agent).toBeDefined();
      expect(result.certPath).toBe('/etc/ssl/client/client.crt');
      expect(result.keyPath).toBe('/etc/ssl/private/client.key');
      expect(https.Agent).toHaveBeenCalledWith(expect.objectContaining({
        cert: expect.stringContaining('-----BEGIN CERTIFICATE-----'),
        key: expect.stringContaining('-----BEGIN PRIVATE KEY-----'),
        rejectUnauthorized: true,
      }));
    });
  });

  describe('Certificate loading', () => {
    beforeEach(() => {
      process.env.API_MTLS_ENABLED = 'true';
      process.env.API_IS_SECURE = 'true';
    });

    it('should load certificates from standard paths', async () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/etc/ssl/client/client.crt' ||
            path === '/etc/ssl/private/client.key') {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/etc/ssl/client/client.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/etc/ssl/private/client.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });

      const result = await setupMtlsAgent({});

      expect(result.success).toBe(true);
      expect(result.certPath).toBe('/etc/ssl/client/client.crt');
      expect(result.keyPath).toBe('/etc/ssl/private/client.key');
    });

    it('should load certificates from environment variable paths', async () => {
      process.env.MTLS_CLIENT_CERT_PATH = '/custom/path/client.crt';
      process.env.MTLS_CLIENT_KEY_PATH = '/custom/path/client.key';

      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/custom/path/client.crt' ||
            path === '/custom/path/client.key') {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/custom/path/client.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/custom/path/client.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });

      const result = await setupMtlsAgent({});

      expect(result.success).toBe(true);
      expect(result.certPath).toBe('/custom/path/client.crt');
      expect(result.keyPath).toBe('/custom/path/client.key');
    });

    it('should load certificates from local development paths', async () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/client.crt' ||
            path === '/private-keys/local/certs/mtls/client.key') {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/client.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/private-keys/local/certs/mtls/client.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });

      const result = await setupMtlsAgent({});

      expect(result.success).toBe(true);
      expect(result.certPath).toBe('/private-keys/local/certs/mtls/client.crt');
      expect(result.keyPath).toBe('/private-keys/local/certs/mtls/client.key');
    });

    it('should fail when certificate is not found', async () => {
      // Mock certificate files to not exist
      vi.mocked(fs.existsSync).mockReturnValue(false);

      const result = await setupMtlsAgent({});

      expect(result.success).toBe(false);
      expect(result.reason).toBe('Client certificate not found');
      expect(https.Agent).not.toHaveBeenCalled();
    });

    it('should fail when key is not found', async () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/etc/ssl/client/client.crt') {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/etc/ssl/client/client.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        return '';
      });

      const result = await setupMtlsAgent({});

      expect(result.success).toBe(false);
      expect(result.reason).toBe('Client key not found');
      expect(https.Agent).not.toHaveBeenCalled();
    });

    it('should skip invalid certificate format', async () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/etc/ssl/client/client.crt' ||
            path === '/private-keys/local/certs/mtls/client.crt' ||
            path === '/private-keys/local/certs/mtls/client.key') {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/etc/ssl/client/client.crt') {
          return 'INVALID CERTIFICATE FORMAT';
        }
        if (path === '/private-keys/local/certs/mtls/client.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/private-keys/local/certs/mtls/client.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });

      const result = await setupMtlsAgent({});

      expect(result.success).toBe(true);
      expect(result.certPath).toBe('/private-keys/local/certs/mtls/client.crt');
      expect(result.keyPath).toBe('/private-keys/local/certs/mtls/client.key');
    });

    it('should skip invalid key format', async () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/etc/ssl/client/client.crt' ||
            path === '/etc/ssl/private/client.key' ||
            path === '/private-keys/local/certs/mtls/client.key') {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/etc/ssl/client/client.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/etc/ssl/private/client.key') {
          return 'INVALID KEY FORMAT';
        }
        if (path === '/private-keys/local/certs/mtls/client.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });

      const result = await setupMtlsAgent({});

      expect(result.success).toBe(true);
      expect(result.certPath).toBe('/etc/ssl/client/client.crt');
      expect(result.keyPath).toBe('/private-keys/local/certs/mtls/client.key');
    });
  });

  describe('HTTPS agent creation', () => {
    beforeEach(() => {
      process.env.API_MTLS_ENABLED = 'true';
      process.env.API_IS_SECURE = 'true';

      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/etc/ssl/client/client.crt' ||
            path === '/etc/ssl/private/client.key') {
          return true;
        }
        return false;
      });

      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/etc/ssl/client/client.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/etc/ssl/private/client.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });
    });

    it('should create HTTPS agent with correct options', async () => {
      const result = await setupMtlsAgent({});

      expect(result.success).toBe(true);
      expect(result.agent).toBeDefined();
      expect(https.Agent).toHaveBeenCalledWith({
        cert: '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----',
        key: '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----',
        rejectUnauthorized: true,
        secureOptions: crypto.constants.SSL_OP_NO_TLSv1_3,
      });
    });

    it('should handle HTTPS agent creation errors', async () => {
      // Mock https.Agent to throw an error
      vi.mocked(https.Agent).mockImplementation(() => {
        throw new Error('Invalid certificate');
      });

      const result = await setupMtlsAgent({});

      expect(result.success).toBe(false);
      expect(result.reason).toContain('Error creating HTTPS agent');
      expect(result.reason).toContain('Invalid certificate');
    });
  });

  describe('Error handling', () => {
    beforeEach(() => {
      process.env.API_MTLS_ENABLED = 'true';
      process.env.API_IS_SECURE = 'true';
    });

    it('should handle file read errors', async () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/etc/ssl/client/client.crt') {
          return true;
        }
        return false;
      });

      // Mock readFileSync to throw an error
      vi.mocked(fs.readFileSync).mockImplementation(() => {
        throw new Error('Permission denied');
      });

      const result = await setupMtlsAgent({});

      expect(result.success).toBe(false);
      expect(result.reason).toBe('Client certificate not found');
    });

    it('should handle module import errors', async () => {
      // This test is not applicable since we're not using dynamic imports
      // Instead, test when no certificates are found
      vi.mocked(fs.existsSync).mockReturnValue(false);

      const result = await setupMtlsAgent({});

      expect(result.success).toBe(false);
      expect(result.reason).toBe('Client certificate not found');
    });
  });
});
