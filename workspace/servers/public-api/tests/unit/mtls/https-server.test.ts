import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';
import * as https from 'https';
import * as http from 'http';
import * as crypto from 'crypto';

// Mock the fs module
vi.mock('fs', () => ({
  existsSync: vi.fn(),
  readFileSync: vi.fn(),
}));

// Mock the path module
vi.mock('path', () => ({
  join: vi.fn((...args) => args.join('/')),
}));

// Mock the https module
vi.mock('https', () => ({
  Server: vi.fn(),
}));

// Mock the http module
vi.mock('http', () => ({
  Server: vi.fn(),
}));

// Mock the crypto module
vi.mock('crypto', () => ({
  constants: {
    SSL_OP_NO_TLSv1_3: 0x8000000,
  },
}));

// Extract the HTTPS server creation logic into a testable function
// This is based on the existing code in app.ts
function createServer(options: {
  enableMtls?: boolean;
  certDir?: string;
  serverCert?: string;
  serverKey?: string;
  clientCert?: string;
}) {
  const {
    enableMtls = process.env.ENABLE_MTLS === '1',
    certDir = process.env.MTLS_CERT_DIR || '/private-keys/local/certs/mtls',
    serverCert,
    serverKey,
    clientCert,
  } = options;

  if (!enableMtls) {
    console.log('🔑 mTLS is disabled, using regular HTTP server');
    return {
      success: true,
      server: new http.Server(),
      type: 'http',
    };
  }

  try {
    // Setup HTTPS with mTLS
    console.log('🔐 mTLS is enabled');
    console.log('📎 Certificate directory:', certDir);

    // Create HTTPS options
    const httpsOptions: any = {
      // Add this option to help with possible encoding issues
      secureOptions: crypto.constants.SSL_OP_NO_TLSv1_3,
    };

    // Use provided certificates or load from files
    if (serverCert && serverKey) {
      httpsOptions.cert = serverCert;
      httpsOptions.key = serverKey;
    } else {
      // Try to load server certificate and key
      try {
        // First try direct paths for GCP Cloud Run with mounted secrets
        let certPath = path.join(certDir, 'server.crt');
        let keyPath = path.join(certDir, 'server.key');

        // If direct paths don't exist, try the nested structure for local development
        if (!fs.existsSync(certPath)) {
          certPath = path.join(certDir, 'certs', 'server.crt');
        }

        if (!fs.existsSync(keyPath)) {
          keyPath = path.join(certDir, 'private', 'server.key');
        }

        // Check if certificate files exist
        if (!fs.existsSync(certPath)) {
          return { success: false, reason: `Server certificate not found at ${certPath}` };
        }

        if (!fs.existsSync(keyPath)) {
          return { success: false, reason: `Server key not found at ${keyPath}` };
        }

        // Read certificate files
        httpsOptions.cert = fs.readFileSync(certPath, 'utf8');
        httpsOptions.key = fs.readFileSync(keyPath, 'utf8');
      } catch (error) {
        return { 
          success: false, 
          reason: `Error loading server certificates: ${error instanceof Error ? error.message : String(error)}` 
        };
      }
    }

    // Add client certificate if provided
    if (clientCert) {
      httpsOptions.ca = clientCert;
      httpsOptions.requestCert = true;
      httpsOptions.rejectUnauthorized = true;
    } else {
      // Try to load client certificate CA for verification
      try {
        // Try multiple possible paths for client certificate
        const possibleClientCertPaths = [
          // New standard path
          '/etc/ssl/client/client.crt',
          // Legacy paths for backward compatibility
          '/etc/ssl/certs/client.crt',
          // Environment variable path
          process.env.MTLS_CLIENT_CERT_PATH,
          // Local development paths
          path.join(certDir, 'client.crt'),
          path.join(certDir, 'certs', 'client.crt'),
          path.join('/private-keys/local/certs/mtls', 'client.crt')
        ].filter(Boolean); // Remove undefined/null values

        let clientCertFound = false;

        for (const clientCertPath of possibleClientCertPaths) {
          if (!clientCertPath) continue;

          if (fs.existsSync(clientCertPath)) {
            try {
              httpsOptions.ca = fs.readFileSync(clientCertPath, 'utf8');
              
              // Enable client certificate verification
              httpsOptions.requestCert = true;
              httpsOptions.rejectUnauthorized = true;
              clientCertFound = true;
              break;
            } catch (error) {
              // Continue to next path on error
            }
          }
        }

        if (!clientCertFound) {
          console.log('⚠️ Client certificate not found, mTLS will not require client certificates');
        }
      } catch (error) {
        console.log(`⚠️ Error loading client certificate: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // Create HTTPS server with the options
    console.log('🔐 Creating HTTPS server with mTLS options');
    const server = new https.Server(httpsOptions);
    console.log('✅ HTTPS server created successfully');
    
    return {
      success: true,
      server,
      type: 'https',
      options: httpsOptions,
    };
  } catch (error) {
    console.error('❌ Error setting up mTLS:', error instanceof Error ? error.message : String(error));
    console.log('⚠️ Falling back to regular HTTP server');
    
    return {
      success: true,
      server: new http.Server(),
      type: 'http',
      reason: `Fallback to HTTP due to error: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

describe('HTTPS Server Creation', () => {
  // Save original environment variables
  const originalEnv = { ...process.env };
  
  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();
    
    // Reset environment variables
    process.env = { ...originalEnv };
    
    // Default mock for path.join to behave like the real function
    vi.mocked(path.join).mockImplementation((...args) => args.join('/'));
    
    // Mock console methods to prevent noise in test output
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });
  
  afterEach(() => {
    // Restore environment variables
    process.env = { ...originalEnv };
    
    // Restore console methods
    vi.restoreAllMocks();
  });
  
  describe('mTLS enablement', () => {
    it('should create HTTP server when mTLS is disabled', () => {
      process.env.ENABLE_MTLS = '0';
      
      const result = createServer({});
      
      expect(result.success).toBe(true);
      expect(result.type).toBe('http');
      expect(http.Server).toHaveBeenCalled();
      expect(https.Server).not.toHaveBeenCalled();
    });
    
    it('should create HTTPS server when mTLS is enabled', () => {
      process.env.ENABLE_MTLS = '1';
      
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/server.crt' || 
            path === '/private-keys/local/certs/mtls/server.key') {
          return true;
        }
        return false;
      });
      
      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/server.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/private-keys/local/certs/mtls/server.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });
      
      const result = createServer({});
      
      expect(result.success).toBe(true);
      expect(result.type).toBe('https');
      expect(https.Server).toHaveBeenCalled();
      expect(http.Server).not.toHaveBeenCalled();
    });
  });
  
  describe('Server certificate loading', () => {
    beforeEach(() => {
      process.env.ENABLE_MTLS = '1';
    });
    
    it('should use provided server certificates', () => {
      const serverCert = '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
      const serverKey = '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
      
      const result = createServer({ serverCert, serverKey });
      
      expect(result.success).toBe(true);
      expect(result.type).toBe('https');
      expect(https.Server).toHaveBeenCalledWith(expect.objectContaining({
        cert: serverCert,
        key: serverKey,
      }));
      expect(fs.existsSync).not.toHaveBeenCalled();
      expect(fs.readFileSync).not.toHaveBeenCalled();
    });
    
    it('should load server certificates from files', () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/server.crt' || 
            path === '/private-keys/local/certs/mtls/server.key') {
          return true;
        }
        return false;
      });
      
      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/server.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/private-keys/local/certs/mtls/server.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });
      
      const result = createServer({});
      
      expect(result.success).toBe(true);
      expect(result.type).toBe('https');
      expect(https.Server).toHaveBeenCalledWith(expect.objectContaining({
        cert: expect.stringContaining('-----BEGIN CERTIFICATE-----'),
        key: expect.stringContaining('-----BEGIN PRIVATE KEY-----'),
      }));
    });
    
    it('should fail when server certificate is not found', () => {
      // Mock certificate files to not exist
      vi.mocked(fs.existsSync).mockReturnValue(false);
      
      const result = createServer({});
      
      expect(result.success).toBe(false);
      expect(result.reason).toContain('Server certificate not found');
      expect(https.Server).not.toHaveBeenCalled();
    });
    
    it('should fail when server key is not found', () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/server.crt' || 
            path === '/private-keys/local/certs/mtls/certs/server.crt') {
          return true;
        }
        return false;
      });
      
      const result = createServer({});
      
      expect(result.success).toBe(false);
      expect(result.reason).toContain('Server key not found');
      expect(https.Server).not.toHaveBeenCalled();
    });
    
    it('should handle file read errors', () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/server.crt' || 
            path === '/private-keys/local/certs/mtls/server.key') {
          return true;
        }
        return false;
      });
      
      // Mock readFileSync to throw an error
      vi.mocked(fs.readFileSync).mockImplementation(() => {
        throw new Error('Permission denied');
      });
      
      const result = createServer({});
      
      expect(result.success).toBe(false);
      expect(result.reason).toContain('Error loading server certificates');
      expect(result.reason).toContain('Permission denied');
      expect(https.Server).not.toHaveBeenCalled();
    });
  });
  
  describe('Client certificate loading', () => {
    beforeEach(() => {
      process.env.ENABLE_MTLS = '1';
      
      // Mock server certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/server.crt' || 
            path === '/private-keys/local/certs/mtls/server.key') {
          return true;
        }
        return false;
      });
      
      // Mock server certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/server.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/private-keys/local/certs/mtls/server.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });
    });
    
    it('should use provided client certificate', () => {
      const clientCert = '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
      
      const result = createServer({ clientCert });
      
      expect(result.success).toBe(true);
      expect(result.type).toBe('https');
      expect(result.options).toEqual(expect.objectContaining({
        ca: clientCert,
        requestCert: true,
        rejectUnauthorized: true,
      }));
    });
    
    it('should load client certificate from standard path', () => {
      // Mock client certificate to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/server.crt' || 
            path === '/private-keys/local/certs/mtls/server.key' ||
            path === '/etc/ssl/client/client.crt') {
          return true;
        }
        return false;
      });
      
      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/server.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/private-keys/local/certs/mtls/server.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        if (path === '/etc/ssl/client/client.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        return '';
      });
      
      const result = createServer({});
      
      expect(result.success).toBe(true);
      expect(result.type).toBe('https');
      expect(result.options).toEqual(expect.objectContaining({
        ca: expect.stringContaining('-----BEGIN CERTIFICATE-----'),
        requestCert: true,
        rejectUnauthorized: true,
      }));
    });
    
    it('should create HTTPS server without client certificate', () => {
      // Mock client certificate to not exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/server.crt' || 
            path === '/private-keys/local/certs/mtls/server.key') {
          return true;
        }
        return false;
      });
      
      const result = createServer({});
      
      expect(result.success).toBe(true);
      expect(result.type).toBe('https');
      expect(result.options).not.toHaveProperty('ca');
      expect(result.options).not.toHaveProperty('requestCert');
      expect(result.options).not.toHaveProperty('rejectUnauthorized');
    });
  });
  
  describe('Error handling', () => {
    beforeEach(() => {
      process.env.ENABLE_MTLS = '1';
    });
    
    it('should fall back to HTTP server on HTTPS server creation error', () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/server.crt' || 
            path === '/private-keys/local/certs/mtls/server.key') {
          return true;
        }
        return false;
      });
      
      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/private-keys/local/certs/mtls/server.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/private-keys/local/certs/mtls/server.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });
      
      // Mock https.Server to throw an error
      vi.mocked(https.Server).mockImplementation(() => {
        throw new Error('Invalid certificate');
      });
      
      const result = createServer({});
      
      expect(result.success).toBe(true);
      expect(result.type).toBe('http');
      expect(result.reason).toContain('Fallback to HTTP due to error');
      expect(result.reason).toContain('Invalid certificate');
      expect(http.Server).toHaveBeenCalled();
    });
  });
  
  describe('Custom certificate directory', () => {
    beforeEach(() => {
      process.env.ENABLE_MTLS = '1';
    });
    
    it('should use custom certificate directory from environment variable', () => {
      process.env.MTLS_CERT_DIR = '/custom/cert/dir';
      
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/custom/cert/dir/server.crt' || 
            path === '/custom/cert/dir/server.key') {
          return true;
        }
        return false;
      });
      
      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/custom/cert/dir/server.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/custom/cert/dir/server.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });
      
      createServer({});
      
      expect(vi.mocked(fs.existsSync)).toHaveBeenCalledWith('/custom/cert/dir/server.crt');
      expect(vi.mocked(fs.existsSync)).toHaveBeenCalledWith('/custom/cert/dir/server.key');
    });
    
    it('should use custom certificate directory from function parameter', () => {
      // Mock certificate files to exist
      vi.mocked(fs.existsSync).mockImplementation((path) => {
        if (path === '/param/cert/dir/server.crt' || 
            path === '/param/cert/dir/server.key') {
          return true;
        }
        return false;
      });
      
      // Mock certificate content
      vi.mocked(fs.readFileSync).mockImplementation((path) => {
        if (path === '/param/cert/dir/server.crt') {
          return '-----BEGIN CERTIFICATE-----\nMIIDQzCCAiugAwIBAgIUJlq06p\n-----END CERTIFICATE-----';
        }
        if (path === '/param/cert/dir/server.key') {
          return '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQ\n-----END PRIVATE KEY-----';
        }
        return '';
      });
      
      createServer({ certDir: '/param/cert/dir' });
      
      expect(vi.mocked(fs.existsSync)).toHaveBeenCalledWith('/param/cert/dir/server.crt');
      expect(vi.mocked(fs.existsSync)).toHaveBeenCalledWith('/param/cert/dir/server.key');
    });
  });
});
