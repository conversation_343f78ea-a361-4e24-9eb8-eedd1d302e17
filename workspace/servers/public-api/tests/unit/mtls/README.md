# Client-Side mTLS Unit Tests

This directory contains unit tests for the client-side mTLS (Mutual TLS) implementation in the Divinci web client.

## Overview

These tests verify the core functionality of the client-side mTLS implementation, including:

1. Certificate loading from various paths
2. HTTPS agent creation with mTLS options
3. Error handling for various scenarios

## Test Files

### `client-mtls.test.ts`

Tests the client-side mTLS implementation, including:

- Loading client certificates from different paths
- Creating an HTTPS agent with mTLS options
- Handling missing or invalid certificates
- Environment variable overrides for certificate paths
- Error handling during setup

## Running the Tests

To run these tests, use the following command from the project root:

```bash
cd workspace/clients/web
npm test -- tests/unit/mtls
```

## Test Coverage

These tests aim to provide comprehensive coverage of the client-side mTLS implementation, including:

- Happy path scenarios (valid certificates, proper configuration)
- Error handling (missing certificates, invalid formats)
- Edge cases (different certificate formats, environment variable overrides)
- Browser vs. Node.js environment detection

## Future Improvements

- Add integration tests with a local mTLS server
- Add tests for certificate rotation
- Add tests for certificate expiration monitoring
- Add tests for standardized certificate paths
- Add tests for browser-specific certificate handling
