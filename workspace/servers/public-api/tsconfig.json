{"compilerOptions": {"target": "es2022", "module": "nodenext", "moduleResolution": "nodenext", "types": ["node", "vitest/globals"], "lib": ["es2022", "dom"], "sourceMap": true, "outDir": "./dist", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "removeComments": true, "noImplicitAny": true}, "files": ["src/index.ts"], "include": ["src", "./typings", "./types", "tests/unit/src/ux/whitelabel/pending-file.test.ts", "tests/unit/src/ux/whitelabel/add-chunks.test.ts"], "exclude": ["node_modules", "tests"]}