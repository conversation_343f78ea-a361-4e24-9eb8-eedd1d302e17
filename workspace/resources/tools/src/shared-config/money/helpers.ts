import { SelectedPublicToolWithConfig } from "@divinci-ai/models";
import { ToolConfig } from "../types";
import { ShallowObject } from "@divinci-ai/utils";

export function estimatePricingForTool<Config extends ShallowObject, Input>(
  availableTools: Record<string, ToolConfig<any, Config, Input, any>>,
  toolConfig: SelectedPublicToolWithConfig,
  input: Input,
){
  const tool = availableTools[toolConfig.id];
  if(!tool) throw new Error(`Invalid tool id: ${toolConfig.id}`);
  if(tool.validateInput && !tool.validateInput(input)){
    throw new Error(`Invalid input for tool ${toolConfig.id}`);
  }
  if(tool.inputConfig){
    const errors = tool.inputConfig.validate(toolConfig.config);
    if(errors.length > 0){
      throw new Error(`Invalid config: ${errors.map((e)=>e.error).join(", ")}`);
    }
  }
  return tool.pricing.getEstimatedCost(toolConfig.config, input);
}


export function finalizePricingForTool<Input, Output>(
  availableTools: Record<string, ToolConfig<any, any, Input, Output>>,
  toolConfig: SelectedPublicToolWithConfig,
  input: Input,
  output: Output,
){
  const tool = availableTools[toolConfig.id];
  if(!tool) throw new Error(`Invalid tool id: ${toolConfig.id}`);
  return tool.pricing.getFinalCost(toolConfig.config, input, output);
}
