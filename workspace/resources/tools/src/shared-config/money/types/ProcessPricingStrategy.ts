
import { ShallowObject } from "@divinci-ai/utils";
import { ToolPricingStrategy, ToolInitialCosts, ToolFinalCosts } from "./ToolPricingStrategy";
import { SelectedPublicToolWithConfig } from "@divinci-ai/models";
import { ToolConfig } from "../../types";
import {
  estimatePricingForTool,
  finalizePricingForTool,
} from "../helpers";

export class ProcessPricingStrategy<
  Config extends ShallowObject,
  InputArg, OutputResult,
  Too<PERSON><PERSON><PERSON> extends string = string
> {
  constructor(
    public name: string,
    private mandatoryCosts: Record<string, ToolPricingStrategy<Config, InputArg, OutputResult>>,
    public mandatoryInfo: Record<string, { title: string, description: string }>,
    private selectableTools: (
      Record<ToolKey, Record<string, ToolConfig<any, any, InputArg, OutputResult>>>
    ),
    public selectableInfo: Record<ToolKey, { title: string, description: string }>,
    private validateInput?: (input: InputArg)=>boolean | Promise<boolean>
  ){
    for(const key in selectableTools){
      if(key in mandatoryCosts){
        throw new Error(`Process has conflicting key "${key}" in both mandatory and selectable costs`);
      }
    }
  }

  async getEstimatedCost(
    tools: Record<ToolKey, SelectedPublicToolWithConfig>,
    config: Config,
    input: InputArg
  ){
    if(this.validateInput && !await this.validateInput(input)){
      throw new Error("Input Failed Validation");
    }

    const costList = await Promise.all([
      ...Object.entries(this.mandatoryCosts).map(async ([key, pricing])=>(
        {
          key, costs: await pricing.getEstimatedCost(config, input)
        }
      )),
      ...typedEntries(this.selectableTools).map(async ([key, available])=>{
        const selectedTool = tools[key];
        if(!selectedTool) throw new Error(`Missing tool config for ${key}`);
        return {
          key, costs: await estimatePricingForTool(available, selectedTool, input)
        };
      }),
    ]);
    const costs: Record<string, ToolInitialCosts> = {};
    for(const cost of costList){
      costs[cost.key] = cost.costs;
    }
    return costs;
  }

  async getFinalCost(
    tools: Record<ToolKey, SelectedPublicToolWithConfig>,
    config: Config,
    input: InputArg,
    output: OutputResult,
  ): Promise<Record<string, ToolFinalCosts>>{
    const costList = await Promise.all([
      ...Object.entries(this.mandatoryCosts).map(async ([key, pricing])=>(
        {
          key, costs: await pricing.getFinalCost(config, input, output)
        }
      )),
      ...typedEntries(this.selectableTools).map(async ([key, available])=>{
        const selectedTool = tools[key];
        if(!selectedTool) throw new Error(`Missing tool config for ${key}`);
        return {
          key, costs: await finalizePricingForTool(available, selectedTool, input, output)
        };
      }),
    ]);
    const costs: Record<string, ToolFinalCosts> = {};
    for(const cost of costList){
      costs[cost.key] = cost.costs;
    }
    return costs;
  }
}

function typedEntries<T extends object>(obj: T): [keyof T, T[keyof T]][]{
  return Object.entries(obj) as [keyof T, T[keyof T]][];
}
