import { ShallowObject } from "@divinci-ai/utils";
import { ToolConfig } from "./types";

export function toolByIdOrDefault(
  id: undefined | null | string,
  map: Record<string, ToolConfig<any, any, any, any, any>>
){
  if(id && id in map) return map[id];
  const tool = Object.values(map)[0];
  if(!tool) throw new Error("No tools available");
  return tool;
}

export function toolToState(tool: ToolConfig<any, any, any, any, any>): {
  id: string,
  config: ShallowObject,
}{
  return { id: tool.id, config: !tool.inputConfig ? {} : tool.inputConfig.default };
}

export function getInitialToolValue(
  available: Record<string, ToolConfig<any, any, any, any, any>>, id?: string | null
){
  const tool = toolByIdOrDefault(id, available);
  return toolToState(tool);
}
