
import { ToolType } from "./type";
import { ToolAvailableStatus } from "../../../../../types";
import { PRICING } from "./pricing";
import { INFO } from "./info";
import { GeneratorAssistantTypeName } from "../../../types";
import { Prefix } from "./meta";
import { AI_CATEGORY_ENUM } from "@divinci-ai/models";

export const OpenAIGPT4oMiniMermaid: ToolType = {
  toolType: GeneratorAssistantTypeName,
  id: INFO.id,
  status: ToolAvailableStatus.AVAILABLE,

  info: INFO,
  pricing: PRICING,
  meta: {
    assistantCategory: AI_CATEGORY_ENUM.DIAGRAM,
    canReply: true,
    messagePrefix: Prefix
  }
};
