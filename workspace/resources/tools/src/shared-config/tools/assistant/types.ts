import {
  TRANSCRIPT_MESSAGE_CATEGORY_ENUM,
} from "@divinci-ai/models";
import { ShallowObject } from "@divinci-ai/utils";
import { ToolConfig } from "../../types";
import { GeneratorAssistantMetaData } from "@divinci-ai/models";

export type GeneratorAssistantInput = {
  thread: Array<(
    | { role: "system", content: string }
    | { role: "user", content: string, category: TRANSCRIPT_MESSAGE_CATEGORY_ENUM, user: string }
    | { role: "assistant", content: string, category: TRANSCRIPT_MESSAGE_CATEGORY_ENUM }
  )>,
  responseValues: { id: string },
  customModel?: string,
};

export type GeneratorAssistantOutput = {
  text: string,
};

export const GeneratorAssistantTypeName = "GeneratorAssistant";
export type GeneratorAssistantTypeName = "GeneratorAssistant";

export { GeneratorAssistantMetaData };

export type GeneratorAssistantBase<
  Config extends ShallowObject
> = (
  ToolConfig<
    GeneratorAssistantTypeName, Config,
    GeneratorAssistantInput,
    GeneratorAssistantOutput,
    GeneratorAssistantMetaData
  >
);
