
import { GeneratorAssistantInput } from "../shared-config";
import { AnonymousMessage } from "@divinci-ai/models";
import { MessageContextItem, TRANSCRIPT_MESSAGE_CATEGORY_ENUM } from "@divinci-ai/models";

export function anonymousTranscriptToAssistantInput(
  anonymousChat: Array<AnonymousMessage>,
  msgEnv: {
    threadPrefix?: Array<string>,
    messagePrefix?: string,
  },
  newMessage: {
    context?: Array<MessageContextItem>,
    content: string,
  }
): GeneratorAssistantInput["thread"]{
  const input: GeneratorAssistantInput["thread"] = [];
  if(msgEnv.threadPrefix){
    for(const prefix of msgEnv.threadPrefix){
      input.push({
        role: "system",
        content: prefix,
      });
    }
  }
  input.push(...collectThread(anonymousChat));
  if(msgEnv.messagePrefix){
    input.push({
      role: "system",
      content: msgEnv.messagePrefix
    });
  }
  if(newMessage.context && newMessage.context.length > 0){
    input.push(...buildContextMessage(newMessage.context));
  }
  input.push({
    role: "user",
    category: TRANSCRIPT_MESSAGE_CATEGORY_ENUM.TEXT,
    content: newMessage.content,
    user: "anonymous"
  });

  return input;
}

function collectThread(
  anonymousChat: Array<AnonymousMessage>,
){
  const thread: GeneratorAssistantInput["thread"] = [];
  for(const message of anonymousChat){
    if(message.msgPrefix){
      thread.push({
        role: "system",
        content: message.msgPrefix
      });
    }
    if(message.context && message.context.length > 0){
      thread.push(...buildContextMessage(message.context));
    }
    thread.push({
      role: "user",
      category: TRANSCRIPT_MESSAGE_CATEGORY_ENUM.TEXT,
      content: message.prompt,
      user: "anonymous"
    });
    thread.push({
      role: "assistant",
      category: TRANSCRIPT_MESSAGE_CATEGORY_ENUM.TEXT,
      content: message.response
    });
  }
  return thread;
}

function buildContextMessage(
  context: Array<MessageContextItem>
): GeneratorAssistantInput["thread"]{
  return [{
    role: "system",
    content: `Context:\n${context.map((context)=>`- ${context.content}`).join("\n")}`
  }];
}
