import { MessageContextItem, TranscriptMessage } from "@divinci-ai/models";
import { GeneratorAssistantInput } from "../shared-config";
import { TRANSCRIPT_MESSAGE_CATEGORY_ENUM } from "@divinci-ai/models";

export function transcriptToAssistantInput(
  transcript: Array<TranscriptMessage>,
  runnerUserId: string,
  msgEnv: {
    threadPrefix?: Array<string>,
    messagePrefix?: string,
  },
  newMessage: {
    replyTo?: string,
    context?: Array<MessageContextItem>,
    category: TRANSCRIPT_MESSAGE_CATEGORY_ENUM,
    content: string,
  }
): GeneratorAssistantInput["thread"]{
  const input: GeneratorAssistantInput["thread"] = [];
  if(msgEnv.threadPrefix){
    for(const prefix of msgEnv.threadPrefix){
      input.push({
        role: "system",
        content: prefix
      });
    }
  }
  if(newMessage.replyTo){
    input.push(
      ...collectThread(transcript, newMessage)
    );
  }
  if(msgEnv.messagePrefix){
    input.push({
      role: "system",
      content: msgEnv.messagePrefix
    });
  }
  if(newMessage.context && newMessage.context.length > 0){
    for(const contextItem of newMessage.context){
      input.push({
        role: "system",
        content: contextItem.content,
      });
    }
  }
  input.push({
    role: "user",
    category: newMessage.category,
    content: newMessage.content,
    user: runnerUserId
  });

  return input;
}

function collectThread(
  transcript: Array<TranscriptMessage>,
  newMessage: {
    replyTo?: string,
    context?: Array<MessageContextItem>,
    content: string,
  }
){
  const threadReversed: GeneratorAssistantInput["thread"] = [];
  if(newMessage.replyTo){
    for(const message of yieldThreadItem(transcript, newMessage.replyTo)){
      switch(message.role){
        case "error": throw new Error("cannot respond to error");
        case "system": {
          threadReversed.push({
            role: message.role,
            content: message.content
          });
          break;
        }
        case "assistant": {
          threadReversed.push({
            role: message.role,
            category: message.category as TRANSCRIPT_MESSAGE_CATEGORY_ENUM,
            content: message.content
          });
          break;
        }
        case "user": {
          threadReversed.push({
            role: message.role,
            category: message.category as TRANSCRIPT_MESSAGE_CATEGORY_ENUM,
            content: message.content,
            user: message.name
          });
          break;
        }
        default: throw new Error("Unknown message role");
      }
      if(message.context && message.context.length > 0){
        for(const contextItem of message.context){
          threadReversed.push({
            role: "system",
            content: contextItem.content,
          });
        }
      }
    }
  }
  return threadReversed.reverse();
}


function* yieldThreadItem(
  transcript: Array<TranscriptMessage>,
  initial: string
){
  let toFind = initial;
  let completed = false;
  for(let i = transcript.length - 1; i > -1; i--){
    const message = transcript[i];
    if(message._id !== toFind) continue;
    // if(message.category !== TRANSCRIPT_MESSAGE_CATEGORY_ENUM.TEXT){
    //   throw new Error("❌collectThread message category must be text for now");
    // }
    yield message;
    if(typeof message.replyTo === "undefined"){
      completed = true;
      break;
    }
    toFind = message.replyTo;
  }
  if(!completed){
    throw new Error("❌collectThread was not able to find message " + toFind);
  }
}
