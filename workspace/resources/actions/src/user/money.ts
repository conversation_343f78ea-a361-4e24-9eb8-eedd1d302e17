import { StripePaymentMethod, NanoUSDWalletTransactionDoc } from "@divinci-ai/models";
import { fetchBody, jsonExtraResponse } from "@divinci-ai/utils";

export function getUsersBalance(fetcher: typeof fetch){
  return jsonExtraResponse(fetcher(
    "/user/money/balance",
  )) as Promise<{ balance: bigint }>;
}

export function getUsersPaymentMethods(fetcher: typeof fetch){
  return jsonExtraResponse(fetcher(
    "/user/money/payment-method/list",
  )) as Promise<Array<StripePaymentMethod>>;
}

export function depositUserFunds(fetcher: typeof fetch, body: { amount: number, paymentMethodId: string }){
  return jsonExtraResponse(fetcher(
    "/user/money/deposit",
    fetchBody("POST", body)
  )) as Promise<{ status: string, amount: number }>;
}

export function getUsersTransactions(fetcher: typeof fetch, range: { start: number, end: number }){
  return jsonExtraResponse(fetcher(
    `/user/money/transactions?start=${range.start}&end=${range.end}`,
  )) as Promise<Array<NanoUSDWalletTransactionDoc>>;
}
