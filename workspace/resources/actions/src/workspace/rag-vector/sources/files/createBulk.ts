import {
  handleFetch, replacePara<PERSON>, fetchBody,
  checkUsePresigned, PresignedCheck,
  JSON_Object
} from "@divinci-ai/utils";
import { RagVectorFileDoc } from "@divinci-ai/models";

import { RAG_FILE_ROOT } from "./paths";
const RAG_FILE_CREATE_BULK = `${RAG_FILE_ROOT}/bulk`;

type CreateBody = {
  files: Array<File>,
  chunkingTool: string,
  chunkingToolConfig?: JSON_Object,
  title: string,
  description: string,
};

export function createRagVectorFileBulk(
  fetcher: typeof fetch, params: { whitelabelId: string }, body: CreateBody,
  presigned: PresignedCheck = "check-size"
){
  let totalByteSize = 0;
  for(const file of body.files) totalByteSize += file.size;
  if(!checkUsePresigned(totalByteSize, presigned)){
    return createRagVectorFileBulkDirect(fetcher, params, body);
  }

  return createRagVectorFileBulkPresigned(fetcher, params, body);
}


async function createRagVectorFileBulkDirect(
  fetcher: typeof fetch, params: { whitelabelId: string }, body: CreateBody
){
  const formData = new FormData();

  for(const file of body.files) formData.append("file", file);

  formData.set("chunkingTool", body.chunkingTool);
  if(body.chunkingToolConfig){
    formData.set("chunkingToolConfig", JSON.stringify(body.chunkingToolConfig));
  }
  formData.set("title", body.title);
  formData.set("description", body.description);

  return await handleFetch(fetcher(
    replaceParams(RAG_FILE_CREATE_BULK, params),
    { method: "POST", body: formData, }
  )) as Array<RagVectorFileDoc>;
}

import { PresignedResult, handleEmptyFetch } from "@divinci-ai/utils";
async function createRagVectorFileBulkPresigned(
  fetcher: typeof fetch, params: { whitelabelId: string }, body: CreateBody
){

  const fileConfigs = body.files.map((file)=>({ byteSize: file.size, filename: file.name }));
  const presigned = await handleFetch(
    fetcher(
      replaceParams(`${RAG_FILE_CREATE_BULK}/presigned/prepare`, params),
      fetchBody("POST", fileConfigs)
    )
  ) as Array<PresignedResult>;

  const files = [...body.files];
  await Promise.all(presigned.map(({ url, byteSize, filename })=>{
    const index = files.findIndex((file)=>(
      file.size === byteSize && file.name === filename
    ));
    if(index === -1) throw new Error("Couldn't process file");
    const file = files.splice(index, 1)[0];
    return handleEmptyFetch(fetch(url, {
      method: "PUT",
      headers: { "Content-Type": file.type },
      body: file
    }));
  }));

  return await handleFetch(fetcher(
    replaceParams(`${RAG_FILE_CREATE_BULK}/presigned/run`, params),
    fetchBody("POST", {
      chunkingTool: body.chunkingTool,
      title: body.title,
      description: body.description,
      files: presigned.map((data)=>(JSON.stringify(data))),
      ...(!body.chunkingToolConfig ? {} :(
        { chunkingToolConfig: JSON.stringify(body.chunkingToolConfig) }
      ))
    })
  )) as Array<RagVectorFileDoc>;
}
