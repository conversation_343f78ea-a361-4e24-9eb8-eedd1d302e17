import { FineTuneAI } from "@divinci-ai/models";

import { fetchBody, handleFetch, replaceParams } from "@divinci-ai/utils";
import { FINETUNE_AI_ITEM } from "../paths";

export async function updateFineTuneInfo(
  user: typeof fetch,
  params: { whitelabelId: string, customAiId: string },
  body: { title: string, description: string }
){
  return await handleFetch(user(
    replaceParams(`${FINETUNE_AI_ITEM}/user-info`, params),
    fetchBody("POST", body)
  )) as FineTuneAI;
}
