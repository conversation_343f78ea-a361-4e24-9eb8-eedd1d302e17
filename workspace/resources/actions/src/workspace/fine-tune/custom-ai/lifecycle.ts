

import { FineTuneAI } from "@divinci-ai/models";
import { fetchBody, handleFetch, replaceParams } from "@divinci-ai/utils";

import { FINETUNE_AI, FINETUNE_AI_ITEM } from "../paths";

export async function createFineTune(
  user: typeof fetch,
  params: { whitelabelId: string },
  body: { title: string, description: string, finetuneTool: string }
){
  return await handleFetch(user(
    replaceParams(FINETUNE_AI, params),
    fetchBody("POST", body)
  )) as FineTuneAI;
}

export async function getFineTune(
  user: typeof fetch, params: { whitelabelId: string, customAiId: string }
){
  return await handleFetch(user(
    replaceParams(FINETUNE_AI_ITEM, params),
  )) as FineTuneAI;
}

export async function forkFineTune(
  user: typeof fetch,
  params: { whitelabelId: string, customAiId: string },
  body: { title: string }
){
  return await handleFetch(user(
    replaceParams(`${FINETUNE_AI_ITEM}/fork`, params),
    fetchBody("POST", body)
  )) as FineTuneAI;
}


export async function deleteFineTune(
  user: typeof fetch,
  params: { whitelabelId: string, customAiId: string }
){
  return await handleFetch(user(
    replaceParams(FINETUNE_AI_ITEM, params),
    { method: "DELETE" }
  )) as FineTuneAI;
}
