
import { FineTuneJobInfo } from "@divinci-ai/models";
import { fetchBody, handleFetch, replaceParams } from "@divinci-ai/utils";
import { DIVINCI_TEST_PROCESS_Config, DIVINCI_TEST_PROCESS_AddHeader } from "@divinci-ai/utils";
import { FINETUNE_AI_ITEM } from "../paths";

export async function createFineTuneJob(
  user: typeof fetch,
  params: { whitelabelId: string, customAiId: string },
  body: { fileId: string },
  testConfig: null | DIVINCI_TEST_PROCESS_Config = null
){
  return await handleFetch(user(
    replaceParams(`${FINETUNE_AI_ITEM}/fine-tune`, params),
    DIVINCI_TEST_PROCESS_AddHeader(fetchBody("POST", body), testConfig)
  )) as FineTuneJobInfo;
}

export async function getFineTuneJob(
  user: typeof fetch,
  params: { whitelabelId: string, customAiId: string }
){
  return await handleFetch(user(
    replaceParams(`${FINETUNE_AI_ITEM}/fine-tune`, params),
  )) as null | FineTuneJobInfo;
}

export async function cancelFineTuneJob(
  user: typeof fetch,
  params: { whitelabelId: string, customAiId: string }
){
  return await handleFetch(user(
    replaceParams(`${FINETUNE_AI_ITEM}/fine-tune`, params),
    { method: "DELETE" }
  ))  as null | FineTuneJobInfo;
}
