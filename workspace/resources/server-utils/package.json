{"name": "@divinci-ai/server-utils", "version": "1.3.14", "description": "Utility functions for client and server.", "main": "dist/index.js", "typings": "src/index.ts", "scripts": {"build": "tsc", "build:ci": "tsc --project tsconfig.ci.json", "build:ignore-errors": "tsc --skipLib<PERSON>heck --noEmit || echo 'TypeScript errors ignored'", "build:ignore-errors:ci": "tsc --skipLib<PERSON>heck --noEmit --project tsconfig.ci.json || echo 'TypeScript errors ignored'", "prepare": "rimraf ./dist && tsc", "test": "vitest run", "test:watch": "vitest"}, "author": "", "license": "JSON", "dependencies": {"@aws-sdk/client-s3": "^3.750.0", "@aws-sdk/lib-storage": "^3.750.0", "@divinci-ai/utils": "file:../utils", "@types/async": "^3.2.24", "@types/body": "^5.1.4", "@types/ws": "^8.5.12", "abort-controller": "^3.0.0", "app-root-path": "^3.1.0", "async": "^3.2.6", "body": "^5.1.0", "busboy": "^1.6.0", "express": "^4.19.2", "form-data": "^4.0.1", "jszip": "^3.10.1", "mime-types": "^2.1.35", "mongoose": "^8.8.3", "node-fetch-commonjs": "^3.3.2", "path-to-regexp": "^6.3.0", "ws": "^8.18.0"}, "devDependencies": {"@types/async": "^3.2.20", "@types/body": "^5.1.1", "@types/busboy": "^1.5.4", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/mime-types": "^2.1.4", "@types/node": "^22.5.2", "@types/ws": "^8.5.12", "dotenv": "^16.3.1", "glob": "^10.4.1", "jest": "^29.7.0", "rimraf": "^6.0.1", "ts-jest": "^29.2.5", "typescript": "^5.8.3", "vitest": "^3.1.1"}, "engines": {"node": ">=20", "pnpm": ">=10"}, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b"}