import { TranscriptModel } from "../../index";
import { GPT_Message } from "../../types";

import { PromptCategorizer } from "./PromptCategorizer";
import { categorizersByKey } from "./prompts";

import { TranscriptMessageMetaData } from "@divinci-ai/models";

type GPT_Messages = Array<GPT_Message>;

const categorizers = Object.values(categorizersByKey);

export async function collectMetaData(this: InstanceType<typeof TranscriptModel>, replyTo: void | string, content: string): Promise<Array<TranscriptMessageMetaData>>{
  const fullMessages = typeof replyTo === "string" ? this.collectThread(replyTo) : [];
  const messages = fullMessages.map((chatmessage)=>{
    if(chatmessage.role === "error") throw new Error("Cannot reply to an error");
    return { role: chatmessage.role, content: chatmessage.content };
  });
  if(typeof replyTo === "string"){
    if(messages.length === 0){
      throw new Error("missing reply to");
    }
    const lastMessage = messages.at(-1);
    if(typeof lastMessage === "undefined"){
      throw new Error("no messages to reply to");
    }
    if(lastMessage.role !== "assistant"){
      throw new Error("the message to reply to should be an assistant message");
    }
  }

  return Promise.all(
    categorizers.map((categorizer)=>(runPrompt(categorizer, messages, content)))
  );
}


async function runPrompt(prompt: PromptCategorizer, prevMessages: GPT_Messages, content: string): Promise<TranscriptMessageMetaData> {
  try {
    return {
      name: prompt.key,
      type: "boolean",
      value: await prompt.run(content),
    };
  }catch(e){
    const message = typeof e !== "object" ? e : e === null ? "" : "message" in e ? e.message : e;
    console.error("failed metadata ", message, prompt.key, e);
    throw e;
  }
}
