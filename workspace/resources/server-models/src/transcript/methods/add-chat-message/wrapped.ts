import { TranscriptValueWalletModel, WalletConfig } from "../../../money";

import {
  ITranscriptModelType,
  PAYOUT_CONFIG,
  MessageContext,
  MessageEnvironment,
  TranscriptMessageBody,
  PreparedMessageValues,
} from "../../types";

import { uniqueId, logDebug } from "@divinci-ai/utils";

import { addRequestMessage } from "./context/add-request";
import { prepareThread } from "./context/prepare-thread";
import { addResponseMessage, addResponseErrorMessage } from "./context/add-response";
import { TranscriptMessage, MessageContextItem } from "@divinci-ai/models";

// Main function with refactored contextMessages part
export async function wrappedAddChatMessage(
  transcript: InstanceType<ITranscriptModelType>,
  msgContext: MessageContext,
  msgEnv: MessageEnvironment,
  message: TranscriptMessageBody,
  buildUrl: (message: TranscriptMessage) => string,
  walletConfig: WalletConfig,
  prepared?: PreparedMessageValues
): Promise<{
  message: TranscriptMessage,
  transcript: InstanceType<ITranscriptModelType>,
  contextMessages: Array<MessageContextItem>,
}>{
  const model = transcript.constructor as ITranscriptModelType;
  if(!prepared){
    prepared = await model.messagePrepareModerate(msgEnv, message);
  }
  const { user, context } = prepared;

  const { assistant, threadPrefix, } = msgEnv;
  const { assistantAPI, customAssistant } = assistant;
  const messageWithUser = { ...message, user };
  const responseId = uniqueId();

  const {
    transcript: updatedTranscript,
    message: reqMessage,
    contextMessages,
  } = await addRequestMessage(
    transcript,
    msgContext,
    msgEnv,
    context,
    messageWithUser,
    buildUrl,
  );

  logDebug("💬 post-wrappedAddChatMessage: transcript, message: ", transcript, {
    assistantAPI,
    customAssistant,
    messageWithUser,
    buildUrl,
    contextMessages,
  });

  let success = false;

  try {
    await TranscriptValueWalletModel.wrapTransaction(
      walletConfig,
      PAYOUT_CONFIG,
      async ()=>{
        const system: { content: string }[] = (reqMessage.msgPrefix ? [{ content: reqMessage.msgPrefix.prefix }] : []);
        logDebug("🤖🌟 TranscriptValueWalletModel.wrapTransaction::system: ", system);
        logDebug("🤖🌟 TranscriptValueWalletModel.wrapTransaction::reqMessage.msgPrefix: ", reqMessage.msgPrefix);
        logDebug("🤖🌟 TranscriptValueWalletModel.wrapTransaction::reqMessage.context: ", reqMessage.context);
        system.push(...(reqMessage.context || []));
        return {
          value: assistantAPI.calculateInputValue(
            message.content,
            system.map((item)=>(item.content)),
          )
        };
      },
      async (maximumOutputValue)=>{
        const thread = await prepareThread(
          threadPrefix,
          updatedTranscript,
          assistantAPI,
          reqMessage,
        );
        const response = await assistantAPI.generateValue(
          thread,
          { id: responseId },
          maximumOutputValue,
          customAssistant,
        );
        success = true;

        await addResponseMessage(
          updatedTranscript,
          msgContext,
          msgEnv,
          { message: reqMessage },
          { ...response, id: responseId },
          buildUrl,
        );

        return { value: assistantAPI.calculateOutputValue(response) };
      },
    );

    // Return the required shape
    return {
      message: reqMessage,
      transcript: updatedTranscript,
      contextMessages,
    };
  }catch(e){
    if(!success) {
      console.error("🔴 Message Add Error Should add Error Response:", e);
      await addResponseErrorMessage(
        updatedTranscript,
        msgContext,
        msgEnv,
        { user, phoneNumber: message.phoneNumber, message: reqMessage },
        { id: responseId },
        buildUrl,
      );
    }
    throw e;
  }
}
