import {
  getTwi<PERSON>, TWILIO_PHONE_NUMBER,
} from "@divinci-ai/server-globals";

import { regexLastIndexOf } from "@divinci-ai/utils";

import { PhoneNumberModel } from "../../../../user/PhoneNumber";

import { AIAssistantAPI } from "@divinci-ai/server-tools";


export async function broadcastTextMessage(
  exceptions: Array<string>, target: string, rawMessage: string, messageUrl: string
){

  console.log("broadcast TextMessage messageUrl:", messageUrl);

  const { twilioClient } = getTwilio();
  const numbers = await PhoneNumberModel.find({
    subscription: target
  }).exec();

  const message = fitMessageToSize(rawMessage, 1000);
  return Promise.all(
    numbers
    .filter((number)=>(
      exceptions.indexOf(number.phoneNumber) === -1
    ))
    .map((number)=>(
      twilioClient.messages.create({
        from: TWILIO_PHONE_NUMBER,
        to: number.phoneNumber,
        body: message,
      })
    ))
  );

  function fitMessageToSize(rawMessage: string, max: number){
    if(rawMessage.length <= max) return rawMessage;
    const restText = "...\n\nTo view the rest of this, go to:" + messageUrl;
    max = max - restText.length;
    let smallerMessage = rawMessage;
    do {
      const offset = regexLastIndexOf(rawMessage, /(\n )/);
      if(offset === -1){
        smallerMessage = smallerMessage.slice(0, max);
        break;
      }
      smallerMessage = smallerMessage.slice(0, offset);
    } while(smallerMessage.length > max);
    return smallerMessage + restText;
  }
}

export async function broadcastMessengerMessage<ResponseType>(
  exceptions: Array<string>,
  target: string,
  assistant: AIAssistantAPI<any, ResponseType>,
  response: { id: string, raw: ResponseType, text: string },
  messageURL: string
){
  const numbers = await PhoneNumberModel.find({
    subscription: target
  }).exec();

  console.log("attempting to broadcast to:", numbers);

  return Promise.all(
    numbers
    .filter((number)=>(
      exceptions.indexOf(number.phoneNumber) === -1
    ))
    .map((number)=>(
      assistant.sendSMSMessage(number.phoneNumber, response, messageURL)
    ))
  );
}
