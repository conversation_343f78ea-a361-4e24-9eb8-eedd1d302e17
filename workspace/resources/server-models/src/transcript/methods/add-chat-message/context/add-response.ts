import {
  Auth0User, getChatRedisClient,getT<PERSON><PERSON>, TWILIO_PHONE_NUMBER,
} from "@divinci-ai/server-globals";

import {
  TranscriptMessage, condenseTarget, TRANSCRIPT_LOCATION,
} from "@divinci-ai/models";

import { broadcastMessengerMessage, broadcastTextMessage } from "./broadcast";

const { twilioClient } = getTwilio();
const { chatRedisClient } = getChatRedisClient();

import { ITranscriptModelType, MessageContext, MessageEnvironment } from "../../../types";
import { unlockTranscript } from "./lock";

const PATH_CHAT_INDEX = "/transcript";

export async function addResponseMessage<Response>(
  transcript: InstanceType<ITranscriptModelType>,
  msgContext: MessageContext,
  msgEnv: MessageEnvironment,
  request: { message: TranscriptMessage },
  response: { id: string, raw: Response, text: string },
  buildUrl: (message: TranscriptMessage)=>string
){

  const { assistantAPI, customAssistant } = msgEnv.assistant;

  const { category, assistantName: baseAssistantName } = assistantAPI.info;

  const assistantName = customAssistant || baseAssistantName;

  const message: TranscriptMessage = {

    release: !msgContext.release ? void 0 : msgContext.release._id.toString(),

    _id: response.id,
    replyTo: request.message._id,
    role: "assistant",
    content: response.text,
    emojis: new Map(),
    timestamp: Date.now(),

    name: assistantName,
    category: category,
    metadata: [],
    context: [],
  };

  const TranscriptModel = transcript.constructor as ITranscriptModelType;
  const updated = await TranscriptModel.findByIdAndUpdate(
    transcript._id,
    {
      $push: { messages: message },
      awaitingResponse: false,
    },
    { returnDocument: "after" },
  );

  chatRedisClient.publish(
    PATH_CHAT_INDEX + "/" + transcript._id,
    JSON.stringify(updated),
  ).catch((e) => { console.error("Redis Publish Error", "response", "/transcript/" + transcript._id.toString(), e); });

  const target = condenseTarget({
    ...TRANSCRIPT_LOCATION,
    id: transcript._id.toString(),
  });
  broadcastMessengerMessage([], target, assistantAPI, response, buildUrl(message));

  return {
    transcript: updated,
    message,
  };
}

export async function addResponseErrorMessage(
  transcript: InstanceType<ITranscriptModelType>,
  msgContext: MessageContext,
  msgEnv: MessageEnvironment,
  { user, phoneNumber, message }: { user: Auth0User, phoneNumber: undefined | string, message: TranscriptMessage },
  response: { id: string },
  buildUrl: (message: TranscriptMessage)=>string,
){
  const { assistantAPI, customAssistant } = msgEnv.assistant;
  const { assistantName: baseAssistantName } = assistantAPI.info;
  const assistantName = customAssistant || baseAssistantName;

  const responseMessage: TranscriptMessage = {
    release: !msgContext.release ? void 0 : msgContext.release._id.toString(),
    replyTo: message._id,

    _id: response.id,
    role: "error",
    category: "text",
    emojis: new Map(),
    content: "❌ Failed Comunicating with AI.",
    name: assistantName,
    timestamp: Date.now(),
    metadata: [],
    context: [],
  };
  const updated = await unlockTranscript(transcript, message);

  chatRedisClient.publish(
    PATH_CHAT_INDEX + "/" + transcript._id,
    JSON.stringify(updated),
  );

  const target = condenseTarget({
    ...TRANSCRIPT_LOCATION,
    id: transcript._id.toString(),
  });
  broadcastTextMessage(
    phoneNumber ? [phoneNumber] : [],
    target,
    `Message from ${user.nickname} could not be completed. 🙃`,
    buildUrl(responseMessage),
  );
  if(phoneNumber){
    twilioClient.messages.create({
      from: TWILIO_PHONE_NUMBER,
      to: phoneNumber,
      body: "🙃 Sorry but you're request could not be completed.",
    });
  }
}
