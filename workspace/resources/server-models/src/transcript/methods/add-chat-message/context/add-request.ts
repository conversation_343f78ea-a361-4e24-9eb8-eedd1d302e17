import { getChatRedisClient } from "@divinci-ai/server-globals";
import { uniqueId, logDebug } from "@divinci-ai/utils";

import {
  TranscriptMessageMetaData,
  condenseTarget,
  TranscriptMessage,
  TRANSCRIPT_LOCATION,
  MessageContextItem,
} from "@divinci-ai/models";
import { AIAssistantAPI } from "@divinci-ai/server-tools";
import { broadcastTextMessage } from "./broadcast";
const { chatRedisClient } = getChatRedisClient();

import { AIUser } from "./types";
import { ITranscriptModelType, MessageContext } from "../../../types";

import { lockTranscript, unlockTranscript } from "./lock";
import { IRagVectorModelType } from "../../../../white-label/rag/custom-vector/types";
import { IMessagePrefixModelType, MessageEnvironment } from "../../../../white-label";

export async function addRequestMessage(
  transcript: InstanceType<ITranscriptModelType>,
  msgContext: MessageContext,
  msgEnv: MessageEnvironment,
  contextMessages: Array<MessageContextItem>,
  request: AIUser & { content: string },
  buildUrl: (message: TranscriptMessage) => string,
){
  const { assistant: assistantConfig, ragVectorIndex, messagePrefix } = msgEnv;
  const { assistantAPI, customAssistant } = assistantConfig;
  const { category, assistantName: baseAssistantName } = assistantAPI.info;

  const assistantName = customAssistant || baseAssistantName;

  const { user, content, phoneNumber } = request;
  const id = uniqueId();

  const metadata: Array<TranscriptMessageMetaData> = [];

  logDebug("👤🙋🏻‍♂️ transcript addRequestMessage user: ", JSON.stringify(user));
  logDebug(
    "👤🙋🏻‍♂️ transcript addRequestMessage content: ",
    JSON.stringify(content),
  );

  const isLocked = await lockTranscript(transcript, assistantName, msgContext.replyTo);
  if(!isLocked){
    throw new Error(" 🔒 Chat is already locked. ");
  }

  const userMessage: TranscriptMessage = {

    release: !msgContext.release ? void 0 : msgContext.release._id.toString(),
    replyTo: msgContext.replyTo,

    _id: id,
    role: "user",
    category,
    content,
    name: user.user_id,
    timestamp: Date.now(),
    metadata,
    emojis: new Map(),
    msgPrefix: messagePrefix as any,
    context: [],
  };

  const { updated } = await handleNewMessage(
    transcript,
    assistantAPI,
    ragVectorIndex,
    messagePrefix,
    contextMessages,
    userMessage,
  );

  logDebug(
    "👤🙋🏻‍♂️ transcript addRequestMessage updated:",
    JSON.stringify(updated),
  );

  chatRedisClient
    .publish(
      "/transcript/" + transcript._id.toString(),
      JSON.stringify(updated),
    )
    .catch((e: any)=>{
      console.error(
        "Redis Publish Error",
        "request",
        "/transcript/" + transcript._id.toString(),
        e,
      );
    });

  const target = condenseTarget({
    ...TRANSCRIPT_LOCATION,
    id: transcript._id.toString(),
  });
  broadcastTextMessage(
    phoneNumber ? [phoneNumber] : [],
    target,
    `From ${user.nickname}\n\n` + content,
    buildUrl(userMessage),
  );

  return {
    message: userMessage,
    transcript: updated,
    contextMessages,
  };
}

async function handleNewMessage(
  transcript: InstanceType<ITranscriptModelType>,
  assistantAPI: AIAssistantAPI<any, any>,
  ragVertexIndex: undefined | InstanceType<IRagVectorModelType>,
  messagePrefix: undefined | InstanceType<IMessagePrefixModelType>,
  contextMessages: Array<MessageContextItem>,
  newMessage: TranscriptMessage,
){

  try {
    if(!assistantAPI.useContext || !ragVertexIndex){
      contextMessages = [];
    }

    // Get message prefix if it exists
    newMessage.context = [...contextMessages];

    logDebug("🛰️💬 handleNewMessage: messagePrefix: ", messagePrefix);

    if(messagePrefix?._id) {
      // Add message prefix as a system message to the context
      newMessage.msgPrefix = {
        _id: messagePrefix._id.toString(),
        prefix: messagePrefix.prefix
      };
    }

    logDebug("🛰️💬 handleNewMessage: newMessage: ", newMessage);

    const updated = await pushToTranscript(transcript, [newMessage]);

    if(updated === null) {
      throw new Error("🤷🏻‍♂️ Unable to update chat, possibly deleted.");
    }

    logDebug(
      "👤🙋🏻‍♂️🥞 transcript handleNewMessage updated: ",
      JSON.stringify(updated),
    );

    return { updated, contextMessages: contextMessages };
  }catch(e) {
    await unlockTranscript(transcript);
    throw e;
  }
}

function pushToTranscript(
  transcript: InstanceType<ITranscriptModelType>,
  messages: Array<TranscriptMessage>,
){
  const TranscriptModel = transcript.constructor as ITranscriptModelType;
  return TranscriptModel.findOneAndUpdate(
    { _id: transcript._id },
    {
      $push: { messages: { $each: messages } },
    },
    {
      returnDocument: "after",
    },
  );
}
