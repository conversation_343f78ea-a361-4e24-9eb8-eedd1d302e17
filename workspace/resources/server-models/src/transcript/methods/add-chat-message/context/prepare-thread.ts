import { TranscriptMessage } from "@divinci-ai/models";
import { IThreadPrefixModelType } from "../../../../white-label/thread-prefix";
import { ITranscriptModelType } from "../../../types";
import { AIAssistantAPI } from "@divinci-ai/server-tools";
import { logDebug } from "@divinci-ai/utils";

export async function prepareThread<T>(
  threadPrefix: undefined | InstanceType<IThreadPrefixModelType>,
  transcript: InstanceType<ITranscriptModelType>,
  assistant: AIAssistantAPI<T, any>,
  reqMessage: TranscriptMessage,
) {
  // const prefix = await getPrefixMessages(chat.whitelabel);

  const prefix = convertThreadPrefix(assistant, threadPrefix);
  const rawThread = await transcript.collectThread(reqMessage._id);
  return prefix.concat(await convertThread(assistant, rawThread));
}

export function convertThreadPrefix<T>(
  assistant: AIAssistantAPI<T, any>,
  threadPrefix: undefined | InstanceType<IThreadPrefixModelType>
): Array<T>{
  if(!threadPrefix) return [];
  if(threadPrefix.prefix.length === 0) return [];
  const messages: Array<T> = [];
  for(const rawPrefix of threadPrefix.prefix){
    const systemPrefix = assistant.createSystemMessage(rawPrefix);
    if(!systemPrefix) continue;
    messages.push(systemPrefix);
  }
  return messages;
}

/**
 * Converts a raw thread of TranscriptMessage objects into an array of type T using the provided AIMessenger.
 *
 * @param messenger The AIMessenger object used to convert the raw thread.
 * @param rawThread An array of TranscriptMessage objects representing the raw thread.
 * @returns An array of type T containing the converted thread messages.
 */
export async function convertThread<T>(
  assistant: AIAssistantAPI<T, any>,
  rawThread: Array<TranscriptMessage>,
){
  const chatThread: Array<T> = [];
  for(let i = 0; i < rawThread.length; i++){
    const item = rawThread[i];

    logDebug("📛⭐️ convertThread::item.msgPrefix: ", item.msgPrefix);
    logDebug("1️⃣⭐️ convertThread::item: ", item);

    if(typeof item.msgPrefix !== "undefined") {
      const messagePrefix = assistant.createSystemMessage(
        item.msgPrefix.prefix,
      );
      messagePrefix && chatThread.push(messagePrefix);
    }

    if(typeof item.context !== "undefined" && item.context.length > 0) {
      const messages = assistant.contextToInternal(
        item.context.map((context) => {
          return { content: context.content };
        }),
      );
      chatThread.push(...messages);
    }
    chatThread.push(assistant.chatMessageToInternal(item));
  }
  return chatThread;
}
