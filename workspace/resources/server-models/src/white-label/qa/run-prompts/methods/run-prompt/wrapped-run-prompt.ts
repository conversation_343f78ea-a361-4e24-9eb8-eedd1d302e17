import { WhiteLabelModel } from "../../../../white-label/model";
import { IWhiteLabelQAPromptsModelType, QAEnvironment } from "../../types";
import { HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import {
  TRANSCRIPT_MESSAGE_CATEGORY,
  TranscriptMessage,
  TranscriptMessageMetaData,
  MessageContextItem,
  condenseTarget,
  WHITE_LABEL_LOCATION,
} from "@divinci-ai/models";

import {
  TranscriptValueWalletModel,
  TranscriptValueTransactionModel,
  WalletConfig,
} from "../../../../../money";
import { convertThreadPrefix, convertThread } from "../../../../../transcript/methods/add-chat-message/context/prepare-thread";
import { PAYOUT_CONFIG } from "../../../../../transcript";

import { WhiteLabelReleaseModel } from "../../../../release";
import { PromptModeratorModel } from "../../../../prompt-moderation";

export async function runPrompt(
  qaDoc: InstanceType<IWhiteLabelQAPromptsModelType>,
  runnerUserId: string,
  promptId: string,

  envConfig: QAEnvironment
) {
  const prompt = qaDoc.prompts.find((prompt) => prompt.id === promptId);
  if(typeof prompt === "undefined") {
    throw HTTP_ERRORS.NOT_FOUND;
  }

  const whitelabel = await WhiteLabelModel.findById(qaDoc.whitelabel);
  if(whitelabel === null) {
    throw HTTP_ERRORS.NOT_FOUND;
  }
  const wallet = await TranscriptValueWalletModel.findOrCreate(whitelabel.ownerUser);

  const target = condenseTarget({
    ...WHITE_LABEL_LOCATION,
    id: whitelabel._id.toString(),
  });

  const environment = await (async ()=>{
    switch(envConfig.type){
      case "config": {
        return WhiteLabelReleaseModel.resolveMessageEnvironment(target, envConfig.value);
      }
      case "release": {
        const release = await WhiteLabelReleaseModel.findById(envConfig.value);
        if(release === null){
          throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("Release not found");
        }
        if(release.whitelabel !== whitelabel._id.toString()){
          throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND("Release not found");
        }
        return release.getMessageEnvironment();
      }
    }
  })();

  const { assistant, promptModeration, threadPrefix, messagePrefix, ragVectorIndex } = environment;
  const { assistantAPI, customAssistant } = assistant;

  const context: MessageContextItem[] = await (async function () {
    if(!assistantAPI.useContext) return [];
    if(!ragVectorIndex) return [];
    return await ragVectorIndex.getContextOfPrompt(prompt.prompt);
  })();

  await PromptModeratorModel.tryToPass(
    promptModeration, prompt.prompt, !ragVectorIndex ? null : context
  );


  const walletConfig: WalletConfig = {
    payer: {
      wallet,
      callback: (payment, inputOrOutput) =>
        TranscriptValueTransactionModel.useWhitelabelQA(
          runnerUserId,
          whitelabel,
          promptId,
          inputOrOutput,
          payment,
        ),
    },
  };

  let responseText: undefined | string;

  await TranscriptValueWalletModel.wrapTransaction(
    walletConfig,
    PAYOUT_CONFIG,
    async () => {
      const system: { content: string }[] = (messagePrefix ? [{ content: messagePrefix.prefix }] : []);
      system.push(...(context || []));
      return {
        value: assistantAPI.calculateInputValue(
          prompt.prompt,
          system.map(item => item.content),
        )
      };
    },
    async (maximumOutputValue) => {
      const rawThread: TranscriptMessage[] = [];

      rawThread.push(
        toTranscriptMessage(
          assistantAPI.info.category,
          prompt.prompt,
          messagePrefix ? { _id: messagePrefix._id.toString(), prefix: messagePrefix.prefix } : undefined,
          context,
        ),
      );
      const threadPrefixMessages = convertThreadPrefix(assistantAPI, threadPrefix);
      const thread = await convertThread(assistantAPI, rawThread);

      const response = await assistantAPI.generateValue(
        threadPrefixMessages.concat(thread),
        { id: "doesn't matter" },
        maximumOutputValue,
        customAssistant,
      );

      responseText = response.text;

      return {
        value: assistantAPI.calculateOutputValue(response),
      };
    },
  );

  // Include metadata along with vectorId and content in the context
  return { text: responseText, context } as {
    text: string;
    context: MessageContextItem[];
  };
}

function toTranscriptMessage(
  category: TRANSCRIPT_MESSAGE_CATEGORY,
  prompt: string,
  msgPrefix: { _id: string, prefix: string } | undefined,
  context: MessageContextItem[],
): TranscriptMessage {
  return {
    release: void 0,
    _id: "doesn't matter",
    role: "user",
    category: category,
    name: "doesn't matter",
    timestamp: Date.now(),
    metadata: [] as TranscriptMessageMetaData[],
    emojis: new Map(),

    replyTo: void 0,
    msgPrefix,
    context: context, // Include metadata here
    content: prompt,
  };
}
