import { IPromptModeratorModelType as IModelType } from "../types";
import { getOpenAI, OPENAI_MODERATION_VERSION } from "@divinci-ai/server-globals";

export const checkHarmful: IModelType["checkHarmful"] = async function(
  config, prompt
){
  if(!config.on) return true;
  const { openai } = getOpenAI();

  const moderationResponse = await openai.moderations.create({
    model: OPENAI_MODERATION_VERSION,
    input: prompt
  });

  if(moderationResponse.results.some((result)=>(result.flagged))){
    console.error({
      message: "was flagged by moderation",
      results: JSON.parse(JSON.stringify(moderationResponse.results))
    });
    throw new Error(config.title);
  }
  return true;
};
