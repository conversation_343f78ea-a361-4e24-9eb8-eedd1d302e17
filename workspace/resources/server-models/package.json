{"name": "@divinci-ai/server-models", "version": "1.3.69", "description": "", "main": "dist/index.js", "typings": "src/index.ts", "scripts": {"build": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "build:ci": "tsc --skipL<PERSON><PERSON><PERSON><PERSON> --project tsconfig.ci.json", "build:ignore-errors": "tsc --skipLib<PERSON>heck --noEmit || echo 'TypeScript errors ignored'", "build:ignore-errors:ci": "tsc --skipLib<PERSON>heck --noEmit --project tsconfig.ci.json || echo 'TypeScript errors ignored'", "prepare": "rimraf ./dist && tsc --skipLibCheck", "test": "vitest run", "test:watch": "vitest", "test:perf": "vitest run --config vitest.perf.config.ts", "test:security": "vitest run --config vitest.sec.config.ts", "test:all": "pnpm test:vitest && pnpm test:perf && pnpm test:security", "migrate:transcript": "ts-node-dev src/migration/migrate-to-transcript"}, "author": "", "license": "JSON", "devDependencies": {"@types/async": "^3.2.24", "@types/body": "^5.1.4", "@types/busboy": "^1.5.4", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.11", "@types/node": "^22.5.2", "@types/pg": "^8.11.8", "@types/stream-json": "^1.7.7", "@types/stream-to-blob": "^2.0.0", "@types/node-fetch": "^2.6.11", "dotenv": "^16.4.5", "jest": "^29.7.0", "rimraf": "^6.0.1", "ts-jest": "^29.2.5", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3", "vitest": "^3.1.1"}, "dependencies": {"@aws-sdk/client-s3": "^3.750.0", "@aws-sdk/lib-storage": "^3.750.0", "@aws-sdk/s3-request-presigner": "^3.740.0", "@divinci-ai/models": "file:../models", "@divinci-ai/server-globals": "file:../server-globals", "@divinci-ai/server-tools": "file:../server-tools", "@divinci-ai/server-utils": "file:../server-utils", "@divinci-ai/tools": "file:../tools", "@divinci-ai/utils": "file:../utils", "@types/node-fetch": "^2.6.11", "form-data": "^4.0.1", "gpt-tokens": "^1.3.12", "mime-types": "^2.1.35", "mongoose": "^8.8.3", "node-fetch": "^3.3.2", "node-fetch-commonjs": "^3.3.2", "pg": "^8.14.1", "prompt-engine": "^0.0.31", "stream-json": "^1.8.0"}, "engines": {"node": ">=20", "pnpm": ">=10"}, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b"}