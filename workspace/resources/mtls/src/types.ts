/**
 * Common types and interfaces for mTLS implementation
 */

import { Server as HTTPServer } from "http";
import { Server as HTTPSServer } from "https";
import { Agent as HTTPSAgent } from "https";

/**
 * Certificate type (server or client)
 */
export type CertificateType = "server" | "client";

/**
 * File type (cert or key)
 */
export type FileType = "cert" | "key";

/**
 * Environment type
 */
export type Environment = "local" | "develop" | "staging" | "production";

/**
 * mTLS server options
 */
export interface MTLSServerOptions {
  /**
   * Whether to enable mTLS
   * @default false
   */
  enableMTLS?: boolean,

  /**
   * Whether to verify client certificates
   * @default false
   */
  verifyClient?: boolean,

  /**
   * Custom certificate directory
   */
  certDir?: string,

  /**
   * Custom server certificate path
   */
  serverCertPath?: string,

  /**
   * Custom server key path
   */
  serverKeyPath?: string,

  /**
   * Custom CA certificate path for verifying client certificates
   */
  caCertPath?: string,

  /**
   * Environment (local, develop, staging, production)
   * @default process.env.ENVIRONMENT || 'local'
   */
  environment?: Environment,

  /**
   * Whether to log debug information
   * @default false
   */
  debug?: boolean,
}

/**
 * mTLS client options
 */
export interface MTLSClientOptions {
  /**
   * Whether to enable mTLS
   * @default false
   */
  enableMTLS?: boolean,

  /**
   * Custom certificate directory
   */
  certDir?: string,

  /**
   * Custom client certificate path
   */
  clientCertPath?: string,

  /**
   * Custom client key path
   */
  clientKeyPath?: string,

  /**
   * Custom CA certificate path for verifying server certificates
   */
  caCertPath?: string,

  /**
   * Whether to verify server certificates
   * @default true
   */
  verifyServer?: boolean,

  /**
   * Environment (local, develop, staging, production)
   * @default process.env.ENVIRONMENT || 'local'
   */
  environment?: Environment,

  /**
   * Whether to log debug information
   * @default false
   */
  debug?: boolean,
}

/**
 * mTLS fetch options
 */
export interface MTLSFetchOptions extends RequestInit {
  /**
   * mTLS client options
   */
  mtlsOptions?: MTLSClientOptions,
}

/**
 * mTLS server result
 */
export interface MTLSServerResult {
  /**
   * The created server (HTTP or HTTPS)
   */
  server: HTTPServer | HTTPSServer,

  /**
   * Whether mTLS is enabled
   */
  mtlsEnabled: boolean,

  /**
   * Whether client verification is enabled
   */
  clientVerificationEnabled: boolean,
}

/**
 * mTLS HTTPS agent options
 */
export interface MTLSAgentOptions {
  /**
   * Client certificate content
   */
  cert?: string,

  /**
   * Client key content
   */
  key?: string,

  /**
   * CA certificate content for verifying server certificates
   */
  ca?: string,

  /**
   * Whether to reject unauthorized servers
   * @default true
   */
  rejectUnauthorized?: boolean,
}

/**
 * mTLS HTTPS agent result
 */
export interface MTLSAgentResult {
  /**
   * The created HTTPS agent
   */
  agent: HTTPSAgent,

  /**
   * Whether mTLS is enabled
   */
  mtlsEnabled: boolean,
}
