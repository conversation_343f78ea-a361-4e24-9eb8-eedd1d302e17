{"name": "@divinci-ai/mtls", "version": "0.1.0", "description": "Mutual TLS (mTLS) utilities for Divinci services", "main": "dist/index.js", "types": "src/index.ts", "scripts": {"build": "tsc", "test": "jest --passWithNoTests", "lint": "eslint src --ext .ts", "prepare": "npm run build"}, "keywords": ["mtls", "security", "tls", "https"], "author": "Divinci AI", "license": "JSON", "dependencies": {"@divinci-ai/server-globals": "workspace:*", "@divinci-ai/server-utils": "workspace:*", "@divinci-ai/utils": "workspace:*"}, "devDependencies": {"@types/node": "^20.11.0", "typescript": "^5.3.3"}}