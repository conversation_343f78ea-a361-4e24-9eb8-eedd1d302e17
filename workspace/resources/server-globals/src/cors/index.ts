import { DIVINCI_DOMAINS, CORS_PORTS } from "./domains-from-env";
export { DIVINCI_DOMAINS, CORS_PORTS };

import { CorsOptions } from "cors";
import { DIVINCI_ORGANIZATION_HEADER } from "@divinci-ai/models";
import { IncomingMessage, ServerResponse } from "http";

export function createCorsOptions(): CorsOptions {
  const isCloudEnvironment =
    process.env.ENVIRONMENT !== "local" && process.env.ENVIRONMENT !== "dev";
  const standardPorts = [80, 8080, 443];

  const generateOrigins = (domains: string[], ports: number[]) => {
    return domains.flatMap((domain) => {
      return ports.map((port) => {
        return port === 80
          ? `http://${domain}`
          : port === 443
          ? `https://${domain}`
          : `http://${domain}:${port}`;
      });
    });
  };

  // For local development, we need to allow various localhost ports
  const localPorts = [
    8080, 8081, 8082, 8083, 8084, 9080, 9081, 9082, 9083, 9084,
  ];

  const allowedOrigins = [
    ...generateOrigins(DIVINCI_DOMAINS, standardPorts),
    ...(isCloudEnvironment ? [] : generateOrigins(["localhost"], localPorts)),
  ];

  const corsOptions: CorsOptions = {
    origin: function (
      origin: string | undefined,
      callback: (error: Error | null, allow?: boolean) => void
    ) {
      console.log(`🔍 CORS debug: Checking origin: "${origin}"`);
      console.log(`🔍 CORS debug: isCloudEnvironment: ${isCloudEnvironment}`);
      console.log(
        `🔍 CORS debug: Environment: ${process.env.ENVIRONMENT || "not set"}`
      );

      // Always allow OPTIONS requests regardless of origin
      if ((this as any)?.req?.method === "OPTIONS") {
        console.log("✅ CORS: Allowing OPTIONS request regardless of origin");
        callback(null, true);
        return;
      }

      // Case 1: No origin (like server-to-server requests)
      if (origin === undefined) {
        // console.log("✅ CORS: Allowing request with no origin");
        callback(null, true);
        return;
      }

      // Case 2: Matches *.divinci.app pattern
      if (/^https:\/\/(.*\.)?divinci\.app$/.test(origin)) {
        // console.log(`✅ CORS: Allowing divinci.app subdomain: ${origin}`);
        callback(null, true);
        return;
      }

      // Case 2.1: Explicitly allow chat.stage.divinci.app (for redundancy)
      if (origin === "https://chat.stage.divinci.app") {
        // console.log(`✅ CORS: Explicitly allowing chat.stage.divinci.app`);
        callback(null, true);
        return;
      }

      // Case 3: Localhost in local environment (with or without port)
      if (!isCloudEnvironment && /^http:\/\/localhost(:\d+)?$/.test(origin)) {
        // console.log(`✅ CORS: Allowing localhost in local environment: ${origin}`);
        callback(null, true);
        return;
      }

      // Case 3.1: Explicitly allow localhost:8080 for local development
      if (!isCloudEnvironment && origin === "http://localhost:8080") {
        // console.log(`✅ CORS: Explicitly allowing localhost:8080 for local development`);
        callback(null, true);
        return;
      }

      // Case 3.2: Allow GitHub Codespaces domains (*.app.github.dev) in local environment
      if (
        !isCloudEnvironment &&
        /^https:\/\/.*\.app\.github\.dev$/.test(origin)
      ) {
        console.log(`✅ CORS: Allowing GitHub Codespaces domain: ${origin}`);
        callback(null, true);
        return;
      }

      // Case 4: Explicitly allowed origin
      if (allowedOrigins.indexOf(origin) !== -1) {
        console.log(
          `✅ CORS: Allowing explicitly configured origin: ${origin}`
        );
        callback(null, true);
        return;
      }

      // If we get here, the origin is not allowed
      console.log(`❌ CORS: Denying origin: ${origin}`);
      console.log(`   Environment: ${process.env.ENVIRONMENT || "not set"}`);
      console.log(`   Is local check: ${!isCloudEnvironment}`);
      console.log(`   allowedOrigins:`, allowedOrigins.slice(0, 10)); // Show first 10 for debugging
      callback(new Error(`🚨 Origin: ${origin} is not allowed by CORS`));
    },
    // origin: '*',
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"], // to works well with web app, OPTIONS is not usually needed
    credentials: true, // required to let cookies or authorization headers through in either direction
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      DIVINCI_ORGANIZATION_HEADER,
      "Origin",
      "x-access-token",
      // Add lowercase headers
      "x-file-name",
      "x-file-id",
      "x-target",
      "x-processor",
      "x-vectorize-config",
      "x-processor-config",
      "x-debug-client",
      "divinci-organization",
      // Add Cloudflare Access headers
      "CF-Access-Client-Id",
      "CF-Access-Client-Secret",
      // Add Cloudflare Worker dev auth headers
      "cloudflare-worker-x-dev-auth",
      "x-worker-local-dev",
    ], // X-Requested-With - if you're using it client side, some legacy browsers clash with CORS features and it gets stripped
    // allowedHeaders: '*',
    preflightContinue: false, // stops the browser from sending an extra leading OPTIONS request
    optionsSuccessStatus: 204, // IE11 choke on 204 No Content
    exposedHeaders: [
      "x-file-name",
      "x-file-id",
      "x-target",
      "x-processor",
      "x-vectorize-config",
      "x-processor-config",
      "x-debug-client",
      "divinci-organization",
      // Add Cloudflare Worker dev auth headers
      "cloudflare-worker-x-dev-auth",
      "x-worker-local-dev",
    ],
  };

  return corsOptions;
}

/**
 * Apply CORS headers to a response
 * This function can be used directly in HTTP/HTTPS server request handlers
 *
 * @param req The incoming request
 * @param res The server response
 * @returns true if the origin is allowed, false otherwise
 */
export function applyCorsHeaders(
  req: IncomingMessage,
  res: ServerResponse
): boolean {
  // Normalize origin and provide fallbacks when reverse proxies strip the Origin header
  let origin = req.headers.origin as string | undefined;
  // If Origin is missing, try common forwarded headers (Codespaces / proxies may set these)
  if (!origin) {
    const xfHost = (req.headers["x-forwarded-host"] || req.headers["host"]) as
      | string
      | undefined;
    const referer = req.headers["referer"] as string | undefined;
    if (xfHost && typeof xfHost === "string") {
      // Prefer https scheme for Codespaces / app.github.dev hosts
      origin = xfHost.startsWith("http") ? xfHost : `https://${xfHost}`;
    } else if (referer) {
      try {
        const u = new URL(referer);
        origin = `${u.protocol}//${u.hostname}${u.port ? ":" + u.port : ""}`;
      } catch (e) {
        // ignore parse errors
      }
    }
  }
  const isCloudEnvironment =
    process.env.ENVIRONMENT !== "local" &&
    process.env.ENVIRONMENT !== "dev" &&
    process.env.ENVIRONMENT !== "development";
  const method = req.method;

  // Always allow OPTIONS requests
  if (method === "OPTIONS") {
    // Debug log headers for OPTIONS so we can see what the browser sent vs what the server received
    try {
      console.log(
        "🔍 CORS OPTIONS request headers:",
        JSON.stringify(req.headers)
      );
    } catch (e) {
      /* ignore */
    }

    // Set CORS headers for OPTIONS requests
    // Use the derived origin where possible. If still missing, try to fall back to Host-derived origin
    if (origin) {
      res.setHeader("Access-Control-Allow-Origin", origin);
    } else {
      // If we couldn't derive an origin, fallback to the Host header if present
      const hostHdr = req.headers["host"] as string | undefined;
      if (hostHdr) {
        const hostOrigin = hostHdr.startsWith("http")
          ? hostHdr
          : `https://${hostHdr}`;
        res.setHeader("Access-Control-Allow-Origin", hostOrigin);
      } else {
        // As a last resort, set wildcard (note: with credentials this will be ignored by browsers)
        res.setHeader("Access-Control-Allow-Origin", "*");
      }
    }
    res.setHeader(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, OPTIONS, PATCH"
    );
    res.setHeader(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization, cloudflare-worker-x-dev-auth, x-worker-local-dev"
    );
    res.setHeader("Access-Control-Allow-Credentials", "true");
    res.setHeader("Access-Control-Max-Age", "86400"); // 24 hours
    res.setHeader(
      "Access-Control-Expose-Headers",
      "x-file-name, x-file-id, x-target, x-processor, x-vectorize-config, x-processor-config, x-debug-client, divinci-organization, cloudflare-worker-x-dev-auth, x-worker-local-dev"
    );

    // Make sure we don't set a status code that might conflict with other middleware
    // Use 204 No Content for OPTIONS requests
    if (!res.headersSent) {
      res.writeHead(204);
      res.end();
    }

    return true;
  }

  // For non-OPTIONS requests, always set CORS headers if there's an origin
  if (origin) {
    res.setHeader("Access-Control-Allow-Origin", origin);
    res.setHeader("Access-Control-Allow-Credentials", "true");
  }

  // For non-OPTIONS requests, check if origin is allowed
  let isAllowed = false;
  console.log(`🔍 applyCorsHeaders: Checking origin: "${origin}"`);
  console.log(`🔍 applyCorsHeaders: isCloudEnvironment: ${isCloudEnvironment}`);

  // Case 1: No origin (like server-to-server requests)
  if (origin === undefined) {
    console.log("✅ applyCorsHeaders: Allowing request with no origin");
    isAllowed = true;
  }
  // Case 2: Matches *.divinci.app pattern
  else if (/^https:\/\/(.*\.)?divinci\.app$/.test(origin)) {
    console.log("✅ applyCorsHeaders: Allowing divinci.app subdomain");
    isAllowed = true;
  }
  // Case 2.1: Explicitly allow chat.stage.divinci.app (for redundancy)
  else if (origin === "https://chat.stage.divinci.app") {
    console.log(
      "✅ applyCorsHeaders: Explicitly allowing chat.stage.divinci.app"
    );
    isAllowed = true;
  }
  // Case 3: Localhost in local environment (with or without port)
  else if (!isCloudEnvironment && /^http:\/\/localhost(:\d+)?$/.test(origin)) {
    console.log("✅ applyCorsHeaders: Allowing localhost in local environment");
    isAllowed = true;
  }
  // Case 3.1: Explicitly allow localhost:8080 for local development
  else if (!isCloudEnvironment && origin === "http://localhost:8080") {
    console.log("✅ applyCorsHeaders: Explicitly allowing localhost:8080");
    isAllowed = true;
  }
  // Case 3.2: Allow GitHub Codespaces domains (*.app.github.dev) in local environment
  else if (
    !isCloudEnvironment &&
    /^https:\/\/.*\.app\.github\.dev$/.test(origin)
  ) {
    console.log("✅ applyCorsHeaders: Allowing GitHub Codespaces domain");
    isAllowed = true;
  }

  // Apply CORS headers based on whether the origin is allowed
  if (origin && isAllowed) {
    // Set standard CORS headers
    res.setHeader("Access-Control-Allow-Origin", origin);
    res.setHeader(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, OPTIONS, PATCH"
    );
    res.setHeader(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization, cloudflare-worker-x-dev-auth, x-worker-local-dev"
    );
    res.setHeader("Access-Control-Allow-Credentials", "true");
    res.setHeader("Access-Control-Max-Age", "86400"); // 24 hours
    res.setHeader(
      "Access-Control-Expose-Headers",
      "x-file-name, x-file-id, x-target, x-processor, x-vectorize-config, x-processor-config, x-debug-client, divinci-organization, cloudflare-worker-x-dev-auth, x-worker-local-dev"
    );
  } else if (!origin) {
    // For requests without origin (like server-to-server), we still set CORS headers
    // But we don't set Access-Control-Allow-Origin
    res.setHeader(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, OPTIONS, PATCH"
    );
    res.setHeader(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization, Origin, X-Debug-Client, CF-Access-Client-Id, CF-Access-Client-Secret, divinci-organization, cloudflare-worker-x-dev-auth, x-worker-local-dev"
    );
    res.setHeader("Access-Control-Allow-Credentials", "true");
    res.setHeader("Access-Control-Max-Age", "86400"); // 24 hours
    res.setHeader(
      "Access-Control-Expose-Headers",
      "x-file-name, x-file-id, x-target, x-processor, x-vectorize-config, x-processor-config, x-debug-client, divinci-organization, cloudflare-worker-x-dev-auth, x-worker-local-dev"
    );
  }

  return isAllowed;
}
