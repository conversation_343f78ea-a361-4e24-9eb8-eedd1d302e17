import { JSON_Unknown, JSON_Object } from "./types";

export function clone<T extends JSON_Unknown>(json: T): T{
  return JSON.parse(JSON.stringify(json));
}

export function deepEqual(a: JSON_Unknown, b: JSON_Unknown): boolean {
  if(typeof a !== "object") return a === b;
  if(typeof b !== "object") return false;
  if(a === null) return a === b;
  if(b === null) return false;
  if(Array.isArray(a) && Array.isArray(b)){
    if(a.length !== b.length) return false;
    return !a.some((item, index)=>(!deepEqual(item, b[index])));
  }
  if(Array.isArray(a) || Array.isArray(b)){
    return false;
  }
  const akeys = Object.keys(a);
  const bkeys = Object.keys(b);
  if(akeys.length !== bkeys.length) return false;
  return !akeys.some((key)=>{
    if(!(key in b)) return true;
    return !deepEqual(a[key], b[key]);
  });
}

export function deepApproximated(a: JSON_Unknown,b: JSON_Unknown): boolean {
  if(typeof a !== "object") return a === b;
  if(typeof b !== "object") return false;
  if(a === null) return a === b;
  if(b === null) return false;
  if(Array.isArray(a) && Array.isArray(b)){
    if(a.length !== b.length) return false;
    return !a.some((item)=>(!b.some((v)=>(deepApproximated(item, v)))));
  }
  if(Array.isArray(a) || Array.isArray(b)){
    return false;
  }
  const akeys = Object.keys(a);
  const bkeys = Object.keys(b);
  if(akeys.length !== bkeys.length) return false;
  return !akeys.some((key)=>{
    if(!(key in b)) return true;
    return !deepApproximated(a[key], b[key]);
  });
}



export function jsonGet(path: string, object: JSON_Object){
  try {
    return path.split(".").reduce((obj, pathPart: string)=>{
      if(typeof obj !== "object"){
        throw void 0;
      }
      if(!Array.isArray(obj)){
        return obj[pathPart];
      }
      const pathNumber = Number.parseInt(pathPart);
      if(Number.isNaN(pathNumber)){
        throw new Error("expected number for array, got " + pathPart);
      }
      return obj[pathNumber];
    }, object as JSON_Unknown);

  }catch(e){
    if(typeof e === "undefined"){
      return e;
    }
    throw e;
  }
}
