import { PublicToolInfo } from "@divinci-ai/models";
import { RawFileToChunks } from "../../types";
import { r2FileToChunkStream } from "./r2file-to-chunkstream";
import { rawStreamToChunkStream } from "./rawstream-to-chunkstream";

export const UnstructuredRawFileToChunks: { info: PublicToolInfo, api: RawFileToChunks } = {
  info: {
    id: "unstructured",
    url: "https://unstructured.io/",
    title: "Unstructured",
    picture: "/img/unstructured.avif",
    description: "Unstructured is a tool for converting raw files to text chunks.",
    org: "Unstructured",
    orgUrl: "https://unstructured.io/",
  },
  api: {
    transformFile: r2FileToChunkStream,
    transformStream: rawStreamToChunkStream,
  }
};
