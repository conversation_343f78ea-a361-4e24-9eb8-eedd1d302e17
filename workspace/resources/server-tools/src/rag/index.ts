import { Organizer } from "../util/Organizer";
import { RawFileToChunks } from "./types";
export const RAWFILE_TO_CHUNKS = new Organizer<RawFileToChunks>("Chunker");

import { OpenParseRawFileToChunks } from "./raw-to-chunks/open-parse";
RAWFILE_TO_CHUNKS.add(OpenParseRawFileToChunks.info, OpenParseRawFileToChunks.api);

import { UnstructuredRawFileToChunks } from "./raw-to-chunks/unstructured";
RAWFILE_TO_CHUNKS.add(UnstructuredRawFileToChunks.info, UnstructuredRawFileToChunks.api);


import { VectorIndex } from "./types";
export const VECTOR_INDEX = new Organizer<VectorIndex<any, any>>("Vector Index");

import { QDrant } from "./vector/qdrant";
VECTOR_INDEX.add(QDrant.info, QDrant.api);
import { CloudFlareV2 } from "./vector/cloudflare";
VECTOR_INDEX.add(CloudFlareV2.info, CloudFlareV2.api);

export * from "./types";
export * from "./vector-id";
export * from "./workflows";
export * from "./vector/cloudflare";
