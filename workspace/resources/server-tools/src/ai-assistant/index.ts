

import { AssistantOrganizer } from "./AssistantOrganizer";

export { AssistantOrganizer };
export { AIAssistantAPI } from "./types";

export const CHAT_AI_ASSISTANT = new AssistantOrganizer();

import { assistant as textAIAssistant_GPT4o } from "./generators/text/gpt/gpt-4o";
import { assistant as textAIAssistant_GPT4o_mini } from "./generators/text/gpt/gpt-4o-mini";
CHAT_AI_ASSISTANT.addAssistant(textAIAssistant_GPT4o, true);
CHAT_AI_ASSISTANT.addAssistant(textAIAssistant_GPT4o_mini);

import { assistant as imageAIAssistant_DALL_E_3 } from "./generators/image/dall-e-3";
import { assistant as imageAIAssistant_DALL_E_2 } from "./generators/image/dall-e-2";
CHAT_AI_ASSISTANT.addAssistant(imageAIAssistant_DALL_E_3, true);
CHAT_AI_ASSISTANT.addAssistant(imageAIAssistant_DALL_E_2);

import { assistant as diagramAIAssistant } from "./generators/diagram/mermaid";
CHAT_AI_ASSISTANT.addAssistant(diagramAIAssistant, true);

export const WHITE_LABEL_AI_ASSISTANT = new AssistantOrganizer();

WHITE_LABEL_AI_ASSISTANT.addAssistant(textAIAssistant_GPT4o, true);
WHITE_LABEL_AI_ASSISTANT.addAssistant(textAIAssistant_GPT4o_mini);
