import { handleFetch } from "@divinci-ai/utils";

import { PYANNOTE_APIKEY, PyannoteJobStatus } from "./pyannote-constants";
import { requireEnvVar } from "@divinci-ai/server-utils";

// Get the local Pyannote service URL
const DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL = requireEnvVar("DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL");

export async function startJob({ publicAudioUrl }: { publicAudioUrl: string }){
  // Check if we're in local mode
  const NODE_ENV = process.env.NODE_ENV || "";
  const ENVIRONMENT = process.env.ENVIRONMENT || "";
  const IS_LOCAL_MODE = NODE_ENV === "development" || NODE_ENV === "local" ||
                      ENVIRONMENT === "local" || ENVIRONMENT === "development";

  // For local mode, we need to send the URL to the Pyannote service
  if (IS_LOCAL_MODE) {
    console.log(`🔍 Using local Pyannote service at: ${DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL}`);

    try {
      // Send the URL to the local Pyannote service
      console.log(`📤 Sending URL to local Pyannote service: ${publicAudioUrl}`);

      // Use JSON request with URL instead of form upload
      const response = await fetch(DIVINCI_AUDIO_DIARIZER_PYANNOTE_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: publicAudioUrl
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Local Pyannote service error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      // Parse the response
      const diarization = await response.json();

      // Return a compatible response format
      return {
        jobId: "local-job",
        status: "succeeded" as PyannoteJobStatus,
        output: { diarization }
      };
    } catch (error) {
      console.error("❌ Error in local Pyannote processing:", error);
      throw error;
    }
  } else {
    // Use the external Pyannote API for non-local environments
    console.log(`🔍 Using external Pyannote API`);

    return await handleFetch(fetch(
      "https://api.pyannote.ai/v1/diarize",
      {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${PYANNOTE_APIKEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: publicAudioUrl
        })
      }
    )) as {
      "jobId": string,
      "status": PyannoteJobStatus,
      "output"?: {
        "diarization": any
      }
    };
  }
}
