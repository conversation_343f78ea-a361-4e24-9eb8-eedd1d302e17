import { S3 } from "@aws-sdk/client-s3";

export type R2Pointer = {
  s3: S3,
  originalName: string,
  Bucket: string,
  Key: string,
};

export type SpeakerSegment = { speaker: string, start: number, end: number };

export type SpeakerDiarization = {
  validMimetypes(): Array<string>,
  processFile(
    r2Pointer: R2Pointer,
    speakerConfig: { languageCode: string },
  ): Promise<Array<SpeakerSegment>>,
};

