{"compilerOptions": {"target": "es2022", "module": "nodenext", "moduleResolution": "nodenext", "types": ["node", "jest", "vitest/globals"], "lib": ["es2022", "dom"], "sourceMap": true, "outDir": "./dist", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "removeComments": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false}, "files": ["src/index.ts"], "include": ["./src/**/*"], "exclude": ["node_modules", "**/*.spec.ts"]}