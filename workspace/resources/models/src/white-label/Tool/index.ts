// Define the config types
export type UnstructuredConfig = {
  chunkingStrategy: "by_title" | "by_similarity" | "by_page" | "by_character",
  maxCharacters?: number,
  similarityThreshold?: number,
  includeOriginalElements?: boolean,
  multipageSections?: boolean,
};

export type EmbeddingsConfig = {
  provider: "ollama" | "openai" | "cloudflare" | "none",
  model?: string,
};

export type OpenParseConfig = {
  semantic: boolean,
  minTokens: number,
  maxTokens: number,
  chunkOverlap?: number,
  embeddingsProvider?: string,
  useTokens?: boolean,
  embeddings?: EmbeddingsConfig,
  relevanceThreshold?: number,
};

export type ChunkingConfig = {
  semantic_chunking?: boolean,
  skipDeJunk?: boolean,
  relevanceThreshold?: number,
  unstructured?: UnstructuredConfig,
  openparse?: OpenParseConfig,
};

export type PublicToolInfo = {
  id: string,
  title: string,
  description: string,
  picture?: string,
  defaultConfig?: ChunkingConfig,

  url: string,
  orgUrl: string,
  org: string,

  provider?: { name: string, url: string },

  deprecated?: boolean,
};

