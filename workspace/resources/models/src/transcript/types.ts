import { Doc } from "../util/Doc";

export const TRANSCRIPT_MESSAGE_CATEGORY_LIST = [
  "text",
  "image",
  "diagram",
  "audio",
  "video",
  "unknown",
];
export type TRANSCRIPT_MESSAGE_CATEGORY =
  | "text"
  | "image"
  | "diagram"
  | "audio"
  | "video"
  | "unknown";

export enum TRANSCRIPT_MESSAGE_CATEGORY_ENUM {
  TEXT = "text",
  IMAGE = "image",
  DIAGRAM = "diagram",
  AUDIO = "audio",
  VIDEO = "video",
  UNKNOWN = "unknown",
}

export type TRANSCRIPT_CATEGORY_AND_CONFIDENCE = {
  category: TRANSCRIPT_MESSAGE_CATEGORY;
  confidence: number;
};

export type Transcript = Doc<TranscriptProps>;

export type TranscriptProps = {
  awaitingResponse: boolean;
  messages: Array<TranscriptMessage>;
  titleSet?: boolean;
};

export type TranscriptMessageRole = "system" | "assistant" | "user" | "error";

// Define the base metadata type
export type BaseMessageContextMetadata = {
  id?: string;
};

// Define metadata for RAG context
export type MessageContextMetadata = BaseMessageContextMetadata & {
  originalName: string;
  fileObjectKey: string;
  tokenCount: number;
};

export type MessageContextItem = {
  content: string;
  metadata: MessageContextMetadata;
  vectorId: string;
};

export type TranscriptMessage = {
  _id: string;
  timestamp: number;

  release: undefined | string;
  category: TRANSCRIPT_MESSAGE_CATEGORY;
  replyTo?: string;
  name: string;

  role: TranscriptMessageRole;
  content: string;

  emojis?: Map<string, { count: number; users: string[] }>;
  metadata: Array<TranscriptMessageMetaData>;
  msgPrefix?: { _id: string, prefix: string }, // MessagePrefix
  context?: Array<MessageContextItem>; // Use specific metadata type
  editIds?: Array<string>; // Array to keep track of QA Testing Fine-Tune edits
};

// Message Categorization Metadata.
export type TranscriptMessageMetaData = {
  name: string;
  type: string;
  value: any;
  confidence?: number;
};

export type TranscriptInfo = {
  _id: string;
  numberMessages: number;
  lastUsage: number;
};

export enum CHAT_ROLES {
  ADMIN = "admin",
  EDITOR = "editor",
  VIEWER = "viewer",
  BANNED = "banned"
}

export type CHAT_ROLES_TYPE = keyof typeof CHAT_ROLES;
