import {
  API_IS_SECURE,
  API_HOST,
  API_LIVE_IS_SECURE,
  API_LIVE_HOST,
  API_MTLS_ENABLED
} from "./constants/api";
import { jsonResponse } from "@divinci-ai/utils";

// Enhanced debug logger for CORS and mTLS troubleshooting
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const log = (()=>{
  // Only log in non-production environments by default
  const isEnabled = process.env.NODE_ENV !== "production" || process.env.DEBUG_API === "true";

  // Create timestamp for logs
  const timestamp = ()=>new Date().toISOString();

  // Create a prefix for all logs
  const prefix = (level: string)=>`[${timestamp()}][API][${level}]`;

  // eslint-disable-next-line no-console
  const consoleLog = console.log.bind(console);
  // eslint-disable-next-line no-console
  const consoleWarn = console.warn.bind(console);
  // eslint-disable-next-line no-console
  const consoleError = console.error.bind(console);
  // eslint-disable-next-line no-console
  const consoleDir = console.dir.bind(console);

  return {
    // Regular logging levels
    log: (message: string, ...args: any[])=>{
      if(isEnabled) consoleLog(`${prefix("INFO")} ${message}`, ...args);
    },
    warn: (message: string, ...args: any[])=>{
      if(isEnabled) consoleWarn(`${prefix("WARN")} ${message}`, ...args);
    },
    error: (message: string, ...args: any[])=>{
      // Always log errors
      consoleError(`${prefix("ERROR")} ${message}`, ...args);
    },

    // Special logging categories
    cors: (message: string, ...args: any[])=>{
      if(isEnabled) consoleLog(`${prefix("CORS")} ${message}`, ...args);
    },
    mtls: (message: string, ...args: any[])=>{
      if(isEnabled) consoleLog(`${prefix("MTLS")} ${message}`, ...args);
    },
    request: (message: string, ...args: any[])=>{
      if(isEnabled) consoleLog(`${prefix("REQUEST")} ${message}`, ...args);
    },
    response: (message: string, ...args: any[])=>{
      if(isEnabled) consoleLog(`${prefix("RESPONSE")} ${message}`, ...args);
    },

    // Debug object properties
    debug: (label: string, obj: any)=>{
      if(isEnabled) {
        consoleLog(`${prefix("DEBUG")} ${label}:`);
        consoleDir(obj, { depth: null, colors: true });
      }
    }
  } as any;
})();

const API_ORIGIN = `http${API_IS_SECURE ? "s" : ""}://${API_HOST}`;
const API_LIVE_ORIGIN_SUFFIX = `${API_LIVE_IS_SECURE ? "s" : ""}://${API_LIVE_HOST}`;
export { API_ORIGIN };

export function apiUrl(path: Parameters<typeof fetch>[0]){
  if(path instanceof Request){
    throw new Error("Request not supported");
  }
  return new URL(path, API_ORIGIN);
}

export function apiLiveUrlHTTP(path: string){
  return new URL(path, "http" + API_LIVE_ORIGIN_SUFFIX);
}

export function apiLiveUrlWS(path: string){
  return new URL(path, "ws" + API_LIVE_ORIGIN_SUFFIX);
}

// Function to configure mTLS for Node.js environment
// This is separated to avoid affecting the main request flow
async function configureMtlsForNode(init: RequestInit): Promise<RequestInit>{
  log.mtls("Configuring mTLS for Node.js environment");

  try {
    // Import modules dynamically to avoid issues in browser environments
    log.mtls("Importing required Node.js modules");
    const modules = await Promise.all([
      import("https"),
      import("fs"),
      import("crypto")
    ]);

    const https = modules[0].default;
    const fs = modules[1].default;
    const { constants } = modules[2];
    log.mtls("Successfully imported Node.js modules");

    // Define possible certificate paths
    // The search order prioritizes standard paths, then environment variables, then local development paths
    const certPaths = [
      // Standard Linux SSL paths (recommended for production/staging)
      "/etc/ssl/client/client.crt",  // Primary recommended location
      "/etc/ssl/certs/client.crt",   // Legacy/alternative location

      // Environment variable path (for custom configurations)
      process.env.MTLS_CLIENT_CERT_PATH,

      // Local development paths (for development environments)
      "/private-keys/local/certs/mtls/client.crt",
      "/private-keys/staging/certs/mtls/client.crt"
    ].filter(Boolean); // Remove undefined/null values

    // Define possible key paths
    // The search order matches the certificate paths
    const keyPaths = [
      // Standard Linux SSL paths (recommended for production/staging)
      "/etc/ssl/private/client.key", // Primary recommended location

      // Environment variable path (for custom configurations)
      process.env.MTLS_CLIENT_KEY_PATH,

      // Local development paths (for development environments)
      "/private-keys/local/certs/mtls/client.key",
      "/private-keys/staging/certs/mtls/client.key"
    ].filter(Boolean); // Remove undefined/null values

    log.mtls("Searching for client certificates in these paths:", certPaths);
    log.mtls("Searching for client keys in these paths:", keyPaths);

    // Find valid certificate and key
    let certPath = null;
    let keyPath = null;

    // Check each certificate path
    for(const path of certPaths) {
      if(path && fs.existsSync(path)) {
        log.mtls(`Found client certificate at: ${path}`);
        try {
          // Verify the certificate is readable and in PEM format
          const certContent = fs.readFileSync(path, "utf8");
          if(certContent.includes("-----BEGIN CERTIFICATE-----")) {
            certPath = path;
            log.mtls(`Verified client certificate at ${path} is in PEM format`);
            break;
          } else {
            log.mtls(`Certificate at ${path} is not in PEM format, skipping`);
          }
        }catch(err) {
          log.mtls(`Error reading certificate at ${path}:`, err);
        }
      } else if(path) {
        log.mtls(`Certificate path ${path} does not exist`);
      }
    }

    // Check each key path
    for(const path of keyPaths) {
      if(path && fs.existsSync(path)) {
        log.mtls(`Found client key at: ${path}`);
        try {
          // Verify the key is readable and in PEM format
          const keyContent = fs.readFileSync(path, "utf8");
          if(keyContent.includes("-----BEGIN PRIVATE KEY-----") ||
             keyContent.includes("-----BEGIN RSA PRIVATE KEY-----")) {
            keyPath = path;
            log.mtls(`Verified client key at ${path} is in PEM format`);
            break;
          } else {
            log.mtls(`Key at ${path} is not in PEM format, skipping`);
          }
        }catch(err) {
          log.mtls(`Error reading key at ${path}:`, err);
        }
      } else if(path) {
        log.mtls(`Key path ${path} does not exist`);
      }
    }

    // Configure HTTPS agent if certificates are found
    if(certPath && keyPath) {
      log.mtls(`Using mTLS certificates: ${certPath}, ${keyPath}`);

      try {
        // Read certificate and key content
        const certContent = fs.readFileSync(certPath, "utf8");
        const keyContent = fs.readFileSync(keyPath, "utf8");

        // Log certificate details (first few characters only for security)
        log.mtls(`Certificate content starts with: ${certContent.substring(0, 40)}...`);
        log.mtls(`Key content starts with: ${keyContent.substring(0, 40)}...`);

        // Create a new init object to avoid modifying the original
        const newInit = { ...init };

        // Create HTTPS agent with mTLS certificates
        (newInit as any).agent = new https.Agent({
          cert: certContent,
          key: keyContent,
          rejectUnauthorized: true,
          secureOptions: constants.SSL_OP_NO_TLSv1_3
        });

        log.mtls("Successfully configured HTTPS agent with mTLS certificates");
        return newInit;
      }catch(err) {
        log.error("Error reading certificate or key files:", err);
      }
    } else {
      log.mtls("Could not find valid certificate and key pair");
      if(certPath) {
        log.mtls(`Found certificate at ${certPath} but no matching key`);
      } else if(keyPath) {
        log.mtls(`Found key at ${keyPath} but no matching certificate`);
      } else {
        log.mtls("No certificate or key found in any of the search paths");
      }
    }
  }catch(error) {
    log.error("Error setting up mTLS:", error);
  }

  log.mtls("Returning init object without mTLS configuration");
  return init;
}

export async function fetchApi(...args: Parameters<typeof fetch>): Promise<Response>{
  const href = apiUrl(args[0]).href;
  let init = args[1] || {};

  // Log request details
  log.request(`Preparing request to: ${href}`);
  log.debug("Request init object", init);

  // Add CORS debugging headers if in browser environment
  if(typeof window !== "undefined") {
    // Clone the headers to avoid modifying the original
    const headers = new Headers(init.headers || {});

    // Add a debug header to track requests in network tab
    headers.set("X-Debug-Client", "divinci-web-client");

    // Log CORS-related headers
    log.cors("Request URL:", href);
    log.cors("Origin:", window.location.origin);

    if(headers.has("Origin")) {
      log.cors("Origin header:", headers.get("Origin"));
    } else {
      log.cors("No Origin header set");
    }

    // Update the init object with the modified headers
    init = {
      ...init,
      headers
    };
  }

  // Handle mTLS if enabled
  if(API_IS_SECURE && API_MTLS_ENABLED) {
    log.mtls(`mTLS is enabled for request to ${href}`);

    if(typeof window === "undefined") {
      // Node.js environment
      log.mtls("Running in Node.js environment, configuring mTLS");
      init = await configureMtlsForNode(init);
    } else {
      // Browser environment - certificates handled by browser
      log.mtls("Running in browser environment, using browser certificate store");
      log.mtls("Browser will automatically present client certificate when prompted");
    }
  } else {
    log.mtls(`mTLS is ${API_IS_SECURE ? "disabled" : "not applicable (not using HTTPS)"} for request to ${href}`);
  }

  // Log the final request configuration
  log.request(`Sending ${init.method || "GET"} request to: ${href}`);

  try {
    // Make the request
    const response = await fetch(href, init);

    // Log response details
    log.response(`Received response from ${href}: ${response.status} ${response.statusText}`);

    // Log CORS-related response headers
    if(typeof window !== "undefined") {
      log.cors("Response status:", response.status, response.statusText);

      // Check for CORS-related headers
      const corsHeaders = [
        "Access-Control-Allow-Origin",
        "Access-Control-Allow-Methods",
        "Access-Control-Allow-Headers",
        "Access-Control-Allow-Credentials",
        "Access-Control-Expose-Headers",
        "Access-Control-Max-Age"
      ];

      corsHeaders.forEach(header=>{
        const value = response.headers.get(header);
        if(value) {
          log.cors(`Response header ${header}:`, value);
        } else {
          log.cors(`Response header ${header} not present`);
        }
      });

      // Check if this is a CORS error (status 0)
      if(response.status === 0) {
        log.error("CORS ERROR: Request was blocked by the browser's same-origin policy");
        log.cors("CORS ERROR: Make sure the server includes the proper Access-Control-Allow-Origin header");
      }
    }

    return response;
  }catch(error) {
    // Log any fetch errors
    log.error(`Error fetching ${href}:`, error);
    throw error;
  }
}

export function fetchApiJSON(path: string, init?: RequestInit | undefined){
  return jsonResponse(fetchApi(path, init));
}
