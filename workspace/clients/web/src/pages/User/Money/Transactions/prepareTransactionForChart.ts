import { NanoUSDWalletTransactionDoc } from "@divinci-ai/models";
import { nanoAmountToBalanceUSD } from "@divinci-ai/tools";

export type ChartData = {
  timestamp: number,
  prev_balance: number,
  net_balance: number,
  cost_balance: number,
  gain_balance: number,
  toolTipInfo: {
    runnerUser: string,
    balanceBefore: bigint,
    net_costs: bigint,
    net_gains: bigint,
  },
  originalTxn: NanoUSDWalletTransactionDoc,
};

export function prepareTransactionsForChart(
  walletUser: string,
  transaction: Array<NanoUSDWalletTransactionDoc>
){
  const result: Array<ChartData> = [];
  for(const doc of transaction){
    let chartData: undefined | ChartData;
    for(const part of doc.parts){
      if(part.walletUser !== walletUser) continue;
      if(!chartData){
        chartData = {
          timestamp: doc.createTimestamp,
          prev_balance: nanoAmountToBalanceUSD(part.balanceBefore),
          net_balance: nanoAmountToBalanceUSD(part.balanceBefore),
          cost_balance: nanoAmountToBalanceUSD(part.balanceBefore),
          gain_balance: nanoAmountToBalanceUSD(part.balanceBefore),
          toolTipInfo: {
            runnerUser: doc.runnerUser,
            balanceBefore: part.balanceBefore,
            net_costs: 0n,
            net_gains: 0n,
          },
          originalTxn: doc,
        };
      }
      chartData.net_balance += nanoAmountToBalanceUSD(part.amount);
      if(part.amount < 0n){
        chartData.cost_balance += nanoAmountToBalanceUSD(part.amount);
        chartData.toolTipInfo.net_costs += part.amount;
      } else {
        chartData.gain_balance += nanoAmountToBalanceUSD(part.amount);
        chartData.toolTipInfo.net_gains += part.amount;
      }
    }
    if(chartData){
      result.push(chartData);
    }
  }
  return result;
}
