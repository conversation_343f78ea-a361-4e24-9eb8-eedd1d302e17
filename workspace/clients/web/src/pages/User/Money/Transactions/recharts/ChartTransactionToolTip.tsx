
import React from "react";
import { TooltipProps } from "recharts";
import { ValueType, NameType } from "recharts/types/component/DefaultTooltipContent";
import { ChartData } from "../prepareTransactionForChart";
import { NanoUSDToDollarString } from "../../../../../components/displays/Money";

export function TransactionTooltip(
  { active, payload, label }: TooltipProps<ValueType, NameType>
){
  if(!active || !payload || !payload.length) return null;

  const dataPoint: ChartData = payload[0].payload;
  const txn = dataPoint.originalTxn;
  const info = dataPoint.toolTipInfo;

  return (
    <div style={{ backgroundColor: "#000", padding: "8px", borderRadius: "8px" }}>
      <div><strong>{new Date(label).toLocaleString()}</strong></div>
      <div>Before: <NanoUSDToDollarString nano={info.balanceBefore} /></div>
      <div>Net: <NanoUSDToDollarString nano={info.balanceBefore + info.net_gains + info.net_costs} /></div>
      <div>Gains: <NanoUSDToDollarString nano={info.net_gains} /></div>
      <div>Losses: <NanoUSDToDollarString nano={info.net_costs} /></div>
      <hr />
      <div><strong>Transaction ID:</strong> {txn._id}</div>
      <div><strong>Type:</strong> {txn.transactionType}</div>
      <div><strong>Step:</strong> {txn.transactionStep}</div>
      <div><strong>Parts:</strong> {txn.parts.length}</div>
    </div>
  );
}
