{
  "compilerOptions": {
    "outDir": "./dist",
    "target": "es2022",
    "module": "nodenext",
    "moduleResolution": "nodenext",
    "lib": ["dom", "dom.iterable", "es2022"],
    "sourceMap": true,
    "esModuleInterop": true,
    "strict": true,
    "noImplicitAny": true,
    "preserveConstEnums": true,
    "resolveJsonModule": true,
    "removeComments": true,
    "skipLibCheck": true,
    "typeRoots": [
      "./node_modules/@types",
      "./@types",
      "./typings"
    ],
    "jsx": "react",
  },
  "files": ["src/index.tsx"],
  "include": ["./typings"],
  "exclude": ["node_modules", "**/*.spec.ts"]
}
