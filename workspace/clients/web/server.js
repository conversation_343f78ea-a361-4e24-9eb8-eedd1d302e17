const express = require("express");
const morgan = require("morgan");
const path = require("path");
const cors = require("cors");
const {
  DIVINCI_DOMAINS,
  CORS_PORTS,
  AUTH0_BASE_URL,
} = require("@divinci-ai/server-globals");
const app = express();
const PORT = process.env.HTTP_PORT || 8080;
const fs = require("fs");
const https = require("https");
const http = require("http");
const SSL_CERT_PATH =
  process.env.SSL_CERT_PATH ||
  "/workspaces/server/private-keys/local-fast/server.crt";
const SSL_KEY_PATH =
  process.env.SSL_KEY_PATH ||
  "/workspaces/server/private-keys/local-fast/server.key";

const isCloudEnvironment = process.env.ENVIRONMENT === "cloud";
const standardPorts = [80, 8080, 443];

// Feature flags to help debugging in Codespaces / local dev
const DISABLE_CSP = process.env.DISABLE_CSP === "true";
const DISABLE_CORS = process.env.DISABLE_CORS === "true";
// Optionally force an API host to be used when injecting API_BASE_URL into the client
const API_BASE_HOST = process.env.API_BASE_HOST || null; // e.g. "studious-pancake-...-8080.app.github.dev"
const FORCE_CODESPACE_HOST = process.env.FORCE_CODESPACE_HOST === "true";

console.log("🌐 Web CORS_PORTS: ", CORS_PORTS);

// Parse CORS_PORTS from environment variable or use fallback
const additionalPorts =
  CORS_PORTS && typeof CORS_PORTS === "string"
    ? CORS_PORTS.split(",").map((port) => parseInt(port.trim()))
    : [8080, 8081, 8082, 8083, 8084, 9080, 9081, 8787, 8788, 8789, 8790]; // fallback values matching cors.env

// Combine standard ports with additional ports
const allPorts = [...new Set([...standardPorts, ...additionalPorts])];

const generateOrigins = (domains, ports) => {
  return domains.flatMap((domain) =>
    ports.map((port) =>
      port === 8080 ||
      port === 80 ||
      port === 443 ||
      port === 9080 ||
      port === 8789 ||
      port === 9081
        ? `https://${domain}`
        : `http://${domain}:${port}`
    )
  );
};

// Generate allowed origins and convert to a Set for faster lookups
const allowedOriginsSet = new Set([
  ...generateOrigins(DIVINCI_DOMAINS, allPorts),
  ...(isCloudEnvironment
    ? []
    : generateOrigins(["localhost"], additionalPorts)),
]);

const corsOptions = {
  origin: function (origin, callback) {
    // Allow undefined origins (e.g., for tools like Postman) or matching subdomains
    if (
      origin === undefined ||
      /^https:\/\/(.*\.)?divinci\.app$/.test(origin) || // Subdomain wildcard check
      /^https:\/\/.*\.app\.github\.dev(:[0-9]+)?$/.test(origin) || // Allow Codespaces preview domains
      (isCloudEnvironment ? /^http:\/\/localhost:\d+$/.test(origin) : false) || // Localhost check for dev environments
      allowedOriginsSet.has(origin) // Optimized lookup using Set
    ) {
      callback(null, true);
    } else {
      const errorMessage = `🚨 Origin: ${origin} is not allowed by CORS.`;
      console.error(errorMessage);
      callback(new Error(errorMessage));
    }
  },
  methods: "GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS", // Support standard HTTP methods
  credentials: true, // Allow cookies and credentials
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "Origin",
    "x-access-token",
    "X-File-Name",
    "X-File-Id",
    "X-Target",
    "X-Processor",
    "X-Vectorize-Config",
    "X-Processor-Config",
    "divinci-organization",
  ],
  preflightContinue: false,
  optionsSuccessStatus: 204,
  exposedHeaders: [
    "X-File-Name",
    "X-File-Id",
    "X-Target",
    "X-Processor",
    "X-Vectorize-Config",
    "X-Processor-Config",
  ],
};

morgan.token("req-headers", (req) => JSON.stringify(req.headers));
app.use(
  morgan(
    ":method :url :status :res[content-length] - :response-time ms :req-headers"
  )
);
// Apply CORS. If DISABLE_CORS is set we allow all origins (useful during debugging/preview tunnels)
if (DISABLE_CORS) {
  console.warn("⚠️ DISABLE_CORS is enabled — allowing all origins (dev only)");
  app.use(
    cors({ origin: true, credentials: true, methods: corsOptions.methods })
  );
} else {
  app.use(cors(corsOptions));
}

// Set Content-Security-Policy header
// Optionally skip setting CSP for debugging in Codespaces preview
if (!DISABLE_CSP) {
  app.use((req, res, next) => {
    // Start with base connect-src domains
    const baseConnectSrc = [
      "'self'",
      "wss:",
      "https://api.speechly.com",
      "https://browser-intake-us5-datadoghq.com",
    ];

    // Add DIVINCI_DOMAINS with https protocol (only if they don't already have it)
    const divinciConnectSrc = DIVINCI_DOMAINS.map((domain) => {
      if (domain.startsWith("http://") || domain.startsWith("https://")) {
        return domain; // Already has protocol
      }
      return `https://${domain}`;
    }).filter((domain) => domain && domain !== "https://"); // Filter out empty/invalid domains

    // Add localhost ports for local API access
    const localApiSrc = [
      "https://localhost:9080",
      "https://localhost:8081",
      "https://localhost:8083",
    ];

    // Add Codespaces API URLs for all service ports
    let codespacesApiSrc = [];
    const forwardedHost = req.headers["x-forwarded-host"];
    if (forwardedHost && typeof forwardedHost === "string") {
      // Extract base Codespace name (remove port suffix)
      const baseHost = forwardedHost.replace(/-\d+\.app\.github\.dev$/, "");
      // Add all service ports for this Codespace
      const servicePorts = [9080, 8081, 8082, 8083];
      servicePorts.forEach((port) => {
        codespacesApiSrc.push(`https://${baseHost}-${port}.app.github.dev`);
      });
    }

    // Also check for CODESPACE_NAME environment variable as fallback
    if (process.env.CODESPACE_NAME && codespacesApiSrc.length === 0) {
      const servicePorts = [9080, 8081, 8083];
      servicePorts.forEach((port) => {
        codespacesApiSrc.push(
          `https://${process.env.CODESPACE_NAME}-${port}.app.github.dev`
        );
      });
    }

    // Combine all connect-src domains
    let connectSrc = `connect-src ${[
      ...baseConnectSrc,
      ...divinciConnectSrc,
      ...localApiSrc,
      ...codespacesApiSrc,
    ].join(" ")}`;

    // Sanitize connect-src and allowed origins to remove malformed entries like "https://127.0.0.1:"
    connectSrc = connectSrc.replace(
      /https:\/\/127\.0\.0\.1:/g,
      "https://127.0.0.1"
    );

    // Add AUTH0_BASE_URL for non-cloud environments
    if (!isCloudEnvironment && AUTH0_BASE_URL) {
      connectSrc += ` ${AUTH0_BASE_URL}`;
    }

    const cspString =
      "default-src 'self'; " +
      "script-src 'self' 'unsafe-inline' blob: https://static.cloudflareinsights.com; " +
      "worker-src 'self' blob:; " +
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; " +
      "style-src-elem 'self' https://fonts.googleapis.com https://cdnjs.cloudflare.com 'unsafe-inline'; " +
      "img-src 'self' data: https://cdnjs.cloudflare.com https://storage.googleapis.com; " +
      "font-src 'self' data: https://fonts.googleapis.com https://fonts.gstatic.com https://cdnjs.cloudflare.com; " +
      connectSrc;

    res.setHeader("Content-Security-Policy", cspString);
    next();
  });
} else {
  console.warn(
    "⚠️ DISABLE_CSP is enabled — Content-Security-Policy header will not be set"
  );
}

// Serve static files
// --- Proxy: forward certain API requests to the local API server to avoid CORS/tunnel PRE-OPTIONS issues ---
// In Codespaces the preview/tunnel sometimes intercepts OPTIONS preflight and returns 401; to avoid
// cross-origin preflight we proxy API requests on the web-server (same origin) to the backend.
const API_PROXY_HOST = process.env.API_PROXY_HOST || "localhost";
const API_PROXY_PORT = process.env.API_PROXY_PORT
  ? parseInt(process.env.API_PROXY_PORT)
  : 9080;

function proxyToLocalApi(req, res, next) {
  // Only proxy API-ish routes
  if (
    !req.path.startsWith("/ai-chat") &&
    !req.path.startsWith("/api") &&
    !req.path.startsWith("/cookie")
  ) {
    return next();
  }

  try {
    // Choose backend port based on route
    let targetPort = API_PROXY_PORT; // default for /ai-chat and /api
    if (req.path.startsWith("/cookie")) {
      targetPort = 8082; // public-api-live handles cookie endpoints
    }

    const targetOptions = {
      hostname: API_PROXY_HOST,
      port: targetPort,
      path: req.originalUrl,
      method: req.method,
      headers: Object.assign({}, req.headers, {
        host: `${API_PROXY_HOST}:${targetPort}`,
      }),
      rejectUnauthorized: false,
    };

    const proxyReq = https.request(targetOptions, (proxyRes) => {
      // Copy status and headers
      res.writeHead(proxyRes.statusCode || 502, proxyRes.headers);
      proxyRes.pipe(res, { end: true });
    });

    proxyReq.on("error", (err) => {
      console.error(
        "Proxy error to local API:",
        err && err.message ? err.message : err
      );
      if (!res.headersSent) {
        res.statusCode = 502;
        res.end(JSON.stringify({ error: "Bad Gateway - proxy error" }));
      }
    });

    // Pipe client request body to proxy
    req.pipe(proxyReq);
  } catch (e) {
    console.error("Proxy thrown error:", e && e.message ? e.message : e);
    next();
  }
}
app.use(proxyToLocalApi);

// --- end proxy ---

app.use(express.static(path.join(__dirname, "public")));

// Inject API_BASE_URL into index.html for dynamic Codespaces/localhost support
app.get("/", (req, res, next) => {
  const indexPath = path.join(__dirname, "public", "index.html");
  let apiBaseUrl = "";
  const forwardedHost = req.headers["x-forwarded-host"];
  // Priority: explicit API_BASE_HOST env override -> Codespaces forwarded host -> https localhost
  if (API_BASE_HOST) {
    apiBaseUrl = API_BASE_HOST.startsWith("http")
      ? API_BASE_HOST
      : `https://${API_BASE_HOST}`;
  } else if (
    forwardedHost &&
    typeof forwardedHost === "string" &&
    !FORCE_CODESPACE_HOST
  ) {
    // In Codespaces: prefer same-origin relative API paths so the web server proxy handles requests
    // This avoids Codespaces preview tunnel interfering with preflight OPTIONS requests.
    apiBaseUrl = ""; // client should use relative paths like '/ai-chat' and the proxy will forward to localhost:9080
  } else if (forwardedHost && typeof forwardedHost === "string") {
    // If FORCE_CODESPACE_HOST is set, use the Codespaces host for API calls
    apiBaseUrl = `https://${forwardedHost.replace(/-8080/, "-9080")}`;
  } else {
    // Local: use https://localhost:9080
    apiBaseUrl = "https://localhost:9080";
  }
  // Read and inject the API_BASE_URL script
  require("fs").readFile(indexPath, "utf8", (err, html) => {
    if (err) return next(err);
    const injected = html.replace(
      /<head>/i,
      `<head>\n<script>window.API_BASE_URL='${apiBaseUrl}';</script>`
    );
    res.send(injected);
  });
});

// SPA Fallback
app.get("*", (req, res) => {
  res.sendFile(path.join(__dirname, "public", "index.html"));
});

if (fs.existsSync(SSL_CERT_PATH) && fs.existsSync(SSL_KEY_PATH)) {
  const sslOptions = {
    cert: fs.readFileSync(SSL_CERT_PATH),
    key: fs.readFileSync(SSL_KEY_PATH),
  };
  https.createServer(sslOptions, app).listen(PORT, () => {
    console.log(`HTTPS server is running on port ${PORT}`);
  });
} else {
  app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT} (HTTP fallback)`);
  });
}
