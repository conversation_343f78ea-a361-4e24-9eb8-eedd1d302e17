name = "d1-doc-elements"
main = "src/index.ts"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat"]
logpush = true

[observability]
enabled = true

[placement]
mode = "smart"

[dev]
port = 8787
ip = "0.0.0.0"
local_protocol = "http"

[vars]
ENVIRONMENT = "development"

# D1 Database
# [[d1_databases]]
# binding = "DB"
# database_name = "d1-doc-elements-dev"
# database_id = "ec1ddea6-6668-413d-b05c-54920a3f4539"
# migrations_dir = "migrations"

# Local environment configuration
[env.local.vars]
ENVIRONMENT = "local"
CLOUDFLARE_WORKER_X_AUTH_DEV = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvZSBEb2UiLCJpYXQiOjE1MTYyMzkwMjJ9.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2"

[[env.local.tail_consumers]]
service = "d1-doc-elements_tail"

[[env.local.d1_databases]]
binding = "DB"
database_name = "d1-doc-elements-local"
database_id = "d1-doc-elements-local_id"

# Dev-specific configurations
[env.dev]
[env.dev.vars]
ENVIRONMENT = "dev"

[[env.dev.tail_consumers]]
service = "d1-doc-elements_tail"

# route = "dev.example.com"
[[env.dev.d1_databases]]
binding = "DB"
database_name = "d1-doc-elements-dev"
database_id = "ec1ddea6-6668-413d-b05c-54920a3f4539"

# Stage environment configuration
[env.stage]
# Staging-specific configurations
name = "d1-doc-elements-stage"
[env.stage.vars]
ENVIRONMENT = "stage"

[[env.stage.tail_consumers]]
service = "d1-doc-elements_tail"
# route = "stage.example.com/*"
[[env.stage.d1_databases]]
binding = "DB"
database_name = "d1-doc-elements-stage"
database_id = "93f7422a-a0ac-4790-898d-7c03ae85ee4c"

# Production environment configuration
[env.production]
# Production-specific configurations
name = "d1-doc-elements-production"
vars = { ENVIRONMENT = "production", tail_consumers = [
  { service = "d1-doc-elements_tail" },
] }
# routes = ["example.com/foo/*", "example.com/bar/*"]
[[env.production.d1_databases]]
binding = "DB"
database_name = "d1-doc-elements-production"
database_id = "a76d6335-2643-4ada-8762-ebcd68e5aa95"
