import { Context } from 'hono';
import { streamArray } from 'stream-json/streamers/StreamArray';
import { convertWebReadableToReadable } from '../utils/stream';
import { createDBWritable, InputError, waitForFinish } from './utils';
import { ReadableStream as CloudflareReadableStream } from '@cloudflare/workers-types';

export async function upsertChunks(c: Context) {
  try {
    console.log(`🚦 Handling request for POST /api/upsert`);

    const body = await c.req.json();
    if (!body.table) {
      console.error('❌ Missing required field: table');
      return c.json({
        success: false,
        error: 'Missing required field: table'
      }, 400);
    }
    const tableName = body.table;  // Allow dynamic table name
    console.log(`📦 Request body size: ${JSON.stringify(body).length} bytes`);

    // Add detailed chunk logging
    if (Array.isArray(body)) {
      console.log(`📝 Upserting chunks details:`, {
        count: body.length,
        chunkIds: body.map(chunk => chunk.id),
        sampleChunk: body.length > 0 ? {
          id: body[0].id,
          textLength: body[0].text.length,
          hasMetadata: !!body[0].metadata
        } : 'N/A'
      });
    }

    // Check if this is a table creation request
    if (!Array.isArray(body) && body.table && body.schema) {
      console.log('📝 Creating table:', body.table);
      const createTableSQL = `CREATE TABLE IF NOT EXISTS ${body.table} (${
        Object.entries(body.schema)
          .map(([column, type]) => `${column} ${type}`)
          .join(', ')
      })`;

      await c.env.DB.prepare(createTableSQL).run();
      return c.json({ success: true });
    }

    // Extract chunks array if body is an object with chunks property
    const chunks = Array.isArray(body) ? body : (body.chunks || []);
    // Use provided table name from earlier in the code
    const finalTableName = body.table || tableName; // Use provided table name or default

    console.log(`🔍 Using table: ${finalTableName}`);

    // Validate chunks array is not empty
    if (chunks.length === 0) {
      console.error('❌ Invalid input: empty chunks array');
      return c.json({
        success: false,
        error: '❌ Invalid input: expected non-empty array of chunks'
      }, 400);
    }

    // Validate each chunk
    for (const chunk of chunks) {
      if (!chunk.id || !chunk.text || !chunk.metadata) {
        console.error('❌ Invalid chunk format:', chunk);
        return c.json({
          success: false,
          error: 'Invalid chunk format: each chunk must have id, text, and metadata'
        }, 400);
      }
    }

    // Process chunks
    const stmt = c.env.DB.prepare(
      `INSERT OR REPLACE INTO ${finalTableName} (id, text, metadata)
       VALUES (?, ?, ?)`
    );

    for (const chunk of chunks) {
      try {
        await stmt.bind(
          chunk.id,
          chunk.text,
          JSON.stringify(chunk.metadata)
        ).run();
        console.log(`✅ Successfully upserted chunk: ${chunk.id} to table ${finalTableName}`);
      } catch (error) {
        console.error(`❌ Error inserting chunk ${chunk.id} into ${finalTableName}:`, error);
        return c.json({
          success: false,
          error: `Failed to insert chunk ${chunk.id}: ${error instanceof Error ? error.message : String(error)}`
        }, 400);
      }
    }

    return c.json({ success: true });
  } catch (error) {
    console.error('❌ Error processing upsert request:', error);
    return c.json({
      success: false,
      error: `Internal server error: ${error instanceof Error ? error.message : String(error)}`
    }, 500);
  }
}
