// ⛔️ NEED TO SECURE HEADERS WITH: "Access-Control-Allow-Headers": "Content-Type, Cloudflare-Worker-X-Dev-Auth, Authorization",
// 📢

import { Hono } from "hono";
import { secureHeaders } from "hono/secure-headers";
import { cors } from "hono/cors";

type Env = {
  DB: D1Database,
  // 📓 https://developers.cloudflare.com/workers/configuration/secrets/
  AUTH0_S2S_CLIENT_SECRET: string,
  ENVIRONMENT: string, // Cloudflare deployment environment
};


const app = new Hono<{ Bindings: Env }>();

const unauthorized403Message = "🚫 403 - Unauthorized access";

// Add CORS middleware
app.use("*", cors({
  origin: "*",
  allowHeaders: ["Content-Type", "Cloudflare-Worker-X-Dev-Auth", "Authorization", "x-debug-client",],
  allowMethods: ["POST", "GET", "OPTIONS", "DELETE"],
  exposeHeaders: ["Content-Length"],
  maxAge: 600,
  credentials: true,
}));

// DEBUG: we have an issue with d1 tables, these are useful but open up a security hole
// import { retrieveAll, retrieveTablenames } from "./retrieve";
// app.get("/retrieve-tablenames", retrieveTablenames);
// app.get("/retrieve-all", retrieveAll);

app.use("*", async (c, next)=>{
  console.log("🎯 Hitting D1 Worker endpoint:", c.req.path);
  console.log("📨 Request method:", c.req.method);
  await next();
});

// 📓 https://hono.dev/middleware/builtin/secure-headers
app.use("*", secureHeaders());

import { validateRequest } from "./util";
app.use("*", async (c, next)=>{
  if(!validateRequest(c)) {
    return c.json(unauthorized403Message, 403);
  }

  return await next();
});

import { upsertChunks } from "./upsert";
app.post("/api/upsert", upsertChunks);

import { retrieveChunk, retrieveChunkWithIds } from "./retrieve";
app.post("/api/retrieve", retrieveChunk);
app.post("/api/retrieve-ids", retrieveChunkWithIds);

import { deleteChunk, deleteChunkWithIds } from "./delete";
app.delete("/api/delete", deleteChunk);
app.delete("/api/delete-ids", deleteChunkWithIds);

// Add SQL execution endpoint for admin operations
app.post("/api/admin/execute-sql", async (c) => {
  try {
    const { sql } = await c.req.json();
    if (!sql) {
      return c.json({ error: "SQL query is required" }, 400);
    }

    console.log("🔧 Executing SQL:", sql);
    const result = await c.env.DB.prepare(sql).run();
    return c.json({ success: true, result });
  } catch (error) {
    console.error("❌ Error executing SQL:", error);
    return c.json({ success: false, error: String(error) }, 500);
  }
});

// Add OPTIONS handler for CORS preflight
app.options("*", (c)=>{
  return new Response(null, {
    status: 204,
  });
});

// Fallback for unhandled routes
app.all("*", (c)=>{
  console.log("⚠️ Unhandled route:", c.req.path);
  return c.json("👀 Not Found", 404);
});

export default app;
