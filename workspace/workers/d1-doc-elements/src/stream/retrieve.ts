import { Context } from "hono";

import { ensureTableExists, getTableName } from "./util";
import { buildFilterQuery } from "./filter";

export async function retrieveChunk(c: Context<any>){
  console.log(`🚦 Handling request for POST /api/retrieve`);

  const requestBody = await c.req.json();
  const { id, whitelabelId } = requestBody;

  console.log(`🔍 Attempting to retrieve chunk with id: ${id}`);

  if(!id || !whitelabelId) {
    console.error(`❌ Missing required fields:`, { id, whitelabelId });
    return c.json(`👀 Missing required fields id: ${id}, whitelabelId: ${whitelabelId}`, 400);
  }

  const tableName = await ensureTableExists(c, whitelabelId);

  //📝 Retrieve the specified record
  //▶️ Test Locally:  npx wrangler d1 execute d1-doc-elements-dev --command "SELECT * FROM d1_chunks_<whitelabel_id> WHERE id = '00000021i8vugjjuf2ghf-chunk_946';" --env dev
  try {
    console.log(
      `🔍 Executing retrieve query in table ${tableName} with id ${id}`,
    );
    const retrieveQuery = `SELECT * FROM ${tableName} WHERE id = ?`;
    const statement = c.env.DB.prepare(retrieveQuery);
    const { results } = await statement.bind(id).run();

    if(!results || results.length === 0) {
      console.error(`❌ Chunk not found: ${id} in table ${tableName}`);
      // Add a debug query to check table contents
      const debugQuery = `SELECT id FROM ${tableName} LIMIT 5`;
      const { results: sampleResults } = await c.env.DB.prepare(debugQuery).run();
      console.log(`📊 Sample of existing chunks in table:`, sampleResults);

      return c.json(
        `🔍 Record not found in table ${tableName} with id ${id}`,
        404,
      );
    }

    // Include text, metadata, and other fields in the response
    const fullResponse = results.map(
      (result: { id: string, text: string, metadata: string })=>(
        {
          id: result.id,
          text: result.text,
          metadata: JSON.parse(result.metadata), // Parse metadata back to object if it's stored as a JSON string
        }
      ));

    console.log(
      `✅ Retrieve query executed successfully with results:`,
      fullResponse,
    );
    return c.json(fullResponse); // Return the full results array
  }catch(error) {
    console.error(
      `❌ Error retrieving id: ${id} from table: ${tableName}: \n`,
      error,
    );
    return c.json(`❌ Failed to retrieve ${id} from ${tableName}`, 500);
  }
}

export async function retrieveChunkWithIds(c: Context<any>){
  console.log(`🚦 Handling request for POST /api/retrieve-ids`);

  try {
    const requestBody = await c.req.json();
    console.log(`📥 Received request body:`, requestBody);

    if(!requestBody || !requestBody.ids || !requestBody.whitelabelId) {
      console.log("❌ Missing required fields in request body");
      return c.json("Missing required fields: ids and whitelabelId", 400);
    }

    const { ids, whitelabelId } = requestBody;

    if(!Array.isArray(ids)) {
      console.log("❌ Expected ids to be an Array");
      return c.json("Expected ids to be an Array", 400);
    }

    if(ids.length === 0) {
      console.log("ℹ️ No items to retrieve");
      return c.json("🗑️ No Items retrieved", 200);
    }

    const tableName = await ensureTableExists(c, whitelabelId);
    console.log(`✅ Using table: ${tableName}`);

    if(typeof ids[0] !== "string") {
      console.log("❌ Expected String at index 0");
      return c.json("Expected String at index 0", 400);
    }

    let idList = `('${ids[0].replaceAll("'", "\\'")}'`;

    for(let i = 1; i < ids.length; i++) {
      if(typeof ids[i] !== "string") {
        console.log(`❌ Expected String at index ${i}`);
        return c.json(`Expected String at index ${i}`, 400);
      }
      idList += `,'${ids[i].replaceAll("'", "\\'")}'`;
    }

    idList += ")";

    // First, check if the table exists
    try {
      const tableExistsQuery = `SELECT name FROM sqlite_master WHERE type='table' AND name='${tableName}'`;
      console.log(`🔍 Checking if table exists:`, tableExistsQuery);
      const { results: tableResults } = await c.env.DB.prepare(tableExistsQuery).run();

      if (tableResults.length === 0) {
        console.error(`❌ Table ${tableName} does not exist`);
        // Create the table
        await ensureTableExists(c, whitelabelId);
        console.log(`✅ Created table ${tableName}`);
      } else {
        console.log(`✅ Table ${tableName} exists`);
      }
    } catch (error) {
      console.error(`❌ Error checking if table exists:`, error);
    }

    const selectQuery = `SELECT * FROM ${tableName} WHERE id IN ${idList}`;
    console.log(`🔍 Executing query:`, selectQuery);

    const { results } = await c.env.DB.prepare(selectQuery).run();

    console.log(`✅ Query returned ${results.length} results`);

    // Add this debug query
    const debugQuery = `SELECT COUNT(*) as count FROM ${tableName}`;
    const { results: countResults } = await c.env.DB.prepare(debugQuery).run();
    console.log(`📊 Total rows in table ${tableName}:`, countResults[0].count);

    // If no results, check if the IDs exist in the table
    if (results.length === 0) {
      console.log(`❌ No results found for the requested IDs`);

      // Sample some IDs from the table to see what's available
      const sampleQuery = `SELECT id FROM ${tableName} LIMIT 10`;
      const { results: sampleResults } = await c.env.DB.prepare(sampleQuery).run();
      console.log(`📊 Sample IDs in table ${tableName}:`, sampleResults.map(r => r.id));

      // Check if any of the requested IDs exist in the table
      for (const id of ids) {
        const checkQuery = `SELECT COUNT(*) as count FROM ${tableName} WHERE id = ?`;
        const { results: checkResults } = await c.env.DB.prepare(checkQuery).bind(id).run();
        console.log(`🔍 Checking if ID ${id} exists:`, checkResults[0].count > 0 ? 'Yes' : 'No');
      }
    }

    return c.json(results);
  }catch(error) {
    console.error("❌ Error in retrieveChunkWithIds:", error);
    return c.json({ error: "Internal Server Error" }, 500);
  }
}

export async function retrieveAll(c: Context<any>){
  try {
    const whitelabelId = c.req.query("tablename");
    if(!whitelabelId) {
      return c.json("Missing required field: tableName", 400);
    }
    const tableName = getTableName(whitelabelId);
    const selectQuery = `SELECT * FROM ${tableName}`;

    const { results } = await c.env.DB.prepare(selectQuery).run();

    return c.json(results);
  }catch(error) {
    console.error("❌ Error in retrieveAll:", error);
    return c.json({ error: "Internal Server Error" }, 500);
  }
}

export async function retrieveTablenames(c: Context<any>){
  try {
    // "SELECT name FROM [databasename].sys.tables";

    const selectQuery = "SELECT name FROM sqlite_master WHERE type='table'";
    const { results } = await c.env.DB.prepare(selectQuery).run();
    return c.json(results);
  }catch(error) {
    console.error("❌ Error in retrieveTablenames:", error);
    return c.json({ error: "Internal Server Error" }, 500);
  }
}

/**
 * Retrieves chunks from the database based on ids or filter criteria.
 *
 * @param c - The Hono context
 * @returns A JSON response with the retrieved chunks or an error message
 */
export async function retrieveChunks(c: Context<any>) {
  try {
    // Parse the request body
    const requestBody = await c.req.json();
    const { table, ids, filter } = requestBody;

    // Validate required fields
    if (!table) {
      return c.json({
        success: false,
        error: 'Missing required field: table'
      }, 400);
    }

    // Validate that either ids or filter is provided
    if (!ids && !filter) {
      return c.json({
        success: false,
        error: 'Must provide either ids or filter'
      }, 400);
    }

    let query: string;
    let params: any[] = [];

    // Build the query based on ids or filter
    if (ids) {
      // Query by ids
      const placeholders = ids.map(() => '?').join(', ');
      query = `SELECT * FROM ${table} WHERE id IN (${placeholders})`;
      params = [...ids];
    } else if (filter) {
      // Query by filter
      const filterResult = buildFilterQuery(filter);
      query = `SELECT * FROM ${table}`;

      if (filterResult.query) {
        query += ` WHERE ${filterResult.query}`;
        params = filterResult.params;
      }
    }

    // Execute the query
    const chunks = await c.env.DB.prepare(query).bind(...params).all();

    // Process the results
    const processedChunks = chunks.map((chunk: any) => ({
      id: chunk.id,
      text: chunk.text,
      metadata: JSON.parse(chunk.metadata)
    }));

    // Return the results
    return c.json({
      success: true,
      chunks: processedChunks
    });
  } catch (error) {
    console.error('Error in retrieveChunks:', error);

    // Handle different types of errors
    if (error instanceof Error) {
      if (error.message.includes('database') || error.message.includes('SQL') || error.message.includes('Database')) {
        return c.json({
          success: false,
          error: `Failed to retrieve chunks: ${error.message}`
        }, 400);
      }
    }

    // Generic error handling
    return c.json({
      success: false,
      error: 'Internal server error'
    }, 500);
  }
}
