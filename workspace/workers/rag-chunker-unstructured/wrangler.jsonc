/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "rag-chunker-unstructured",
	"main": "src/index.ts",
	"compatibility_date": "2025-03-03",
	"compatibility_flags": ["nodejs_compat"],
	"logpush": true,
	"observability": {
		"enabled": true
	},
	"placement": {
		"mode": "smart"
	},

	// Development environment
	"dev": {
		"port": 8787,
		"ip": "0.0.0.0",
		"local_protocol": "http"
	},

	// Base environment variables
	"vars": {
		"ENVIRONMENT": "development",
	},

	// Local environment configuration
	"[env.local]": {
		"vars": {
			"ENVIRONMENT": "local",
			"ALLOWED_ORIGINS": "http://localhost:8080,http://localhost:8787,http://127.0.0.1:8080,http://127.0.0.1:8787,https://api.unstructuredapp.io,http://localhost:9000,http://localhost:9001,http://127.0.0.1:9000,http://127.0.0.1:9001,http://**********:9000,http://**********:9001,http://local-minio:9000"
		}
	},

	// Development environment configuration
	"[env.dev]": {
		"vars": {
			"ENVIRONMENT": "dev",
			"ALLOWED_ORIGINS": "https://*.dev.divinci.app,https://api.unstructuredapp.io"
		},
		"routes": [
			{ "pattern": "rag-chunker-unstructured.dev.divinci.app", "custom_domain": true }
		]
	},

	// Staging environment configuration
	"[env.stage]": {
		"vars": {
			"ENVIRONMENT": "stage",
			"ALLOWED_ORIGINS": "https://*.stage.divinci.app,https://api.unstructuredapp.io"
		},
		"routes": [
			{ "pattern": "rag-chunker-unstructured.stage.divinci.app", "custom_domain": true }
		]
	},

	// Production environment configuration
	"[env.production]": {
		"vars": {
			"ENVIRONMENT": "production",
			"ALLOWED_ORIGINS": "https://*.divinci.app,https://api.unstructuredapp.io"
		},
		"routes": [
			{ "pattern": "rag-chunker-unstructured.divinci.app", "custom_domain": true }
		]
	}
}
