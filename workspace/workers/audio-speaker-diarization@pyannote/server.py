# Environment Variable Setup
from utils.config import load_env_files_with_dotenv
load_env_files_with_dotenv()
print("✅ Loaded env files.")

"""
Environment Variable Should be loaded first
openparse and potentially other libraries depend on the variables being set
"""

# Ensure cache directories are properly set
import os
for env_var, default_path in {
    'HOME': '/home/<USER>',
    'MPLCONFIGDIR': '/home/<USER>/.cache/matplotlib',
    'HF_HOME': '/home/<USER>/.cache/huggingface',
    'XDG_CACHE_HOME': '/home/<USER>/.cache'
}.items():
    if not os.environ.get(env_var):
        os.environ[env_var] = default_path
        print(f"✅ Set {env_var} to {default_path}")
    else:
        print(f"✅ Using existing {env_var}: {os.environ[env_var]}")

# Ensure cache directories exist
for cache_dir in [
    os.environ.get('MPLCONFIGDIR'),
    os.environ.get('HF_HOME'),
    os.environ.get('XDG_CACHE_HOME')
]:
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir, exist_ok=True)
        print(f"✅ Created cache directory: {cache_dir}")

# Setup HTTP Server
import os
import json
import logging
from pathlib import Path

# Configure logging before app initialization
logging.basicConfig(
    level=os.getenv('LOG_LEVEL', 'DEBUG'),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


from flask import Flask, Response, request, abort, jsonify
from flask_cors import CORS

from src.allowed_files import allowed_file
from src.process_file import prepare_diarization, process_diarization # parsed_to_jsontext
from utils.json_stream import generator_to_json_array_stream
from utils.file import save_file_as_unique


app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configure mTLS if enabled
try:
    from mtls_utils import configure_flask_mtls
    configure_flask_mtls(app)
    logger.info("✅ mTLS configuration applied.")
except ImportError:
    logger.warning("⚠️ mTLS utils not available, continuing without mTLS.")
except Exception as e:
    logger.error(f"⚠️ Failed to configure mTLS: {str(e)}")

FORM_BODY_NAME = 'file'

@app.route('/', methods=['GET'])
def index():
    my_data = {
      "hello": "world",
      "whoami": "a speaker diarization processor"
    }
    return Response(
        json.dumps(my_data),
        status=200,
        mimetype='application/json'
    )

@app.route('/health', methods=['GET'])
def health():
    my_data = {
      "status": "ok",
      "service": "pyannote",
      "mtls": "enabled" if app.config.get('MTLS_ENABLED', False) else "disabled"
    }
    return Response(
        json.dumps(my_data),
        status=200,
        mimetype='application/json'
    )

@app.route('/', methods=['POST'])
def upload_file():
    logger.info(f"📥 Received upload request")
    logger.info(f"📥 Request headers: {dict(request.headers)}")
    logger.info(f"📥 Request content type: {request.content_type}")
    logger.info(f"📥 Request is_json: {request.is_json}")
    logger.info(f"📥 Request data: {request.data}")
    logger.info(f"📥 Request form: {request.form}")
    logger.info(f"📥 Request files: {request.files}")
    logger.info(f"📥 Request method: {request.method}")

    try:
        # Check if this is a URL-based request
        if request.is_json or request.content_type == 'application/json' or (request.data and len(request.data) > 0):
            logger.info("📄 Processing JSON request")
            try:
                # Try to parse JSON data
                if request.is_json:
                    data = request.get_json()
                else:
                    # Try to parse raw data as JSON
                    import json
                    data = json.loads(request.data)

                logger.info(f"📄 JSON data: {data}")
            except Exception as e:
                logger.error(f"❌ Error parsing JSON: {str(e)}")
                return jsonify({"error": "Invalid JSON format"}), 400

            if not data or 'url' not in data:
                logger.warning("❌ No URL in JSON request")
                return jsonify({"error": "No URL provided in JSON request."}), 400

            url = data['url']
            logger.info(f"📄 Processing URL: {url}")

            # Import the S3 client
            from src.s3_client import download_from_url
            import uuid
            import tempfile

            # Create a temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.tmp')
            temp_file.close()

            # Download the file
            logger.info(f"📥 Downloading file from URL: {url} to {temp_file.name}")
            if not download_from_url(url, temp_file.name):
                logger.warning(f"❌ Failed to download file from URL: {url}")
                return jsonify({"error": f"Failed to download file from URL: {url}"}), 400

            file_full_path = temp_file.name
            logger.info(f"📥 File downloaded to: {file_full_path}")

            # Process the file
            try:
                logger.info(f"🔄 Preparing diarization for file: {file_full_path}")
                dia, file_full_path = prepare_diarization(file_full_path)
                logger.info("✅ File processed successfully")
                logger.info(f"📤 Returning diarization results")
                return Response(
                    generator_to_json_array_stream(process_diarization(dia)),
                    status=200,
                    mimetype='application/json'
                )
            except Exception as e:
                logger.error(f"❌ Error processing file: {str(e)}", exc_info=True)
                return jsonify({"error": str(e)}), 500
            finally:
                if os.path.exists(file_full_path):
                    os.remove(file_full_path)
                    logger.info(f"🧹 Removed file: {file_full_path}")
                if os.path.exists(temp_file.name):
                    os.remove(temp_file.name)
                    logger.info(f"🧹 Removed file: {temp_file.name}")

        # Handle regular file upload
        files = request.files.getlist(FORM_BODY_NAME)
        logger.info(f"📥 Files in request: {files}")

        if len(files) == 0:
            logger.warning("❌ No file in request")
            return jsonify({"error": f"No {FORM_BODY_NAME} part in the request."}), 400

        if len(files) > 1:
            logger.warning(f"❌ Multiple files received: {len(files)}")
            return jsonify({"error": f"Only one {FORM_BODY_NAME} allowed."}), 400

        file = files[0]
        logger.info(f"📄 Processing file: {file.filename}")

        if not allowed_file(file.filename):
            logger.warning(f"❌ Invalid file type: {file.filename}")
            return jsonify({"error": "File extension not allowed."}), 400

        file_full_path = save_file_as_unique(file)
        # Process file
        try:
            dia, file_full_path = prepare_diarization(file_full_path)
            logger.info("✅ File processed successfully")
            return Response(
                generator_to_json_array_stream(process_diarization(dia)),
                status=200,
                mimetype='application/json'
            )

        except Exception as e:
            logger.error(f"❌ Error processing file: {str(e)}", exc_info=True)
            return jsonify({"error": str(e)}), 500
        finally:
            if os.path.exists(file_full_path):
                os.remove(file_full_path)

    except Exception as e:
        logger.error(f"❌ Server error: {str(e)}", exc_info=True)
        return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    http_port = int(os.getenv("DIVINCI_AUDIO_DIARIZER_PYANNOTE_HTTP_PORT", "8085"))
    https_port = 19000  # Hardcoded HTTPS port for mTLS
    print("🌞 Starting server on HTTP port: " + str(http_port))

    # Check if mTLS is enabled
    mtls_enabled = os.getenv("MTLS_ENABLED", "false").lower() == "true"
    if mtls_enabled:
        print("🔒 mTLS is enabled, using HTTPS on port " + str(https_port))

        # Use hardcoded paths for certificates
        mtls_server_cert = "/etc/ssl/certs/server.crt"
        mtls_server_key = "/etc/ssl/private/server.key"
        mtls_ca_cert = "/etc/ssl/ca/ca.crt"

        print(f"🔑 Using certificate: {mtls_server_cert}")
        print(f"🔑 Using key: {mtls_server_key}")
        print(f"🔑 Using CA certificate: {mtls_ca_cert}")

        # Verify that the certificate and key files exist
        if not os.path.exists(mtls_server_cert):
            print(f"❌ Certificate file not found: {mtls_server_cert}")
            exit(1)
        if not os.path.exists(mtls_server_key):
            print(f"❌ Key file not found: {mtls_server_key}")
            exit(1)
        if not os.path.exists(mtls_ca_cert):
            print(f"❌ CA certificate file not found: {mtls_ca_cert}")
            exit(1)

        try:
            # Start both HTTP and HTTPS servers using a subprocess
            import subprocess
            from threading import Thread

            # Start HTTP server in a separate thread
            def run_http_server():
                print("🚀 Starting HTTP server on port", http_port)
                app.run(debug=False, host='0.0.0.0', port=http_port, use_reloader=False)

            http_thread = Thread(target=run_http_server)
            http_thread.daemon = True
            http_thread.start()

            # Start HTTPS server using OpenSSL in a subprocess
            print("🚀 Starting HTTPS server on port", https_port)

            # Create a simple WSGI server for HTTPS
            from werkzeug.serving import make_server
            import socket

            # Create a socket and bind it to the port
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            sock.bind(('0.0.0.0', https_port))
            sock.listen(5)

            # Import OpenSSL
            from OpenSSL import SSL

            # Create an SSL context
            context = SSL.Context(SSL.TLS_SERVER_METHOD)
            context.use_privatekey_file(mtls_server_key)
            context.use_certificate_file(mtls_server_cert)
            context.load_verify_locations(mtls_ca_cert)
            context.set_verify(SSL.VERIFY_PEER | SSL.VERIFY_FAIL_IF_NO_PEER_CERT, lambda conn, cert, errno, depth, ok: ok)

            # Create a WSGI server with the SSL context
            from werkzeug.serving import run_simple

            # Create a copy of the Flask app for HTTPS
            https_app = Flask(__name__)

            # Copy all routes from the original app to the HTTPS app
            for rule in app.url_map.iter_rules():
                view_func = app.view_functions[rule.endpoint]
                https_app.add_url_rule(rule.rule, rule.endpoint, view_func, methods=rule.methods)

            # Run the HTTPS server
            run_simple('0.0.0.0', https_port, https_app, ssl_context=context)

        except Exception as e:
            print(f"❌ Error starting HTTPS server: {str(e)}")
            print(f"❌ Error type: {type(e)}")
            print(f"❌ Error details: {repr(e)}")
            import traceback
            traceback.print_exc()
            print("⚠️ Falling back to HTTP")
            app.run(debug=True, host='0.0.0.0', port=http_port)
    else:
        print("🔓 mTLS is disabled, using HTTP")
        app.run(debug=True, host='0.0.0.0', port=http_port)