import os
import boto3
import logging
import requests
from botocore.client import Config
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

def get_s3_client(endpoint_url=None):
    """Creates and returns an S3 client configured for MinIO access."""
    # Get credentials from environment if available
    access_key = os.environ.get('MINIO_ROOT_USER', 'minioadmin')
    secret_key = os.environ.get('MINIO_ROOT_PASSWORD', 'minioadmin')
    
    # Get endpoint URL from argument or environment
    if not endpoint_url:
        minio_host = os.environ.get('MINIO_HOST', 'minio.divinci.local')
        minio_port = os.environ.get('MINIO_PORT', '9000')
        endpoint_url = f"http://{minio_host}:{minio_port}"
    
    logger.info(f"🔄 Creating S3 client with endpoint: {endpoint_url}")
    
    # Create and return the S3 client
    return boto3.client(
        's3',
        endpoint_url=endpoint_url,
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        config=Config(signature_version='s3v4'),
        region_name='us-east-1'  # Dummy region for signature
    )

def download_file(bucket, key, local_path, endpoint_url=None):
    """
    Downloads a file from MinIO S3.
    
    Args:
        bucket (str): Bucket name
        key (str): Object key
        local_path (str): Local path to save the downloaded file
        endpoint_url (str, optional): Custom endpoint URL

    Returns:
        bool: True if successful, False otherwise
    """
    s3 = get_s3_client(endpoint_url)
    try:
        logger.info(f"📥 Downloading {bucket}/{key} to {local_path}")
        s3.download_file(bucket, key, local_path)
        logger.info(f"✅ Download successful")
        return True
    except Exception as e:
        logger.error(f"❌ Error downloading file: {e}")
        return False

def extract_s3_info_from_url(url):
    """
    Extracts bucket and key from a MinIO URL.
    
    Args:
        url (str): MinIO URL
        
    Returns:
        tuple: (bucket, key) or (None, None) if extraction fails
    """
    try:
        parsed = urlparse(url)
        path_parts = parsed.path.strip('/').split('/', 1)
        
        if len(path_parts) < 2:
            logger.warning(f"❌ Invalid S3 URL format: {url}")
            return None, None
            
        bucket = path_parts[0]
        key = path_parts[1]
        
        logger.info(f"🪣 Extracted bucket: {bucket}, key: {key}")
        return bucket, key
    except Exception as e:
        logger.error(f"❌ Error extracting S3 info from URL: {e}")
        return None, None

def download_from_url(url, local_path):
    """
    Downloads a file from a URL, with special handling for MinIO URLs.
    
    Args:
        url (str): URL to download from
        local_path (str): Local path to save the downloaded file
        
    Returns:
        bool: True if successful, False otherwise
    """
    # Check if this is a MinIO URL
    parsed = urlparse(url)
    hostname = parsed.netloc.split(':')[0]
    
    # List of possible MinIO hostnames
    minio_hostnames = [
        'minio.divinci.local', 
        'local-minio', 
        'localhost', 
        '127.0.0.1'
    ]
    
    if hostname in minio_hostnames or 'minio' in hostname:
        logger.info(f"🪣 Detected MinIO URL: {url}")
        bucket, key = extract_s3_info_from_url(url)
        if bucket and key:
            return download_file(bucket, key, local_path)
    
    # For non-MinIO URLs, use regular requests
    try:
        logger.info(f"📥 Downloading from URL: {url}")
        
        # Try with MinIO credentials first
        access_key = os.environ.get('MINIO_ROOT_USER', 'minioadmin')
        secret_key = os.environ.get('MINIO_ROOT_PASSWORD', 'minioadmin')
        
        response = requests.get(
            url, 
            auth=(access_key, secret_key),
            timeout=30
        )
        
        # If that fails, try without auth
        if not response.ok:
            logger.warning(f"⚠️ Download with auth failed, trying without auth")
            response = requests.get(url, timeout=30)
        
        if response.ok:
            with open(local_path, 'wb') as f:
                f.write(response.content)
            logger.info(f"✅ Download successful")
            return True
        else:
            logger.error(f"❌ Download failed: {response.status_code} {response.reason}")
            return False
    except Exception as e:
        logger.error(f"❌ Error downloading from URL: {e}")
        return False
