{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "skipLibCheck": true,
    "lib": [
      "ESNext",
      "DOM"
    ],
    "types": [
      "./worker-configuration.d.ts",
      "node",
    ],
    "jsx": "react-jsx",
    "jsxImportSource": "hono/jsx",
    "allowJs": true,
    "resolveJsonModule": true,
    "baseUrl": "."
  },
  "include": ["src/**/*", "test/boundary/filter-chunks.test.ts"],
  "exclude": ["node_modules", "dist"]
}
