{"name": "chunks-workflow", "scripts": {"dev": "wrangler dev", "dev:local": "wrangler dev --local", "deploy:local": "wrangler deploy", "deploy": "wrangler deploy --minify", "build": "node -r esbuild-register ./esbuild.config.js", "cf-typegen": "wrangler types", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@aws-sdk/client-s3": "^3.750.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@divinci-ai/models": "file:../../resources/models", "@divinci-ai/server-tools": "file:../../resources/server-tools", "@divinci-ai/utils": "file:../../resources/utils", "@hono/zod-validator": "^0.1.11", "esbuild": "^0.25.0", "gpt-tokens": "^1.3.12", "hono": "^3.11.7", "minio": "^8.0.5", "mongoose": "^8.11.0", "unstructured-client": "^0.19.0", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^22.14.0", "@vitest/coverage-istanbul": "^3.1.3", "esbuild-register": "^3.5.0", "fast-check": "^4.1.1", "typescript": "^5.7.3", "vitest": "^3.1.3", "vitest-mock-extended": "^1.2.0", "wrangler": "^4.16.1"}}