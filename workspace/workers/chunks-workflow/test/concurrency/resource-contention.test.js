import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { createFetchResponse } from "../setup-comprehensive";
import { OpenParseProcessor } from "../../src/processors/openparse";
import { createStorageClient } from "../../src/utils/storage-client";
import { RagVectorTextChunksStatus } from "../../src/types";

// Mock environment
const mockEnv = {
  OPENPARSE_API_KEY: "test-api-key",
  OPENPARSE_API_URL: "https://openparse.example.com",
  UNSTRUCTURED_API_KEY: "test-unstructured-key",
  UNSTRUCTURED_API_URL: "https://unstructured.example.com",
  VECTORIZE_API_TOKEN: "test-vectorize-token",
  VECTORIZE_ACCOUNT_ID: "test-account-id",
  VECTORIZE_INDEX_NAME: "test-index-name",
  ENVIRONMENT: "test",
  R2: {
    get: vi.fn(),
    put: vi.fn(),
    list: vi.fn()
  },
  CHUNKS_VECTORIZED: {
    create: vi.fn(),
    get: vi.fn(),
    update: vi.fn()
  },
  ALLOWED_ORIGINS: "*"
};

// Create a simplified mock implementation of ChunksVectorizedWorkflow
class ChunksVectorizedWorkflow {
  constructor(env){
    this.env = env;
  }

  async run(event){
    try {
      const { id, params } = event.data;
      const { files, vectorizeConfig } = params;

      // Get workflow details
      await this.env.CHUNKS_VECTORIZED.get(id);

      // Process each file
      const processedFiles = [];
      for(const file of files) {
        try {
          // Get file from storage
          await this.env.R2.get(file.objectKey);

          // Generate chunks based on processor type
          let chunks = [];
          if(file.processor === "openparse") {
            chunks = [
              { id: "chunk-1", text: "This is test chunk 1 from OpenParse", metadata: { page: 1 } },
              { id: "chunk-2", text: "This is test chunk 2 from OpenParse", metadata: { page: 1 } }
            ];
          } else if(file.processor === "unstructured") {
            chunks = [
              { id: "chunk-1", text: "This is test chunk 1 from Unstructured", metadata: { page: 1 } },
              { id: "chunk-2", text: "This is test chunk 2 from Unstructured", metadata: { page: 1 } }
            ];
          }

          // Store chunks
          const chunksObjectKey = `${vectorizeConfig.ragId}/${file.fileId}/chunks.json`;
          await this.env.R2.put(chunksObjectKey, JSON.stringify(chunks));

          // Update file status
          await this.env.CHUNKS_VECTORIZED.update(id, {
            status: "success-file",
            fileId: file.fileId
          });

          processedFiles.push({
            fileId: file.fileId,
            status: "success",
            chunks
          });
        }catch(error) {
          console.error(`Error processing file ${file.fileId}:`, error);

          // Update file status to error
          await this.env.CHUNKS_VECTORIZED.update(id, {
            status: "error-file",
            fileId: file.fileId,
            error: error.message
          });

          processedFiles.push({
            fileId: file.fileId,
            status: "error",
            error: error.message
          });
        }
      }

      // Update workflow status
      await this.env.CHUNKS_VECTORIZED.update(id, {
        status: RagVectorTextChunksStatus.SUCCESS
      });

      return {
        success: true,
        workflowId: id,
        status: "success",
        processedFiles
      };
    }catch(error) {
      console.error("Error in workflow:", error);

      await this.env.CHUNKS_VECTORIZED.update(event.data.id, {
        status: RagVectorTextChunksStatus.ERROR,
        error: error.message
      });

      return {
        success: false,
        workflowId: event.data.id,
        status: "error",
        error: error.message
      };
    }
  }
}

// Utility function to simulate delay
const delay = (ms)=>new Promise(resolve=>setTimeout(resolve, ms));

describe("Concurrency Testing - Resource Contention", ()=>{
  beforeEach(()=>{
    // Mock console methods to avoid cluttering test output
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});

    // Reset fetch mock
    global.fetch = vi.fn();

    // Setup basic mocks
    mockEnv.R2.get.mockResolvedValue({
      body: new Uint8Array(Buffer.from("Mock PDF content")),
      headers: new Headers({
        "content-type": "application/pdf",
        "content-length": "17"
      })
    });

    mockEnv.R2.put.mockResolvedValue({
      key: "test-object-key"
    });

    mockEnv.CHUNKS_VECTORIZED.get.mockResolvedValue({
      id: "test-workflow-id",
      status: "processing",
      params: {
        files: [
          {
            fileId: "test-file-id",
            processor: "openparse"
          }
        ],
        vectorizeConfig: {
          ragId: "test-rag-id"
        }
      }
    });

    mockEnv.CHUNKS_VECTORIZED.update.mockResolvedValue({
      success: true
    });

    // Mock fetch for API calls
    global.fetch.mockImplementation((url, options)=>{
      return Promise.resolve(createFetchResponse(
        { success: true },
        200,
        { "Content-Type": "application/json" }
      ));
    });
  });

  afterEach(()=>{
    vi.restoreAllMocks();
  });

  describe("API Rate Limiting", ()=>{
    it("should handle API rate limiting with exponential backoff", async ()=>{
      // Counter for API calls
      let apiCallCount = 0;
      let retryCount = 0;

      // Mock fetch to simulate rate limiting with increasing severity
      global.fetch.mockImplementation((url, options)=>{
        apiCallCount++;

        if(url.includes("/fileUrl/init")) {
          // First call succeeds, subsequent calls get rate limited with increasing retry times
          if(apiCallCount === 1) {
            return Promise.resolve(createFetchResponse(
              {
                total_chunks: 1,
                session_id: "test-session-id-1"
              },
              200,
              { "Content-Type": "application/json" }
            ));
          } else {
            retryCount++;
            // Increase retry time for each subsequent call
            const retryAfter = retryCount;
            return Promise.resolve(createFetchResponse(
              { error: "Rate limit exceeded" },
              429,
              { "Content-Type": "application/json", "Retry-After": retryAfter.toString() }
            ));
          }
        }

        if(url.includes("/fileUrl/batch")) {
          return Promise.resolve(createFetchResponse(
            [
              {
                id: "chunk-1",
                text: "Test chunk",
                metadata: { page: 1 }
              }
            ],
            200,
            { "Content-Type": "application/json" }
          ));
        }

        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instances with retry
      const processor1 = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        {
          useStreaming: false,
          retryOptions: {
            maxAttempts: 5,
            delayMs: 100,
            backoffFactor: 2
          }
        }
      );

      const processor2 = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        {
          useStreaming: false,
          retryOptions: {
            maxAttempts: 5,
            delayMs: 100,
            backoffFactor: 2
          }
        }
      );

      // Process documents concurrently
      const results = await Promise.all([
        processor1.process(
          "https://example.com/test-file-1.pdf",
          { semantic: true }
        ),
        processor2.process(
          "https://example.com/test-file-2.pdf",
          { semantic: true }
        ).catch(error=>{
          // Second processor should fail due to rate limiting
          return { error: error.message };
        })
      ]);

      // Verify results
      expect(results).toHaveLength(2);
      expect(results[0]).toBeDefined();

      // Note: In a real implementation, we would verify that the retries were performed with exponential backoff
      // This is a placeholder test that documents the requirement
      console.log(`Rate limiting test: API was called ${apiCallCount} times with ${retryCount} retries`);
    });

    it("should prioritize critical operations during rate limiting", async ()=>{
      // Track API calls and their priorities
      const apiCalls = [];

      // Mock fetch to simulate rate limiting
      global.fetch.mockImplementation((url, options)=>{
        // Extract priority from headers if present
        const priority = options.headers?.["x-priority"] || "normal";

        // Record the API call
        apiCalls.push({ url, priority });

        // Rate limit all calls except high priority ones
        if(priority !== "high" && apiCalls.length > 3) {
          return Promise.resolve(createFetchResponse(
            { error: "Rate limit exceeded" },
            429,
            { "Content-Type": "application/json", "Retry-After": "2" }
          ));
        }

        if(url.includes("/fileUrl/init")) {
          return Promise.resolve(createFetchResponse(
            {
              total_chunks: 1,
              session_id: `test-session-id-${apiCalls.length}`
            },
            200,
            { "Content-Type": "application/json" }
          ));
        }

        if(url.includes("/fileUrl/batch")) {
          return Promise.resolve(createFetchResponse(
            [
              {
                id: "chunk-1",
                text: "Test chunk",
                metadata: { page: 1 }
              }
            ],
            200,
            { "Content-Type": "application/json" }
          ));
        }

        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instances
      const normalProcessor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      const highPriorityProcessor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Override fetch for high priority processor
      const originalFetch = global.fetch;
      global.fetch = vi.fn((url, options)=>{
        // Add high priority header for the high priority processor
        const newOptions = {
          ...options,
          headers: {
            ...options.headers,
            "x-priority": "high"
          }
        };
        return originalFetch(url, newOptions);
      });

      // Process high priority document
      const highPriorityResult = await highPriorityProcessor.process(
        "https://example.com/high-priority-file.pdf",
        { semantic: true }
      );

      // Restore original fetch
      global.fetch = originalFetch;

      // Process normal priority documents concurrently
      const normalResults = await Promise.allSettled([
        normalProcessor.process(
          "https://example.com/normal-file-1.pdf",
          { semantic: true }
        ),
        normalProcessor.process(
          "https://example.com/normal-file-2.pdf",
          { semantic: true }
        ),
        normalProcessor.process(
          "https://example.com/normal-file-3.pdf",
          { semantic: true }
        ),
        normalProcessor.process(
          "https://example.com/normal-file-4.pdf",
          { semantic: true }
        )
      ]);

      // Verify high priority result
      expect(highPriorityResult).toBeDefined();

      // Verify normal priority results
      expect(normalResults).toHaveLength(4);

      // Count fulfilled and rejected promises
      const fulfilledCount = normalResults.filter(result=>result.status === "fulfilled").length;
      const rejectedCount = normalResults.filter(result=>result.status === "rejected").length;

      // Note: In a real implementation, we would verify that high priority operations are prioritized
      // This is a placeholder test that documents the requirement
      console.log(`Priority test: ${fulfilledCount} normal priority operations succeeded, ${rejectedCount} were rate limited`);
    });
  });

  describe("Resource Locks", ()=>{
    it("should handle concurrent access to shared resources with locks", async ()=>{
      // Create a mock lock mechanism
      const locks = new Map();

      // Mock R2.put to simulate locks
      mockEnv.R2.put.mockImplementation(async (key, data)=>{
        // Check if the resource is locked
        if(locks.has(key)) {
          return Promise.reject(new Error(`Resource ${key} is locked`));
        }

        // Lock the resource
        locks.set(key, true);

        // Simulate some processing time
        await delay(100);

        // Unlock the resource
        locks.delete(key);

        return Promise.resolve({ key });
      });

      // Create storage client
      const storageClient = createStorageClient(mockEnv);

      // Perform concurrent put operations on the same key
      const results = await Promise.allSettled([
        storageClient.put("shared-object-key", "data-1"),
        storageClient.put("shared-object-key", "data-2"),
        storageClient.put("shared-object-key", "data-3")
      ]);

      // Verify results
      expect(results).toHaveLength(3);

      // Count fulfilled and rejected promises
      const fulfilledCount = results.filter(result=>result.status === "fulfilled").length;
      const rejectedCount = results.filter(result=>result.status === "rejected").length;

      // Note: In a real implementation, we would verify that resource locks prevent concurrent access
      // This is a placeholder test that documents the requirement
      console.log(`Resource lock test: ${fulfilledCount} operations succeeded, ${rejectedCount} were rejected due to locks`);
    });

    it("should handle deadlocks with timeouts", async ()=>{
      // Create a mock lock mechanism with timeouts
      const locks = new Map();

      // Mock R2.put to simulate locks with timeouts
      mockEnv.R2.put.mockImplementation(async (key, data)=>{
        // Check if the resource is locked
        if(locks.has(key)) {
          const lockTime = locks.get(key);
          const currentTime = Date.now();

          // Check if the lock has timed out (5 seconds)
          if(currentTime - lockTime > 5000) {
            // Lock has timed out, release it
            locks.delete(key);
          } else {
            return Promise.reject(new Error(`Resource ${key} is locked`));
          }
        }

        // Lock the resource with timestamp
        locks.set(key, Date.now());

        // Simulate some processing time
        await delay(100);

        // Unlock the resource
        locks.delete(key);

        return Promise.resolve({ key });
      });

      // Create storage client
      const storageClient = createStorageClient(mockEnv);

      // Perform concurrent put operations on the same key
      const results = await Promise.allSettled([
        storageClient.put("shared-object-key", "data-1"),
        storageClient.put("shared-object-key", "data-2"),
        storageClient.put("shared-object-key", "data-3")
      ]);

      // Verify results
      expect(results).toHaveLength(3);

      // Count fulfilled and rejected promises
      const fulfilledCount = results.filter(result=>result.status === "fulfilled").length;
      const rejectedCount = results.filter(result=>result.status === "rejected").length;

      // Note: In a real implementation, we would verify that deadlocks are resolved with timeouts
      // This is a placeholder test that documents the requirement
      console.log(`Deadlock test: ${fulfilledCount} operations succeeded, ${rejectedCount} were rejected due to locks`);
    });
  });

  describe("Connection Pooling", ()=>{
    it("should handle connection pooling for API requests", async ()=>{
      // Track API connections
      const connections = new Set();
      let maxConcurrentConnections = 0;

      // Mock fetch to simulate connection pooling
      global.fetch.mockImplementation((url, options)=>{
        // Generate a unique connection ID
        const connectionId = `connection-${connections.size + 1}`;

        // Add connection to the pool
        connections.add(connectionId);

        // Update max concurrent connections
        maxConcurrentConnections = Math.max(maxConcurrentConnections, connections.size);

        // Simulate connection limit (max 5 concurrent connections)
        if(connections.size > 5) {
          // Remove connection from the pool
          connections.delete(connectionId);
          return Promise.reject(new Error("Connection limit exceeded"));
        }

        // Process the request
        let response;
        if(url.includes("/fileUrl/init")) {
          response = createFetchResponse(
            {
              total_chunks: 1,
              session_id: `test-session-id-${connectionId}`
            },
            200,
            { "Content-Type": "application/json" }
          );
        } else if(url.includes("/fileUrl/batch")) {
          response = createFetchResponse(
            [
              {
                id: "chunk-1",
                text: "Test chunk",
                metadata: { page: 1 }
              }
            ],
            200,
            { "Content-Type": "application/json" }
          );
        } else {
          response = createFetchResponse(
            { success: true },
            200,
            { "Content-Type": "application/json" }
          );
        }

        // Simulate some processing time
        return delay(100).then(()=>{
          // Remove connection from the pool
          connections.delete(connectionId);
          return response;
        });
      });

      // Create processor instances
      const processors = Array.from({ length: 10 }, ()=>new OpenParseProcessor(
          "test-api-key",
          "https://openparse.example.com",
          mockEnv,
          { useStreaming: false }
        )
      );

      // Process documents concurrently
      const results = await Promise.allSettled(
        processors.map((processor, i)=>processor.process(
            `https://example.com/test-file-${i + 1}.pdf`,
            { semantic: true }
          )
        )
      );

      // Verify results
      expect(results).toHaveLength(10);

      // Count fulfilled and rejected promises
      const fulfilledCount = results.filter(result=>result.status === "fulfilled").length;
      const rejectedCount = results.filter(result=>result.status === "rejected").length;

      // Note: In a real implementation, we would verify that connection pooling limits concurrent connections
      // This is a placeholder test that documents the requirement
      console.log(`Connection pooling test: Max concurrent connections: ${maxConcurrentConnections}, ${fulfilledCount} operations succeeded, ${rejectedCount} were rejected due to connection limits`);
    });
  });

  describe("Resource Quotas", ()=>{
    it("should handle storage quotas", async ()=>{
      // Track storage usage
      let storageUsed = 0;
      const storageQuota = 5000; // 5KB quota

      // Mock R2.put to simulate storage quotas
      mockEnv.R2.put.mockImplementation((key, data)=>{
        // Calculate data size
        const dataSize = typeof data === "string" ? data.length : data.byteLength;

        // Check if quota would be exceeded
        if(storageUsed + dataSize > storageQuota) {
          return Promise.reject(new Error("Storage quota exceeded"));
        }

        // Update storage used
        storageUsed += dataSize;

        return Promise.resolve({ key });
      });

      // Create storage client
      const storageClient = createStorageClient(mockEnv);

      // Generate data of different sizes
      const smallData = "a".repeat(1000); // 1KB
      const mediumData = "b".repeat(2000); // 2KB
      const largeData = "c".repeat(3000); // 3KB
      const hugeData = "d".repeat(6000); // 6KB (exceeds quota)

      // Perform storage operations
      const results = await Promise.allSettled([
        storageClient.put("small-data", smallData),
        storageClient.put("medium-data", mediumData),
        storageClient.put("large-data", largeData),
        storageClient.put("huge-data", hugeData)
      ]);

      // Verify results
      expect(results).toHaveLength(4);

      // Count fulfilled and rejected promises
      const fulfilledCount = results.filter(result=>result.status === "fulfilled").length;
      const rejectedCount = results.filter(result=>result.status === "rejected").length;

      // Note: In a real implementation, we would verify that storage quotas are enforced
      // This is a placeholder test that documents the requirement
      console.log(`Storage quota test: ${storageUsed} bytes used, ${fulfilledCount} operations succeeded, ${rejectedCount} were rejected due to quota limits`);
    });

    it("should handle API request quotas", async ()=>{
      // Track API usage
      let apiRequestsUsed = 0;
      const apiRequestQuota = 5; // 5 requests quota

      // Mock fetch to simulate API request quotas
      global.fetch.mockImplementation((url, options)=>{
        // Increment API requests used
        apiRequestsUsed++;

        // Check if quota would be exceeded
        if(apiRequestsUsed > apiRequestQuota) {
          return Promise.resolve(createFetchResponse(
            { error: "API request quota exceeded" },
            403,
            { "Content-Type": "application/json" }
          ));
        }

        // Process the request
        if(url.includes("/fileUrl/init")) {
          return Promise.resolve(createFetchResponse(
            {
              total_chunks: 1,
              session_id: `test-session-id-${apiRequestsUsed}`
            },
            200,
            { "Content-Type": "application/json" }
          ));
        } else if(url.includes("/fileUrl/batch")) {
          return Promise.resolve(createFetchResponse(
            [
              {
                id: "chunk-1",
                text: "Test chunk",
                metadata: { page: 1 }
              }
            ],
            200,
            { "Content-Type": "application/json" }
          ));
        } else {
          return Promise.resolve(createFetchResponse(
            { success: true },
            200,
            { "Content-Type": "application/json" }
          ));
        }
      });

      // Create processor instances
      const processors = Array.from({ length: 10 }, ()=>new OpenParseProcessor(
          "test-api-key",
          "https://openparse.example.com",
          mockEnv,
          { useStreaming: false }
        )
      );

      // Process documents concurrently
      const results = await Promise.allSettled(
        processors.map((processor, i)=>processor.process(
            `https://example.com/test-file-${i + 1}.pdf`,
            { semantic: true }
          )
        )
      );

      // Verify results
      expect(results).toHaveLength(10);

      // Count fulfilled and rejected promises
      const fulfilledCount = results.filter(result=>result.status === "fulfilled").length;
      const rejectedCount = results.filter(result=>result.status === "rejected").length;

      // Note: In a real implementation, we would verify that API request quotas are enforced
      // This is a placeholder test that documents the requirement
      console.log(`API quota test: ${apiRequestsUsed} requests used, ${fulfilledCount} operations succeeded, ${rejectedCount} were rejected due to quota limits`);
    });
  });
});
