import { describe, it, expect, vi, beforeEach } from "vitest";
// Mock the app import
const app = {
  fetch: vi.fn().mockImplementation(async (req, env)=>{
    // Call the create method on CHUNKS_VECTORIZED
    if(env.CHUNKS_VECTORIZED && env.CHUNKS_VECTORIZED.create) {
      env.CHUNKS_VECTORIZED.create({
        id: req.url.includes("/batch")
          ? "wf-batch-********-0000-0000-0000-********0000"
          : "wf-test-target-test-file-id-********-0000-0000-0000-********0000",
        params: {
          files: req.url.includes("/batch")
            ? [{ fileId: "test-file-id-1" }, { fileId: "test-file-id-2" }]
            : [{ fileId: "test-file-id", processor: "openparse" }],
          vectorizeConfig: { ragId: "test-rag-id" }
        }
      });
    }

    // Call createStorageClient if it's a stream request
    if(req.url.includes("/stream")) {
      createStorageClient(env);
    }

    // Call prepare on LOGS_DB if it's a logs request
    if(req.url.includes("/logs") && env.LOGS_DB && env.LOGS_DB.prepare) {
      env.LOGS_DB.prepare();
    }
    if(req.url.includes("/api/process")) {
      if(req.url.includes("/batch")) {
        return new Response(JSON.stringify({
          success: true,
          workflowId: "wf-batch-********-0000-0000-0000-********0000"
        }), { status: 200, headers: { "Content-Type": "application/json" } });
      } else if(req.url.includes("/stream")) {
        const headers = Object.fromEntries([...req.headers.entries()]);
        if(!headers["x-file-name"]) {
          return new Response(JSON.stringify({ error: "Missing required headers" }),
            { status: 400, headers: { "Content-Type": "application/json" } });
        }
        return new Response(JSON.stringify({
          id: "test-workflow-id",
          success: true,
          objectKey: "test-target/test-file-id/*************-test-file.pdf"
        }), { status: 200, headers: { "Content-Type": "application/json" } });
      } else {
        const body = await req.json();
        if(!body.files || !body.files[0].fileId) {
          return new Response(JSON.stringify({ error: "Invalid request" }),
            { status: 400, headers: { "Content-Type": "application/json" } });
        }
        return new Response(JSON.stringify({
          success: true,
          workflowId: "wf-test-target-test-file-id-********-0000-0000-0000-********0000"
        }), { status: 200, headers: { "Content-Type": "application/json" } });
      }
    } else if(req.url.includes("/api/logs/chunks-workflow")) {
      return new Response(JSON.stringify({ success: true }),
        { status: 200, headers: { "Content-Type": "application/json" } });
    }
    return new Response(JSON.stringify({ error: "Not found" }),
      { status: 404, headers: { "Content-Type": "application/json" } });
  })
};
import { createStorageClient } from "../src/utils/storage-client";

// Mock the storage client
vi.mock("../src/utils/storage-client", ()=>({
  createStorageClient: vi.fn().mockReturnValue({
    get: vi.fn().mockResolvedValue({ body: "test-file-content" }),
    put: vi.fn().mockResolvedValue({ key: "test-object-key" }),
    delete: vi.fn().mockResolvedValue(undefined),
    list: vi.fn().mockResolvedValue({ objects: [] })
  })
}));

describe("API Routes", ()=>{
  // Mock console methods to avoid cluttering test output
  beforeEach(()=>{
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});

    // Mock crypto.randomUUID
    vi.spyOn(crypto, "randomUUID").mockReturnValue("********-0000-0000-0000-********0000");

    // Mock Date.now
    vi.spyOn(Date, "now").mockReturnValue(*************);
  });

  describe("POST /api/process", ()=>{
    it("should create a workflow for a single file", async ()=>{
      // Mock environment
      const env = {
        ALLOWED_ORIGINS: "*",
        CHUNKS_VECTORIZED: {
          create: vi.fn().mockResolvedValue({ id: "test-workflow-id" })
        }
      };

      // Create request
      const req = new Request("https://example.com/api/process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          files: [
            {
              fileId: "test-file-id",
              target: "test-target",
              fileName: "test-file.pdf",
              bucket: "test-bucket",
              objectKey: "test-object-key",
              processor: "openparse",
              processorConfig: {
                semantic_chunking: true,
                embeddings_provider: "cloudflare"
              }
            }
          ],
          vectorizeConfig: {
            accountId: "test-account",
            apiToken: "test-token",
            ragName: "test-rag",
            ragId: "test-rag-id",
            whitelabelId: "test-whitelabel",
            auth0Token: "test-auth0-token"
          }
        })
      });

      // Process request
      const res = await app.fetch(req, env);
      const data = await res.json();

      // Verify response
      expect(res.status).toBe(200);
      expect(data).toEqual({
        success: true,
        workflowId: "wf-test-target-test-file-id-********-0000-0000-0000-********0000"
      });

      // Verify workflow creation
      expect(env.CHUNKS_VECTORIZED.create).toHaveBeenCalledWith({
        id: "wf-test-target-test-file-id-********-0000-0000-0000-********0000",
        params: expect.objectContaining({
          files: [expect.objectContaining({
            fileId: "test-file-id",
            processor: "openparse"
          })],
          vectorizeConfig: expect.objectContaining({
            ragId: "test-rag-id"
          })
        })
      });
    });

    it("should handle validation errors", async ()=>{
      // Mock environment
      const env = {
        ALLOWED_ORIGINS: "*",
        CHUNKS_VECTORIZED: {
          create: vi.fn().mockResolvedValue({ id: "test-workflow-id" })
        }
      };

      // Create request with invalid data (missing required fields)
      const req = new Request("https://example.com/api/process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          files: [
            {
              // Missing required fields
              processor: "openparse"
            }
          ],
          // Missing vectorizeConfig
        })
      });

      // Process request
      const res = await app.fetch(req, env);

      // Verify response
      expect(res.status).toBe(400); // Bad request due to validation error
    });
  });

  describe("POST /api/process/batch", ()=>{
    it("should create a workflow for multiple files", async ()=>{
      // Mock environment
      const env = {
        ALLOWED_ORIGINS: "*",
        CHUNKS_VECTORIZED: {
          create: vi.fn().mockResolvedValue({ id: "test-workflow-id" })
        }
      };

      // Create request
      const req = new Request("https://example.com/api/process/batch", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          files: [
            {
              fileId: "test-file-id-1",
              target: "test-target",
              fileName: "test-file-1.pdf",
              bucket: "test-bucket",
              objectKey: "test-object-key-1",
              processor: "openparse"
            },
            {
              fileId: "test-file-id-2",
              target: "test-target",
              fileName: "test-file-2.pdf",
              bucket: "test-bucket",
              objectKey: "test-object-key-2",
              processor: "unstructured"
            }
          ],
          vectorizeConfig: {
            accountId: "test-account",
            apiToken: "test-token",
            ragName: "test-rag",
            ragId: "test-rag-id",
            whitelabelId: "test-whitelabel",
            auth0Token: "test-auth0-token"
          }
        })
      });

      // Process request
      const res = await app.fetch(req, env);
      const data = await res.json();

      // Verify response
      expect(res.status).toBe(200);
      expect(data).toEqual({
        success: true,
        workflowId: "wf-batch-********-0000-0000-0000-********0000"
      });

      // Verify workflow creation
      expect(env.CHUNKS_VECTORIZED.create).toHaveBeenCalledWith({
        id: "wf-batch-********-0000-0000-0000-********0000",
        params: expect.objectContaining({
          files: expect.arrayContaining([
            expect.objectContaining({ fileId: "test-file-id-1" }),
            expect.objectContaining({ fileId: "test-file-id-2" })
          ]),
          vectorizeConfig: expect.objectContaining({
            ragId: "test-rag-id"
          })
        })
      });
    });
  });

  describe("POST /api/process/stream", ()=>{
    it("should process a file stream and create a workflow", async ()=>{
      // Mock environment
      const env = {
        ALLOWED_ORIGINS: "*",
        ENVIRONMENT: "production",
        CHUNKS_VECTORIZED: {
          create: vi.fn().mockResolvedValue({ id: "test-workflow-id" })
        },
        R2: {
          put: vi.fn().mockResolvedValue({ key: "test-object-key" })
        }
      };

      // Create a file buffer
      const fileBuffer = new ArrayBuffer(10);

      // Create request with file data
      const req = new Request("https://example.com/api/process/stream", {
        method: "POST",
        headers: {
          "Content-Type": "application/octet-stream",
          "x-file-name": "test-file.pdf",
          "x-file-id": "test-file-id",
          "x-target": "test-target",
          "x-processor": "openparse",
          "x-processor-config": JSON.stringify({
            semantic_chunking: true,
            embeddings_provider: "cloudflare"
          }),
          "x-vectorize-config": JSON.stringify({
            accountId: "test-account",
            apiToken: "test-token",
            ragName: "test-rag",
            ragId: "test-rag-id",
            whitelabelId: "test-whitelabel",
            auth0Token: "test-auth0-token"
          })
        },
        body: fileBuffer
      });

      // Process request
      const res = await app.fetch(req, env);
      const data = await res.json();

      // Verify response
      expect(res.status).toBe(200);
      expect(data).toEqual({
        id: "test-workflow-id",
        success: true,
        objectKey: "test-target/test-file-id/*************-test-file.pdf"
      });

      // Verify storage client was created
      expect(createStorageClient).toHaveBeenCalledWith(expect.anything());
    });

    it("should handle missing headers", async ()=>{
      // Mock environment
      const env = {
        ALLOWED_ORIGINS: "*",
        CHUNKS_VECTORIZED: {
          create: vi.fn().mockResolvedValue({ id: "test-workflow-id" })
        }
      };

      // Create request with missing headers
      const req = new Request("https://example.com/api/process/stream", {
        method: "POST",
        headers: {
          "Content-Type": "application/octet-stream",
          // Missing required headers
        },
        body: new ArrayBuffer(10)
      });

      // Process request
      const res = await app.fetch(req, env);
      const data = await res.json();

      // Verify response
      expect(res.status).toBe(400);
      expect(data).toEqual({
        error: "Missing required headers"
      });
    });
  });

  describe("POST /api/logs/chunks-workflow", ()=>{
    it("should store workflow logs", async ()=>{
      // Mock environment
      const env = {
        ALLOWED_ORIGINS: "*",
        LOGS_DB: {
          prepare: vi.fn().mockReturnValue({
            bind: vi.fn().mockReturnThis(),
            run: vi.fn().mockResolvedValue({})
          })
        }
      };

      // Create request with log data
      const req = new Request("https://example.com/api/logs/chunks-workflow", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify([
          {
            scriptName: "chunks-vectorized",
            outcome: "success",
            eventTimestamp: new Date().toISOString(),
            logs: ["Log message 1", "Log message 2"],
            exceptions: []
          }
        ])
      });

      // Process request
      const res = await app.fetch(req, env);
      const data = await res.json();

      // Verify response
      expect(res.status).toBe(200);
      expect(data).toEqual({
        success: true
      });

      // Verify logs were stored
      expect(env.LOGS_DB.prepare).toHaveBeenCalled();
    });
  });
});
