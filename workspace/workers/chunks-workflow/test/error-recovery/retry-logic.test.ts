import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { createFetchResponse } from "../setup-comprehensive";
import { retry } from "../../src/utils/retry";

// Create a simplified mock of OpenParseProcessor for testing
class OpenParseProcessor {
  private apiKey: string;
  private apiUrl: string;
  private existingSessionId: string | null = null;

  constructor(apiKey: string, apiUrl: string, env: any, options: any = {}){
    this.apiKey = apiKey;
    this.apiUrl = apiUrl;
  }

  async initializeSession(fileUrl: string, config: any){
    const response = await fetch(`${this.apiUrl}/fileUrl/init`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        url: fileUrl,
        config: {
          semantic_chunking: config.semantic || false,
          embeddings_provider: config.embeddingsProvider || "none",
          minTokens: config.minTokens || 256,
          maxTokens: config.maxTokens || 1024,
          overlap: config.chunkOverlap || 200,
          useTokens: config.useTokens !== false,
          relevance_threshold: config.relevanceThreshold || 0.3,
        }
      })
    });

    if(!response.ok) {
      const errorText = await response.text();
      throw new Error(`❌ OpenParse init failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    this.existingSessionId = data.session_id;

    return {
      sessionId: data.session_id,
      totalChunks: data.total_chunks
    };
  }

  async processBatch(batchNumber: number, options: any = {}){
    if(!this.existingSessionId) {
      throw new Error("Session not initialized. Call initializeSession first.");
    }

    const batchSize = options.batchSize || 50;

    const response = await fetch(`${this.apiUrl}/fileUrl/batch`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        session_id: this.existingSessionId,
        batch_number: batchNumber,
        batch_size: batchSize
      })
    });

    if(!response.ok) {
      const errorText = await response.text();

      if(response.status === 404) {
        throw new Error("Session expired");
      }

      throw new Error(`❌ Failed to fetch batch: ${response.status} - ${errorText}`);
    }

    const chunks = await response.json();

    if(!Array.isArray(chunks)) {
      throw new Error("❌ Invalid batch response format");
    }

    // Return null if no chunks (end of processing)
    if(chunks.length === 0) {
      return null;
    }

    return chunks;
  }

  async process(fileUrl: string, config: any){
    await this.initializeSession(fileUrl, config);
    const chunks: Array<{ text: string, metadata?: any }> = [];

    let batchNumber = 0;
    let batch: Array<{ text: string, metadata?: any }> | null;

    do {
      batch = await this.processBatch(batchNumber, { batchSize: 50 });
      if(batch) {
        chunks.push(...batch);
      }
      batchNumber++;
    } while(batch !== null);

    return chunks;
  }

  async dispose(){
    if(this.existingSessionId) {
      try {
        // Try the primary cleanup endpoint
        try {
          const response = await fetch(`${this.apiUrl}/cleanup/${this.existingSessionId}`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${this.apiKey}`
            }
          });

          if(!response.ok && response.status === 404) {
            // If the endpoint doesn't exist (404), try the alternative endpoint
            const altResponse = await fetch(`${this.apiUrl}/session/${this.existingSessionId}/cleanup`, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${this.apiKey}`
              }
            });
          }
        }catch(cleanupError: any) {
          console.warn(`⚠️ Error during OpenParse cleanup: ${cleanupError.message}`);
        }

        // Clear the session ID
        this.existingSessionId = null;
      }catch(error) {
        console.warn(`⚠️ Error cleaning up OpenParse session: ${error}`);
      }
    }

    return Promise.resolve();
  }
}

// Create a utility function to simulate a function that fails a certain number of times
const createFailingFunction = (failCount: number, successValue: any = "success")=>{
  let attempts = 0;
  return vi.fn().mockImplementation(()=>{
    attempts++;
    if(attempts <= failCount) {
      return Promise.reject(new Error(`Attempt ${attempts} failed`));
    }
    return Promise.resolve(successValue);
  });
};

describe("Error Recovery and Retry Logic", ()=>{
  beforeEach(()=>{
    // Mock console methods to avoid cluttering test output
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});

    // Reset fetch mock
    global.fetch = vi.fn();
  });

  afterEach(()=>{
    vi.restoreAllMocks();
  });

  describe("Retry Utility", ()=>{
    it("should retry a failed operation until it succeeds", async ()=>{
      // Create a function that fails twice and succeeds on the third attempt
      const failingFunction = createFailingFunction(2);

      // Retry the function with 3 max attempts
      const result = await retry(failingFunction, [], {
        maxAttempts: 3,
        delayMs: 10,
        backoffFactor: 1.5
      });

      // Verify the function was called 3 times
      expect(failingFunction).toHaveBeenCalledTimes(3);

      // Verify the result is correct
      expect(result).toBe("success");
    });

    it("should fail after reaching max attempts", async ()=>{
      // Create a function that always fails
      const failingFunction = createFailingFunction(5);

      // Retry the function with 3 max attempts
      await expect(retry(failingFunction, [], {
        maxAttempts: 3,
        delayMs: 10,
        backoffFactor: 1.5
      })).rejects.toThrow("Attempt 3 failed");

      // Verify the function was called 3 times
      expect(failingFunction).toHaveBeenCalledTimes(3);
    });

    it("should use exponential backoff for retries", async ()=>{
      // Mock setTimeout
      const originalSetTimeout = global.setTimeout;
      global.setTimeout = vi.fn().mockImplementation((callback)=>{
        callback();
        return 0 as any;
      });

      // Create a function that fails 3 times
      const failingFunction = createFailingFunction(3);

      // Retry the function with exponential backoff
      try {
        await retry(failingFunction, [], {
          maxAttempts: 3,
          delayMs: 100,
          backoffFactor: 2
        });
      }catch(error) {
        // Expected to fail
      }

      // Verify setTimeout was called with increasing delays
      expect(global.setTimeout).toHaveBeenCalledTimes(2); // Only 2 retries need delays
      expect(global.setTimeout).toHaveBeenNthCalledWith(1, expect.any(Function), 100);
      expect(global.setTimeout).toHaveBeenNthCalledWith(2, expect.any(Function), 200); // 100 * 2

      // Restore setTimeout
      global.setTimeout = originalSetTimeout;
    });
  });

  describe("OpenParse Processor Retry Logic", ()=>{
    it("should retry initialization when the API returns a transient error", async ()=>{
      // Mock fetch to fail with 503 Service Unavailable twice, then succeed
      global.fetch.mockImplementation((url, options)=>{
        if(url.includes("/fileUrl/init")) {
          if(global.fetch.mock.calls.filter(call=>call[0].includes("/fileUrl/init")).length < 2) {
            return Promise.resolve(createFetchResponse(
              { error: "Service Unavailable" },
              503,
              { "Content-Type": "application/json" }
            ));
          } else {
            return Promise.resolve(createFetchResponse(
              {
                total_chunks: 2,
                session_id: "test-session-id"
              },
              200,
              { "Content-Type": "application/json" }
            ));
          }
        }

        // Default response for other URLs
        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        {}
      );

      // Create a retryable version of the initializeSession method
      const retryableInitializeSession = async (fileUrl: string, config: any)=>{
        return await retry(
          ()=>processor.initializeSession(fileUrl, config),
          [],
          {
            maxAttempts: 3,
            delayMs: 10,
            backoffFactor: 1.5
          }
        );
      };

      // Initialize session with retry
      const result = await retryableInitializeSession(
        "https://example.com/test-file.pdf",
        {
          semantic: true,
          embeddingsProvider: "openai"
        }
      );

      // Verify result
      expect(result).toEqual({
        sessionId: "test-session-id",
        totalChunks: 2
      });

      // Verify fetch was called at least 2 times (1 failure + 1 success)
      expect(global.fetch).toHaveBeenCalledTimes(2);
    });

    it("should retry batch processing when the API returns a transient error", async ()=>{
      // Mock fetch to succeed for initialization, fail for first batch attempt, then succeed
      global.fetch.mockImplementation((url, options)=>{
        if(url.includes("/fileUrl/init")) {
          return Promise.resolve(createFetchResponse(
            {
              total_chunks: 2,
              session_id: "test-session-id"
            },
            200,
            { "Content-Type": "application/json" }
          ));
        }

        if(url.includes("/fileUrl/batch")) {
          const batchCalls = global.fetch.mock.calls.filter(call=>call[0].includes("/fileUrl/batch"));

          if(batchCalls.length === 1) {
            return Promise.resolve(createFetchResponse(
              { error: "Service Unavailable" },
              503,
              { "Content-Type": "application/json" }
            ));
          } else if(JSON.parse(options.body).batch_number === 0) {
            return Promise.resolve(createFetchResponse(
              [
                { id: "chunk-1", text: "Test chunk 1", metadata: {} },
                { id: "chunk-2", text: "Test chunk 2", metadata: {} }
              ],
              200,
              { "Content-Type": "application/json" }
            ));
          } else {
            return Promise.resolve(createFetchResponse(
              [],
              200,
              { "Content-Type": "application/json" }
            ));
          }
        }

        // Default response for other URLs
        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        {}
      );

      // Create a retryable version of the processBatch method
      const retryableProcessBatch = async (batchNumber: number, options: any)=>{
        return await retry(
          ()=>processor.processBatch(batchNumber, options),
          [],
          {
            maxAttempts: 3,
            delayMs: 10,
            backoffFactor: 1.5
          }
        );
      };

      // Initialize session (no retry needed for this test)
      await processor.initializeSession(
        "https://example.com/test-file.pdf",
        {
          semantic: true,
          embeddingsProvider: "openai"
        }
      );

      // Process batch with retry
      const batch = await retryableProcessBatch(0, { batchSize: 50 });

      // Verify batch
      expect(batch).toHaveLength(2);
      expect(batch[0]).toEqual({
        id: "chunk-1",
        text: "Test chunk 1",
        metadata: {}
      });

      // Verify fetch was called 3 times (1 init + 2 batch attempts)
      expect(global.fetch).toHaveBeenCalledTimes(3);
    });

    it("should handle network errors and retry", async ()=>{
      // Mock fetch to throw network errors then succeed
      global.fetch.mockImplementationOnce(()=>{
        throw new Error("Network error");
      }).mockImplementationOnce(()=>{
        throw new Error("Connection reset");
      }).mockImplementationOnce(()=>{
        return Promise.resolve(createFetchResponse(
          {
            total_chunks: 2,
            session_id: "test-session-id"
          },
          200,
          { "Content-Type": "application/json" }
        ));
      }).mockImplementationOnce(()=>{
        return Promise.resolve(createFetchResponse(
          [
            { id: "chunk-1", text: "Test chunk 1", metadata: {} },
            { id: "chunk-2", text: "Test chunk 2", metadata: {} }
          ],
          200,
          { "Content-Type": "application/json" }
        ));
      }).mockImplementationOnce(()=>{
        return Promise.resolve(createFetchResponse(
          [],
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        {}
      );

      // Create a retryable version of the process method
      const retryableProcess = async (fileUrl: string, config: any)=>{
        // First retry the initialization
        await retry(
          ()=>processor.initializeSession(fileUrl, config),
          [],
          {
            maxAttempts: 3,
            delayMs: 10,
            backoffFactor: 1.5
          }
        );

        // Then process batches with retry
        const chunks: Array<{ text: string, metadata?: any }> = [];
        let batchNumber = 0;
        let batch: Array<{ text: string, metadata?: any }> | null;

        do {
          batch = await retry(
            ()=>processor.processBatch(batchNumber, { batchSize: 50 }),
            [],
            {
              maxAttempts: 3,
              delayMs: 10,
              backoffFactor: 1.5
            }
          );

          if(batch) {
            chunks.push(...batch);
          }

          batchNumber++;
        } while(batch !== null);

        return chunks;
      };

      // Process document with retry
      const chunks = await retryableProcess(
        "https://example.com/test-file.pdf",
        {
          semantic: true,
          embeddingsProvider: "openai"
        }
      );

      // Verify chunks
      expect(chunks).toHaveLength(2);

      // Verify fetch was called 5 times (3 init attempts + 1 batch + 1 empty batch)
      expect(global.fetch).toHaveBeenCalledTimes(5);
    });
  });

  describe("Session Recovery", ()=>{
    it("should recover from an expired session by reinitializing", async ()=>{
      // Mock fetch to succeed for initialization, return session expired for batch, then succeed with new session
      let sessionId = "test-session-id-1";

      global.fetch.mockImplementation((url, options)=>{
        if(url.includes("/fileUrl/init")) {
          sessionId = `test-session-id-${global.fetch.mock.calls.filter(call=>call[0].includes("/fileUrl/init")).length + 1}`;
          return Promise.resolve(createFetchResponse(
            {
              total_chunks: 2,
              session_id: sessionId
            },
            200,
            { "Content-Type": "application/json" }
          ));
        }

        if(url.includes("/fileUrl/batch")) {
          const requestBody = JSON.parse(options.body);

          // If using the first session ID and it's the first batch call, return session expired
          if(requestBody.session_id === "test-session-id-1" &&
              global.fetch.mock.calls.filter(call=>call[0].includes("/fileUrl/batch")).length === 1) {
            return Promise.resolve(createFetchResponse(
              { error: "Session expired" },
              404,
              { "Content-Type": "application/json" }
            ));
          } else if(requestBody.batch_number === 0) {
            return Promise.resolve(createFetchResponse(
              [
                { id: "chunk-1", text: "Test chunk 1", metadata: {} },
                { id: "chunk-2", text: "Test chunk 2", metadata: {} }
              ],
              200,
              { "Content-Type": "application/json" }
            ));
          } else {
            return Promise.resolve(createFetchResponse(
              [],
              200,
              { "Content-Type": "application/json" }
            ));
          }
        }

        // Default response for other URLs
        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        {}
      );

      // Process document with session recovery
      const processWithRecovery = async (fileUrl: string, config: any)=>{
        // First initialize the session
        await processor.initializeSession(fileUrl, config);

        const chunks: Array<{ text: string, metadata?: any }> = [];
        let batchNumber = 0;
        let batch: Array<{ text: string, metadata?: any }> | null;

        do {
          try {
            batch = await processor.processBatch(batchNumber, { batchSize: 50 });
          }catch(error: any) {
            // If session expired, reinitialize and retry
            if(error.message.includes("Session expired")) {
              console.log("Session expired, reinitializing...");
              await processor.initializeSession(fileUrl, config);
              batch = await processor.processBatch(batchNumber, { batchSize: 50 });
            } else {
              throw error;
            }
          }

          if(batch) {
            chunks.push(...batch);
          }

          batchNumber++;
        } while(batch !== null);

        return chunks;
      };

      // Process document with session recovery
      const chunks = await processWithRecovery(
        "https://example.com/test-file.pdf",
        {
          semantic: true,
          embeddingsProvider: "openai"
        }
      );

      // Verify chunks
      expect(chunks).toHaveLength(2);

      // Verify fetch was called at least 3 times (1 init + 1 failed batch + 1 reinit)
      expect(global.fetch).toHaveBeenCalledTimes(3);

      // Verify the second session ID was used
      const lastBatchCall = global.fetch.mock.calls.filter(call=>call[0].includes("/fileUrl/batch")).pop();
      expect(JSON.parse(lastBatchCall[1].body).session_id).toBe("test-session-id-2");
    });
  });

  describe("Partial Success Handling", ()=>{
    it("should continue processing after some chunks fail", async ()=>{
      // Mock fetch to succeed for initialization, return partial success for batch
      global.fetch.mockImplementation((url, options)=>{
        if(url.includes("/fileUrl/init")) {
          return Promise.resolve(createFetchResponse(
            {
              total_chunks: 3,
              session_id: "test-session-id"
            },
            200,
            { "Content-Type": "application/json" }
          ));
        }

        if(url.includes("/fileUrl/batch")) {
          const batchNumber = JSON.parse(options.body).batch_number;

          if(batchNumber === 0) {
            return Promise.resolve(createFetchResponse(
              [
                { id: "chunk-1", text: "Test chunk 1", metadata: {} },
                { id: "chunk-2", text: "Test chunk 2", metadata: {} }
              ],
              200,
              { "Content-Type": "application/json" }
            ));
          } else if(batchNumber === 1) {
            // Simulate a partial failure where some chunks are processed but with errors
            return Promise.resolve(createFetchResponse(
              [
                {
                  id: "chunk-3",
                  text: "Test chunk 3",
                  metadata: {},
                  warning: "Partial processing due to content issues"
                }
              ],
              206, // Partial Content
              { "Content-Type": "application/json" }
            ));
          } else {
            return Promise.resolve(createFetchResponse(
              [],
              200,
              { "Content-Type": "application/json" }
            ));
          }
        }

        // Default response for other URLs
        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        {}
      );

      // Process document
      const chunks = await processor.process(
        "https://example.com/test-file.pdf",
        {
          semantic: true,
          embeddingsProvider: "openai"
        }
      );

      // Verify all chunks were processed, including the one with warning
      expect(chunks).toHaveLength(3);
      expect(chunks[2]).toHaveProperty("warning");

      // Verify fetch was called 4 times (1 init + 2 batches + 1 empty batch)
      expect(global.fetch).toHaveBeenCalledTimes(4);
    });
  });
});
