import { describe, it, expect, vi, beforeEach } from "vitest";
import { OpenParseProcessor } from "../../src/processors/openparse";

describe("OpenParseProcessor", ()=>{
  // Mock console methods to avoid cluttering test output
  beforeEach(()=>{
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});
  });

  it("should be instantiable", ()=>{
    // Create processor instance
    const processor = new OpenParseProcessor(
      "test-api-key",
      "https://openparse.example.com",
      {},
      { useStreaming: true }
    );

    // Verify instance
    expect(processor).toBeInstanceOf(OpenParseProcessor);
  });
});
