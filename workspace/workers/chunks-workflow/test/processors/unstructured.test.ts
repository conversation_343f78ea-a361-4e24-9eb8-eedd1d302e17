import { describe, it, expect, vi, beforeEach } from "vitest";
import { UnstructuredProcessor } from "../../src/processors/unstructured";

describe("UnstructuredProcessor", ()=>{
  // Mock console methods to avoid cluttering test output
  beforeEach(()=>{
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});
  });

  it("should be instantiable", ()=>{
    // Create processor instance
    const processor = new UnstructuredProcessor(
      "test-api-key",
      "https://unstructured.example.com",
      {},
      { useStreaming: true }
    );

    // Verify instance
    expect(processor).toBeInstanceOf(UnstructuredProcessor);
  });
});
