import { describe, it, expect, vi, beforeEach } from "vitest";
import { UnstructuredProcessor } from "../../src/processors/unstructured";

describe("UnstructuredProcessor", ()=>{
  // Mock console methods to avoid cluttering test output
  beforeEach(()=>{
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});

    // Reset fetch mock
    global.fetch = vi.fn();
  });

  describe("initializeSession", ()=>{
    it("should initialize a session successfully", async ()=>{
      // Mock fetch response for initialization
      global.fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: ()=>Promise.resolve({
          session_id: "test-session-id",
          total_chunks: 10
        })
      });

      // Create processor instance
      const processor = new UnstructuredProcessor(
        "https://unstructured.example.com"
      );

      // Initialize session
      const result = await processor.initializeSession(
        {
          chunkingStrategy: "by_title",
          maxCharacters: 1024,
          splitPdfPage: true
        },
        "https://example.com/test-file.pdf"
      );

      // Verify result
      expect(result).toEqual({
        sessionId: "test-session-id",
        totalChunks: 10
      });

      // Verify fetch was called correctly
      expect(global.fetch).toHaveBeenCalledWith(
        "https://unstructured.example.com/fileUrl/init",
        expect.objectContaining({
          method: "POST",
          headers: expect.objectContaining({
            "Content-Type": "application/json"
          }),
          body: expect.stringContaining("by_title")
        })
      );
    });

    it("should handle initialization errors", async ()=>{
      // Mock fetch response for initialization
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: "Internal Server Error",
        text: ()=>Promise.resolve("Server error")
      });

      // Create processor instance
      const processor = new UnstructuredProcessor(
        "https://unstructured.example.com"
      );

      // Initialize session should throw
      await expect(processor.initializeSession(
        {
          chunkingStrategy: "by_title"
        },
        "https://example.com/test-file.pdf"
      )).rejects.toThrow("Worker initialization failed");
    });
  });

  describe("processBatch", ()=>{
    it("should process a batch and return chunks", async ()=>{
      // Mock fetch response
      global.fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: ()=>Promise.resolve([
          {
            text: "Test chunk 1",
            metadata: { page_number: 1, type: "Title" }
          },
          {
            text: "Test chunk 2",
            metadata: { page_number: 1, type: "Text" }
          }
        ])
      });

      // Create processor instance
      const processor = new UnstructuredProcessor(
        "https://unstructured.example.com"
      );

      // Process batch
      const chunks = await processor.processBatch("test-session-id", 1, 50);

      // Verify chunks
      expect(chunks).toHaveLength(2);
      expect(chunks[0].text).toBe("Test chunk 1");
      expect(chunks[0].metadata).toEqual({ page_number: 1, type: "Title" });
      expect(chunks[1].text).toBe("Test chunk 2");
      expect(chunks[1].metadata).toEqual({ page_number: 1, type: "Text" });

      // Verify fetch was called correctly
      expect(global.fetch).toHaveBeenCalledWith(
        "https://unstructured.example.com/fileUrl/batch",
        expect.objectContaining({
          method: "POST",
          headers: expect.objectContaining({
            "Content-Type": "application/json"
          }),
          body: expect.stringContaining("test-session-id")
        })
      );
    });

    it("should handle batch processing errors", async ()=>{
      // Mock fetch response
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: "Internal Server Error",
        text: ()=>Promise.resolve("Server error")
      });

      // Create processor instance
      const processor = new UnstructuredProcessor(
        "https://unstructured.example.com"
      );

      // Process batch should throw
      await expect(processor.processBatch("test-session-id", 1, 50))
        .rejects.toThrow("Batch processing failed");
    });

    it("should handle session expired errors", async ()=>{
      // Mock fetch response
      global.fetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: "Not Found",
        text: ()=>Promise.resolve("Session not found")
      });

      // Create processor instance
      const processor = new UnstructuredProcessor(
        "https://unstructured.example.com"
      );

      // Process batch should throw with specific message
      await expect(processor.processBatch("test-session-id", 1, 50))
        .rejects.toThrow("Session expired");
    });
  });

  describe("dispose", ()=>{
    it("should not throw when disposing resources", async ()=>{
      // Create processor instance
      const processor = new UnstructuredProcessor(
        "https://unstructured.example.com"
      );

      // Dispose should not throw
      await expect(processor.dispose()).resolves.not.toThrow();
    });
  });
});
