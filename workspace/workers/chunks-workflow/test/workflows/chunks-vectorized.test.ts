import { describe, it, expect, vi, beforeEach } from "vitest";
import { ChunksVectorizedWorkflow } from "../mocks/chunks-vectorized.mock.js";

describe("ChunksVectorizedWorkflow", ()=>{
  // Mock console methods to avoid cluttering test output
  beforeEach(()=>{
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});
  });

  it("should be instantiable", ()=>{
    // Create a mock environment
    const env = {
      OPENPARSE_API_KEY: "test-key",
      OPENPARSE_API_URL: "https://openparse.example.com",
      UNSTRUCTURED_API_KEY: "test-key",
      UNSTRUCTURED_API_URL: "https://unstructured.example.com",
      VECTORIZE_API_TOKEN: "test-token",
      VECTORIZE_ACCOUNT_ID: "test-account",
      VECTORIZE_INDEX_NAME: "test-index",
      ENVIRONMENT: "test"
    };

    // Create workflow instance
    const workflow = new ChunksVectorizedWorkflow(env);

    // Verify instance
    expect(workflow).toBeDefined();
  });

  it("should run successfully", async ()=>{
    // Create a mock environment
    const env = {
      OPENPARSE_API_KEY: "test-key",
      OPENPARSE_API_URL: "https://openparse.example.com",
      UNSTRUCTURED_API_KEY: "test-key",
      UNSTRUCTURED_API_URL: "https://unstructured.example.com",
      VECTORIZE_API_TOKEN: "test-token",
      VECTORIZE_ACCOUNT_ID: "test-account",
      VECTORIZE_INDEX_NAME: "test-index",
      ENVIRONMENT: "test"
    };

    // Create workflow instance
    const workflow = new ChunksVectorizedWorkflow(env);

    // Run workflow
    const result = await workflow.run({
      data: {
        id: "test-workflow-id",
        params: {
          files: [
            {
              fileId: "test-file-id",
              processor: "openparse"
            }
          ],
          vectorizeConfig: {
            ragId: "test-rag-id"
          }
        }
      }
    });

    // Verify result
    expect(result).toEqual({
      success: true,
      workflowId: "test-workflow-id",
      fileId: "test-file-id",
      status: "success"
    });
  });
});
