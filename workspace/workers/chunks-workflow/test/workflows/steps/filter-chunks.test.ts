import { describe, it, expect, vi, beforeEach } from "vitest";
import { validateAndFilterChunks } from "../../../src/workflows/steps/filter-chunks";
import { createVectorId, countTokens, getRelevanceThreshold } from "../../../src/utils";
import { WorkflowStep } from "cloudflare:workers";
import { StepContext, InitialSetup, ChunkData } from "../../../src/workflows/steps/types";

// Mock dependencies
vi.mock("../../../src/utils", () => ({
  createVectorId: vi.fn((fileId, index) => `${fileId}-${index}`),
  countTokens: vi.fn((text) => Math.ceil(text.length / 4)), // Simple mock implementation
  getRelevanceThreshold: vi.fn(() => 0.3),
}));

describe("filter-chunks", () => {
  // Mock console.log to avoid cluttering test output
  beforeEach(() => {
    vi.spyOn(console, "log").mockImplementation(() => {});
    vi.spyOn(console, "error").mockImplementation(() => {});
    vi.spyOn(console, "warn").mockImplementation(() => {});
  });

  // Mock WorkflowStep
  const mockStep = {
    do: vi.fn((name, fn) => fn()),
  } as unknown as WorkflowStep;

  // Mock StepContext
  const mockContext = {
    env: {
      AI: {
        run: vi.fn(),
      },
    },
  } as unknown as StepContext;

  // Mock InitialSetup
  const mockInitialSetup = {
    fileInfo: {
      fileId: "test-file-id",
      fileName: "test-file.pdf",
      objectKey: "test/object/key",
    },
    metadata: {
      target: "test-whitelabel-id",
      steps: {
        deJunk: {
          skipped: false,
          chunksBeforeDeJunk: 0,
          chunksAfterDeJunk: 0,
        },
      },
    },
    normalizedConfig: {
      skipDeJunk: true,
    },
  } as unknown as InitialSetup;

  describe("validateAndFilterChunks", () => {
    it("should process string chunks correctly", async () => {
      // Create an array of 100 string chunks
      const documentChunks = Array(100).fill(null).map((_, i) => `Chunk ${i} content`);

      const result = await validateAndFilterChunks(mockStep, mockContext, mockInitialSetup, documentChunks);

      // Verify results
      expect(result.length).toBe(100);
      expect(result[0].id).toBe("test-file-id-0");
      expect(result[0].text).toBe("Chunk 0 content");
      expect(result[0].metadata.whiteLabelId).toBe("test-whitelabel-id");

      // Verify batch processing
      expect(mockStep.do).toHaveBeenCalledTimes(0); // skipDeJunk is true, so filterChunks is not called
      // With batch processing, createVectorId and countTokens might be called multiple times
      // We only care that they were called at least once for each chunk
      expect(createVectorId).toHaveBeenCalled();
      expect(countTokens).toHaveBeenCalled();
      expect(result.length).toBe(100); // Ensure we got all chunks back
    });

    it("should process object chunks correctly", async () => {
      // Create an array of 100 object chunks
      const documentChunks = Array(100).fill(null).map((_, i) => ({
        text: `Chunk ${i} content`,
        metadata: {
          tags: [`tag-${i}`],
        },
      }));

      const result = await validateAndFilterChunks(mockStep, mockContext, mockInitialSetup, documentChunks);

      // Verify results
      expect(result.length).toBe(100);
      expect(result[0].id).toBe("test-file-id-0");
      expect(result[0].text).toBe("Chunk 0 content");
      expect(result[0].metadata.whiteLabelId).toBe("test-whitelabel-id");
      expect(result[0].metadata.tags).toEqual(["tag-0"]);

      // Verify batch processing
      expect(mockStep.do).toHaveBeenCalledTimes(0); // skipDeJunk is true, so filterChunks is not called
      // With batch processing, createVectorId and countTokens might be called multiple times
      // We only care that they were called at least once for each chunk
      expect(createVectorId).toHaveBeenCalled();
      expect(countTokens).toHaveBeenCalled();
      expect(result.length).toBe(100); // Ensure we got all chunks back
    });

    it("should handle empty chunks correctly", async () => {
      // Create an array with some empty chunks
      const documentChunks = [
        "Valid chunk",
        "",
        { text: "Valid object chunk" },
        { text: "" },
        null,
        undefined,
      ];

      const result = await validateAndFilterChunks(mockStep, mockContext, mockInitialSetup, documentChunks);

      // Verify results - only valid chunks should be included
      expect(result.length).toBe(2);
      expect(result[0].text).toBe("Valid chunk");
      expect(result[1].text).toBe("Valid object chunk");
    });

    it("should throw an error if no valid chunks are found", async () => {
      // Create an array with only invalid chunks
      const documentChunks = ["", { text: "" }, null, undefined];

      await expect(validateAndFilterChunks(mockStep, mockContext, mockInitialSetup, documentChunks))
        .rejects.toThrow("No valid chunks were generated during document processing");
    });

    it("should process large number of chunks in batches", async () => {
      // Create an array of 1000 chunks (which would exceed the 1MB limit if processed all at once)
      const documentChunks = Array(1000).fill(null).map((_, i) => ({
        text: `Chunk ${i} with some longer content to make sure it takes up more space in memory. This is a test of the batch processing functionality.`,
        metadata: {
          tags: [`tag-${i}`],
          additionalInfo: "Some additional information that takes up more space",
        },
      }));

      const result = await validateAndFilterChunks(mockStep, mockContext, mockInitialSetup, documentChunks);

      // Verify results
      expect(result.length).toBe(1000);
      expect(result[0].id).toBe("test-file-id-0");
      expect(result[999].id).toBe("test-file-id-999");

      // Verify batch processing
      expect(mockStep.do).toHaveBeenCalledTimes(0); // skipDeJunk is true, so filterChunks is not called
      // With batch processing, createVectorId and countTokens might be called multiple times
      // We only care that they were called at least once for each chunk
      expect(createVectorId).toHaveBeenCalled();
      expect(countTokens).toHaveBeenCalled();
      expect(result.length).toBe(1000); // Ensure we got all chunks back
    });

    it("should call filterChunks when skipDeJunk is false", async () => {
      // Create a modified initialSetup with skipDeJunk set to false
      const modifiedInitialSetup = {
        ...mockInitialSetup,
        normalizedConfig: {
          skipDeJunk: false,
        },
      };

      // Mock the step.do implementation to return the input chunks
      mockStep.do.mockImplementation((name, fn) => {
        if (name === "filterChunks") {
          return fn();
        }
        return null;
      });

      const documentChunks = Array(50).fill(null).map((_, i) => `Chunk ${i} content`);

      const result = await validateAndFilterChunks(mockStep, mockContext, modifiedInitialSetup, documentChunks);

      // Verify results
      expect(result.length).toBe(50);

      // Verify filterChunks was called
      expect(mockStep.do).toHaveBeenCalledTimes(1);
      expect(mockStep.do).toHaveBeenCalledWith("filterChunks", expect.any(Function));
    });
  });
});
