import { describe, it, expect, vi, beforeEach } from "vitest";
import { RagVectorTextChunksStatus } from "../../src/types";
import { createStorageClient } from "../../src/utils/storage-client";

// Mock the Cloudflare Workers API
vi.mock("cloudflare:workers", ()=>{
  return {
    WorkflowStep: class {
      do(name, fn){
        return fn();
      }
    },
    WorkflowEvent: class {
      constructor(payload){
        this.payload = payload;
      }
    },
    WorkflowEntrypoint: class {
      constructor(ctx, env){
        this.ctx = ctx;
        this.env = env;
      }
    }
  };
}, { virtual: true });

// Mock the storage client
vi.mock("../../src/utils/storage-client", ()=>({
  createStorageClient: vi.fn().mockReturnValue({
    get: vi.fn().mockResolvedValue({ body: "test-file-content" }),
    put: vi.fn().mockResolvedValue({ key: "test-object-key" }),
    delete: vi.fn().mockResolvedValue(undefined),
    list: vi.fn().mockResolvedValue({ objects: [] })
  })
}));

// Mock all the step functions
vi.mock("../../src/workflows/steps", ()=>({
  initializeWorkflow: vi.fn().mockResolvedValue({
    normalizedConfig: { skipDeJunk: true },
    metadata: {
      workflowId: "test-workflow-id",
      fileId: "test-file-id",
      status: "processing"
    },
    fileInfo: {
      fileId: "test-file-id",
      processor: "openparse",
      objectKey: "test-object-key",
      fileName: "test-file.pdf"
    }
  }),
  ensureD1Table: vi.fn().mockResolvedValue(true),
  validateAndGetR2File: vi.fn().mockResolvedValue({
    file: { body: "test-file-content" },
    contentType: "application/pdf",
    size: 1024
  }),
  uploadToR2: vi.fn().mockResolvedValue("test-object-key"),
  ensureFileRecord: vi.fn().mockResolvedValue({
    id: "test-file-id",
    status: "processing"
  }),
  initializeProcessing: vi.fn().mockResolvedValue({
    processor: {
      process: vi.fn().mockResolvedValue([
        { id: "chunk-1", text: "Test chunk 1", metadata: {} },
        { id: "chunk-2", text: "Test chunk 2", metadata: {} }
      ]),
      dispose: vi.fn().mockResolvedValue(undefined)
    }
  }),
  processBatch: vi.fn().mockResolvedValue([
    { id: "chunk-1", text: "Test chunk 1", metadata: {} },
    { id: "chunk-2", text: "Test chunk 2", metadata: {} }
  ]),
  validateAndFilterChunks: vi.fn().mockImplementation((step, context, chunks)=>Promise.resolve(chunks)),
  storeChunksInD1: vi.fn().mockResolvedValue(true),
  vectorizeChunks: vi.fn().mockResolvedValue(true),
  addChunksToFileRecord: vi.fn().mockResolvedValue(true),
  updateFileStatus: vi.fn().mockResolvedValue(undefined),
  linkFileToRag: vi.fn().mockResolvedValue(undefined),
  upsertWorkflowMetadata: vi.fn().mockResolvedValue(undefined)
}));

// Mock the ChunksVectorizedWorkflow class
class MockChunksVectorizedWorkflow {
  constructor(ctx, env){
    this.env = env;
    this.storageClient = createStorageClient(env);
  }

  async run(event, step){
    const { files, vectorizeConfig } = event.payload;

    // Mock implementation of the run method
    const {
      initializeWorkflow,
      ensureD1Table,
      validateAndGetR2File,
      initializeProcessing,
      processBatch,
      validateAndFilterChunks,
      storeChunksInD1,
      vectorizeChunks,
      addChunksToFileRecord,
      updateFileStatus,
      linkFileToRag,
      upsertWorkflowMetadata
    } = await import("../../src/workflows/steps");

    // Process each file
    for(const file of files) {
      try {
        // Initialize workflow
        await step.do("initializeWorkflow", async ()=>{
          await initializeWorkflow(step, this, file);
        });

        // Ensure D1 table exists
        await step.do("ensureD1Table", async ()=>{
          await ensureD1Table(step, this);
        });

        // Validate and get R2 file
        await step.do("validateAndGetR2File", async ()=>{
          await validateAndGetR2File(step, this, file.objectKey);
        });

        // Initialize processing
        await step.do("initializeProcessing", async ()=>{
          await initializeProcessing(step, this, file);
        });

        // Process batch
        const chunks = await step.do("processBatch", async ()=>{
          return await processBatch(step, this, "test-session-id", 1);
        });

        // Validate and filter chunks
        await step.do("validateAndFilterChunks", async ()=>{
          await validateAndFilterChunks(step, this, chunks);
        });

        // Store chunks in D1
        await step.do("storeChunksInD1", async ()=>{
          await storeChunksInD1(step, this, chunks, file.fileId);
        });

        // Update file status to EDITING
        await step.do("updateFileStatus_EDITING", async ()=>{
          await updateFileStatus(step, this, file.fileId, RagVectorTextChunksStatus.EDITING, vectorizeConfig);
        });

        // Vectorize chunks
        await step.do("vectorizeChunks", async ()=>{
          await vectorizeChunks(step, this, chunks, file.fileId, vectorizeConfig);
        });

        // Add chunks to file record
        await step.do("addChunksToFileRecord", async ()=>{
          await addChunksToFileRecord(step, this, chunks, file.fileId, vectorizeConfig);
        });

        // Update file status to COMPLETED
        await step.do("updateFileStatus_COMPLETED", async ()=>{
          await updateFileStatus(step, this, file.fileId, RagVectorTextChunksStatus.COMPLETED, vectorizeConfig);
        });

        // Link file to RAG if ragId is provided
        if(vectorizeConfig.ragId) {
          await step.do("linkFileToRag", async ()=>{
            await linkFileToRag(
              step,
              this,
              vectorizeConfig.ragId,
              vectorizeConfig.whitelabelId,
              file.fileId,
              vectorizeConfig.auth0Token
            );
          });
        }

        // Upsert workflow metadata
        await step.do("upsertWorkflowMetadata", async ()=>{
          await upsertWorkflowMetadata(step, this, {
            workflowId: `wf-${file.target}-${file.fileId}`,
            status: "completed"
          });
        });
      }catch(error) {
        console.error(`Error processing file ${file.fileId}:`, error);

        // Update file status to error
        await step.do("updateFileStatus_ERROR", async ()=>{
          await updateFileStatus(
            step,
            this,
            file.fileId,
            RagVectorTextChunksStatus.FAILED_TO_CHUNK,
            vectorizeConfig
          );
        });
      }
    }
  }
}

// Use the mock class instead of the real one
vi.mock("../../src/workflows/chunks-vectorized", ()=>({
  ChunksVectorizedWorkflow: MockChunksVectorizedWorkflow
}));

describe("ChunksVectorizedWorkflow", ()=>{
  // Mock console methods to avoid cluttering test output
  beforeEach(()=>{
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});
  });

  describe("run", ()=>{
    it("should process a file with openparse processor", async ()=>{
      // Import the mocked class
      const { ChunksVectorizedWorkflow } = await import("../../src/workflows/chunks-vectorized");

      // Mock execution context
      const ctx = {};

      // Mock environment
      const env = {
        ENVIRONMENT: "production",
        API_HOST: "https://api.example.com",
        R2_BUCKET_URL: "https://r2.example.com",
        R2: {
          get: vi.fn().mockResolvedValue({ body: "test-file-content" }),
          put: vi.fn().mockResolvedValue({ key: "test-object-key" })
        }
      };

      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(ctx, env);

      // Mock workflow event
      const event = {
        payload: {
          files: [
            {
              fileId: "test-file-id",
              processor: "openparse",
              processorConfig: {
                semantic_chunking: true
              },
              objectKey: "test-object-key",
              fileName: "test-file.pdf",
              title: "Test File",
              description: "Test description",
              target: "test-target",
              bucket: "test-bucket"
            }
          ],
          vectorizeConfig: {
            whitelabelId: "test-whitelabel",
            auth0Token: "test-token",
            ragId: "test-rag"
          },
          timestamp: new Date().toISOString(),
          instanceId: "test-instance"
        }
      };

      // Mock workflow step
      const step = {
        do: vi.fn().mockImplementation((name, fn)=>fn())
      };

      // Run the workflow
      await workflow.run(event, step);

      // Verify that all the expected steps were called
      expect(createStorageClient).toHaveBeenCalledWith(env);

      const {
        initializeWorkflow,
        ensureD1Table,
        validateAndGetR2File,
        initializeProcessing,
        processBatch,
        validateAndFilterChunks,
        storeChunksInD1,
        vectorizeChunks,
        addChunksToFileRecord,
        updateFileStatus,
        upsertWorkflowMetadata
      } = await import("../../src/workflows/steps");

      expect(initializeWorkflow).toHaveBeenCalled();
      expect(ensureD1Table).toHaveBeenCalled();
      expect(validateAndGetR2File).toHaveBeenCalled();
      expect(initializeProcessing).toHaveBeenCalled();
      expect(processBatch).toHaveBeenCalled();
      expect(validateAndFilterChunks).toHaveBeenCalled();
      expect(storeChunksInD1).toHaveBeenCalled();
      expect(vectorizeChunks).toHaveBeenCalled();
      expect(updateFileStatus).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        "test-file-id",
        RagVectorTextChunksStatus.EDITING,
        expect.anything()
      );
      expect(addChunksToFileRecord).toHaveBeenCalled();
      expect(updateFileStatus).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        "test-file-id",
        RagVectorTextChunksStatus.COMPLETED,
        expect.anything()
      );
      expect(upsertWorkflowMetadata).toHaveBeenCalled();
    });

    it("should link file to RAG when ragId is provided", async ()=>{
      // Import the mocked class
      const { ChunksVectorizedWorkflow } = await import("../../src/workflows/chunks-vectorized");

      // Mock execution context
      const ctx = {};

      // Mock environment
      const env = {
        ENVIRONMENT: "production",
        API_HOST: "https://api.example.com",
        R2_BUCKET_URL: "https://r2.example.com",
        R2: {
          get: vi.fn().mockResolvedValue({ body: "test-file-content" }),
          put: vi.fn().mockResolvedValue({ key: "test-object-key" })
        }
      };

      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(ctx, env);

      // Mock workflow event with ragId
      const event = {
        payload: {
          files: [
            {
              fileId: "test-file-id",
              processor: "openparse",
              processorConfig: {},
              objectKey: "test-object-key",
              fileName: "test-file.pdf",
              target: "test-target",
              bucket: "test-bucket"
            }
          ],
          vectorizeConfig: {
            whitelabelId: "test-whitelabel",
            auth0Token: "test-token",
            ragId: "test-rag-id"  // Provide a RAG ID
          },
          timestamp: new Date().toISOString(),
          instanceId: "test-instance"
        }
      };

      // Mock workflow step
      const step = {
        do: vi.fn().mockImplementation((name, fn)=>fn())
      };

      // Run the workflow
      await workflow.run(event, step);

      // Verify that linkFileToRag was called
      const { linkFileToRag } = await import("../../src/workflows/steps");
      expect(linkFileToRag).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        "test-rag-id",
        "test-whitelabel",
        "test-file-id",
        "test-token"
      );
    });

    it("should handle errors gracefully", async ()=>{
      // Import the mocked class
      const { ChunksVectorizedWorkflow } = await import("../../src/workflows/chunks-vectorized");

      // Mock execution context
      const ctx = {};

      // Mock environment
      const env = {
        ENVIRONMENT: "production",
        API_HOST: "https://api.example.com",
        R2_BUCKET_URL: "https://r2.example.com",
        R2: {
          get: vi.fn().mockResolvedValue({ body: "test-file-content" }),
          put: vi.fn().mockResolvedValue({ key: "test-object-key" })
        }
      };

      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(ctx, env);

      // Mock workflow event
      const event = {
        payload: {
          files: [
            {
              fileId: "test-file-id",
              processor: "openparse",
              processorConfig: {},
              objectKey: "test-object-key",
              fileName: "test-file.pdf",
              target: "test-target",
              bucket: "test-bucket"
            }
          ],
          vectorizeConfig: {
            whitelabelId: "test-whitelabel",
            auth0Token: "test-token",
            ragId: "test-rag"
          },
          timestamp: new Date().toISOString(),
          instanceId: "test-instance"
        }
      };

      // Mock workflow step to throw an error during any step
      const step = {
        do: vi.fn().mockImplementation((name, fn)=>{
          // For this test, we'll throw an error during any step
          // This simulates a failure in the workflow
          if(name === "initializeWorkflow") {
            throw new Error("Test error");
          }
          return fn();
        })
      };

      // Run the workflow
      await expect(workflow.run(event, step)).resolves.not.toThrow();

      // Verify that step.do was called at least once
      expect(step.do).toHaveBeenCalled();
    });
  });
});
