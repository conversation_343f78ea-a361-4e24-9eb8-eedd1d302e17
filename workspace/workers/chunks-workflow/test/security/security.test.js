import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { createFetchResponse } from "../setup-comprehensive";
import { OpenParseProcessor } from "../../src/processors/openparse";
import { UnstructuredProcessor } from "../../src/processors/unstructured";
import { createStorageClient } from "../../src/utils/storage-client";

// Mock environment
const mockEnv = {
  OPENPARSE_API_KEY: "test-api-key",
  OPENPARSE_API_URL: "https://openparse.example.com",
  UNSTRUCTURED_API_KEY: "test-unstructured-key",
  UNSTRUCTURED_API_URL: "https://unstructured.example.com",
  VECTORIZE_API_TOKEN: "test-vectorize-token",
  VECTORIZE_ACCOUNT_ID: "test-account-id",
  VECTORIZE_INDEX_NAME: "test-index-name",
  ENVIRONMENT: "test",
  R2: {
    get: vi.fn(),
    put: vi.fn(),
    list: vi.fn()
  },
  CHUNKS_VECTORIZED: {
    create: vi.fn(),
    get: vi.fn(),
    update: vi.fn()
  },
  ALLOWED_ORIGINS: "*"
};

describe("Security Testing", ()=>{
  beforeEach(()=>{
    // Mock console methods to avoid cluttering test output
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});

    // Reset fetch mock
    global.fetch = vi.fn();

    // Setup basic mocks
    mockEnv.R2.get.mockResolvedValue({
      body: new Uint8Array(Buffer.from("Mock PDF content")),
      headers: new Headers({
        "content-type": "application/pdf",
        "content-length": "17"
      })
    });

    mockEnv.R2.put.mockResolvedValue({
      key: "test-object-key"
    });

    mockEnv.CHUNKS_VECTORIZED.get.mockResolvedValue({
      id: "test-workflow-id",
      status: "processing",
      params: {
        files: [
          {
            fileId: "test-file-id",
            processor: "openparse"
          }
        ],
        vectorizeConfig: {
          ragId: "test-rag-id"
        }
      }
    });

    mockEnv.CHUNKS_VECTORIZED.update.mockResolvedValue({
      success: true
    });

    // Mock fetch for API calls
    global.fetch.mockImplementation((url, options)=>{
      return Promise.resolve(createFetchResponse(
        { success: true },
        200,
        { "Content-Type": "application/json" }
      ));
    });
  });

  afterEach(()=>{
    vi.restoreAllMocks();
  });

  describe("Authentication and Authorization", ()=>{
    it("should include API keys in requests to external services", async ()=>{
      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Process document
      await processor.process(
        "https://example.com/test-file.pdf",
        { semantic: true }
      );

      // Note: In a real implementation, we would verify that the API key is included in the request
      // This is a placeholder test that documents the security requirement
      console.log("Security requirement: API keys should be included in requests to external services");

      // For now, we'll just verify that the processor was created with the API key
      expect(processor.apiKey).toBe("test-api-key");
    });

    it("should include authorization headers in requests to Vectorize API", async ()=>{
      // Mock fetch for Vectorize API
      global.fetch.mockImplementation((url, options)=>{
        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Call Vectorize API
      await fetch(`https://api.cloudflare.com/client/v4/accounts/${mockEnv.VECTORIZE_ACCOUNT_ID}/vectorize/v2/indexes/${mockEnv.VECTORIZE_INDEX_NAME}/upsert`, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-ndjson",
          "Authorization": `Bearer ${mockEnv.VECTORIZE_API_TOKEN}`
        },
        body: JSON.stringify({
          id: "test-vector",
          values: [0.1, 0.2, 0.3],
          metadata: {
            text: "Test text",
            fileId: "test-file-id",
            ragId: "test-rag-id"
          }
        })
      });

      // Verify authorization header was included in the request
      expect(global.fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            "Authorization": `Bearer ${mockEnv.VECTORIZE_API_TOKEN}`
          })
        })
      );
    });

    it("should validate API keys before making requests", async ()=>{
      // Create processor instance with empty API key
      const processor = new OpenParseProcessor(
        "",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Process document with empty API key
      // This should ideally throw an error or handle the case gracefully
      try {
        await processor.process(
          "https://example.com/test-file.pdf",
          { semantic: true }
        );

        // If we reach here, the processor should have handled the empty API key gracefully
        // Verify that the request was not made or was made with appropriate error handling
        const fetchCalls = global.fetch.mock.calls;
        const apiKeyHeaders = fetchCalls.map(call=>call[1]?.headers?.["x-api-key"]).filter(Boolean);

        // Either no calls were made, or no calls included an empty API key
        expect(apiKeyHeaders.includes("")).toBe(false);
      }catch(error) {
        // If an error was thrown, that's also acceptable behavior
        expect(error).toBeDefined();
      }
    });
  });

  describe("Input Validation and Sanitization", ()=>{
    it("should sanitize file paths to prevent path traversal attacks", async ()=>{
      // Create storage client
      const storageClient = createStorageClient(mockEnv);

      // Attempt to access a file with a path traversal attack
      const maliciousPath = "../../../secrets/credentials.json";

      try {
        await storageClient.get(maliciousPath);

        // If we reach here, the storage client should have sanitized the path
        // Verify that the actual path used does not contain the traversal
        expect(mockEnv.R2.get).toHaveBeenCalledWith(
          expect.not.stringContaining("../")
        );
      }catch(error) {
        // If an error was thrown, that's also acceptable behavior
        expect(error).toBeDefined();
      }
    });

    it("should validate and sanitize user-provided IDs", async ()=>{
      // Create a workflow ID with potentially malicious characters
      const maliciousId = "test-workflow-id<script>alert(\"XSS\")</script>";

      try {
        // Attempt to get a workflow with the malicious ID
        await mockEnv.CHUNKS_VECTORIZED.get(maliciousId);

        // If we reach here, the ID should have been sanitized
        // This is a simplified test - in a real application, you would check
        // that the sanitized ID was used in the actual database query
        expect(true).toBe(true);
      }catch(error) {
        // If an error was thrown, that's also acceptable behavior
        expect(error).toBeDefined();
      }
    });

    it("should validate URLs before making requests", async ()=>{
      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Attempt to process a document with a malicious URL
      const maliciousUrl = "javascript:alert(\"XSS\")";

      try {
        await processor.process(
          maliciousUrl,
          { semantic: true }
        );

        // If we reach here, the processor should have validated the URL
        // Verify that the request was not made with the malicious URL
        const fetchCalls = global.fetch.mock.calls;
        const requestBodies = fetchCalls.map(call=>{
          try {
            return JSON.parse(call[1]?.body || "{}");
          }catch(e) {
            return {};
          }
        });

        // No request should have included the malicious URL
        const containsMaliciousUrl = requestBodies.some(body=>body.file_url === maliciousUrl ||
          body.url === maliciousUrl
        );

        expect(containsMaliciousUrl).toBe(false);
      }catch(error) {
        // If an error was thrown, that's also acceptable behavior
        expect(error).toBeDefined();
      }
    });
  });

  describe("Data Protection", ()=>{
    it("should not log sensitive information", async ()=>{
      // Spy on console.log
      const consoleLogSpy = vi.spyOn(console, "log");

      // Create processor instance with API key
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Process document
      await processor.process(
        "https://example.com/test-file.pdf",
        { semantic: true }
      );

      // Verify that the API key was not logged
      const loggedApiKey = consoleLogSpy.mock.calls.some(call=>call.some(arg=>typeof arg === "string" && arg.includes("test-api-key")
        )
      );

      expect(loggedApiKey).toBe(false);
    });

    it("should not expose sensitive information in error messages", async ()=>{
      // Mock fetch to throw an error
      global.fetch.mockRejectedValueOnce(new Error("Connection failed"));

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Process document and catch the error
      try {
        await processor.process(
          "https://example.com/test-file.pdf",
          { semantic: true }
        );
      }catch(error) {
        // Verify that the error message does not contain the API key
        expect(error.message).not.toContain("test-api-key");
      }
    });

    it("should handle sensitive data in chunks appropriately", async ()=>{
      // Mock fetch to return chunks with potentially sensitive data
      global.fetch.mockImplementation((url, options)=>{
        if(url.includes("/fileUrl/init")) {
          return Promise.resolve(createFetchResponse(
            {
              total_chunks: 1,
              session_id: "test-session-id"
            },
            200,
            { "Content-Type": "application/json" }
          ));
        }

        if(url.includes("/fileUrl/batch")) {
          return Promise.resolve(createFetchResponse(
            [
              {
                id: "chunk-1",
                text: "This document contains a credit card number: 4111-1111-1111-1111",
                metadata: {}
              }
            ],
            200,
            { "Content-Type": "application/json" }
          ));
        }

        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Process document
      const chunks = await processor.process(
        "https://example.com/test-file.pdf",
        { semantic: true }
      );

      // Verify that the chunks were processed
      expect(chunks).toBeDefined();
      expect(chunks.length).toBeGreaterThan(0);

      // In a real application, you would verify that sensitive data is handled appropriately
      // For example, by redacting it or encrypting it
      // This test is more of a placeholder for that kind of verification
    });
  });

  describe("CORS and HTTP Security Headers", ()=>{
    it("should respect CORS configuration", async ()=>{
      // Mock fetch to include CORS headers
      global.fetch.mockImplementation((url, options)=>{
        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": mockEnv.ALLOWED_ORIGINS
          }
        ));
      });

      // Make a request
      const response = await fetch("https://example.com/api/endpoint");

      // Verify that the CORS headers are respected
      expect(response.headers.get("Access-Control-Allow-Origin")).toBe(mockEnv.ALLOWED_ORIGINS);
    });

    it("should include appropriate security headers in responses", async ()=>{
      // Mock fetch to include security headers
      global.fetch.mockImplementation((url, options)=>{
        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          {
            "Content-Type": "application/json",
            "Content-Security-Policy": "default-src 'self'",
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block"
          }
        ));
      });

      // Make a request
      const response = await fetch("https://example.com/api/endpoint");

      // Verify that the security headers are included
      expect(response.headers.get("Content-Security-Policy")).toBeDefined();
      expect(response.headers.get("X-Content-Type-Options")).toBeDefined();
      expect(response.headers.get("X-Frame-Options")).toBeDefined();
      expect(response.headers.get("X-XSS-Protection")).toBeDefined();
    });
  });

  describe("Rate Limiting and DoS Protection", ()=>{
    it("should handle rate limiting gracefully", async ()=>{
      // Mock fetch to simulate rate limiting
      global.fetch.mockImplementationOnce(()=>{
        return Promise.resolve(createFetchResponse(
          { error: "Rate limit exceeded" },
          429,
          { "Content-Type": "application/json", "Retry-After": "2" }
        ));
      }).mockImplementationOnce(()=>{
        return Promise.resolve(createFetchResponse(
          {
            total_chunks: 1,
            session_id: "test-session-id"
          },
          200,
          { "Content-Type": "application/json" }
        ));
      }).mockImplementation((url, options)=>{
        if(url.includes("/fileUrl/batch")) {
          return Promise.resolve(createFetchResponse(
            [
              { id: "chunk-1", text: "Test chunk", metadata: {} }
            ],
            200,
            { "Content-Type": "application/json" }
          ));
        }

        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instance with retry
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        {
          useStreaming: false,
          retryOptions: {
            maxAttempts: 3,
            delayMs: 100,
            backoffFactor: 1.5
          }
        }
      );

      // Process document with rate limiting
      const chunks = await processor.process(
        "https://example.com/test-file.pdf",
        { semantic: true }
      );

      // Verify that the document was processed despite rate limiting
      expect(chunks).toBeDefined();
      expect(chunks.length).toBeGreaterThan(0);

      // Note: In a real implementation, we would verify that fetch was called multiple times due to rate limiting
      // This is a placeholder test that documents the security requirement
      console.log("Security requirement: The system should handle rate limiting gracefully with retry logic");
    });

    it("should protect against excessive resource consumption", async ()=>{
      // Create a very large document (100MB)
      const largeDocumentSize = 100 * 1024 * 1024; // 100MB

      // Mock R2.get to return a large document
      mockEnv.R2.get.mockResolvedValue({
        body: new Uint8Array(largeDocumentSize),
        headers: new Headers({
          "content-type": "application/pdf",
          "content-length": largeDocumentSize.toString()
        })
      });

      // Create storage client
      const storageClient = createStorageClient(mockEnv);

      // Attempt to get the large document
      const result = await storageClient.get("large-document.pdf");

      // Verify that the document was retrieved
      expect(result).toBeDefined();

      // In a real application, you would verify that the system handles large documents appropriately
      // For example, by streaming them or by limiting the maximum document size
      // This test is more of a placeholder for that kind of verification
    });
  });

  describe("Secure Storage and Transmission", ()=>{
    it("should use HTTPS for all external requests", async ()=>{
      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Process document
      await processor.process(
        "https://example.com/test-file.pdf",
        { semantic: true }
      );

      // Verify that all requests use HTTPS
      const fetchCalls = global.fetch.mock.calls;
      const allUrlsUseHttps = fetchCalls.every(call=>call[0].startsWith("https://")
      );

      expect(allUrlsUseHttps).toBe(true);
    });

    it("should handle S3/R2 authentication securely", async ()=>{
      // Create storage client
      const storageClient = createStorageClient(mockEnv);

      // Get a file
      await storageClient.get("test-file.pdf");

      // Verify that R2.get was called
      expect(mockEnv.R2.get).toHaveBeenCalled();

      // In a real application, you would verify that the S3/R2 authentication is handled securely
      // For example, by using signed URLs or by using AWS4-HMAC-SHA256 authentication
      // This test is more of a placeholder for that kind of verification
    });
  });

  describe("Error Handling and Logging", ()=>{
    it("should handle errors securely without exposing sensitive information", async ()=>{
      // Mock fetch to throw an error with sensitive information
      global.fetch.mockRejectedValueOnce(new Error("Connection failed: API key test-api-key is invalid"));

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Spy on console.error
      const consoleErrorSpy = vi.spyOn(console, "error");

      // Process document and catch the error
      try {
        await processor.process(
          "https://example.com/test-file.pdf",
          { semantic: true }
        );
      }catch(error) {
        // Verify that the error was logged without sensitive information
        const loggedSensitiveInfo = consoleErrorSpy.mock.calls.some(call=>call.some(arg=>typeof arg === "string" && arg.includes("test-api-key")
          )
        );

        expect(loggedSensitiveInfo).toBe(false);
      }
    });

    it("should log security-relevant events appropriately", async ()=>{
      // Spy on console.warn
      const consoleWarnSpy = vi.spyOn(console, "warn");

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Mock fetch to return a 401 Unauthorized response
      global.fetch.mockImplementationOnce(()=>{
        return Promise.resolve(createFetchResponse(
          { error: "Unauthorized" },
          401,
          { "Content-Type": "application/json" }
        ));
      }).mockImplementation((url, options)=>{
        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Process document and catch the error
      try {
        await processor.process(
          "https://example.com/test-file.pdf",
          { semantic: true }
        );
      }catch(error) {
        // Verify that the security event was logged
        const loggedSecurityEvent = consoleWarnSpy.mock.calls.some(call=>call.some(arg=>typeof arg === "string" && arg.includes("401")
          )
        );

        // This is a simplified test - in a real application, you would have more specific logging
        // for security events
        expect(loggedSecurityEvent).toBe(true);
      }
    });
  });
});
