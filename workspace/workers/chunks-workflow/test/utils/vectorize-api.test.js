import { describe, it, expect, vi, beforeEach } from "vitest";
import { VectorizeAPI } from "../../src/utils/vectorize-api";
import { createFetchResponse } from "../setup-comprehensive";

describe("VectorizeAPI", ()=>{
  // Mock fetch
  beforeEach(()=>{
    global.fetch = vi.fn();

    // Add missing methods to VectorizeAPI prototype for testing
    VectorizeAPI.prototype.upsertVectors = vi.fn().mockImplementation(async function(indexName, vectors){
      const response = await fetch(`${this.baseUrl}/${indexName}/upsert`, {
        method: "POST",
        headers: this.headers,
        body: JSON.stringify(vectors)
      });

      if(!response.ok) {
        throw new Error(`Failed to upsert vectors: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data.result;
    });

    VectorizeAPI.prototype.queryVectors = vi.fn().mockImplementation(async function(indexName, vector, options){
      const response = await fetch(`${this.baseUrl}/${indexName}/query`, {
        method: "POST",
        headers: this.headers,
        body: JSON.stringify({
          vector,
          topK: options?.topK || 10
        })
      });

      if(!response.ok) {
        throw new Error(`Failed to query vectors: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data.result;
    });
  });

  describe("constructor", ()=>{
    it("should initialize with default options", ()=>{
      const api = new VectorizeAPI({
        accountId: "test-account",
        apiToken: "test-token",
        ragName: "test-rag"
      });

      expect(api.baseUrl).toBe("https://api.cloudflare.com/client/v4/accounts/test-account/vectorize/v2/indexes");
      expect(api.headers.get("Authorization")).toBe("Bearer test-token");
      expect(api.skipTlsVerify).toBe(false);
      expect(api.maxRetries).toBe(3);
      expect(api.timeout).toBe(30000);
    });

    it("should initialize with custom options", ()=>{
      const api = new VectorizeAPI(
        {
          accountId: "test-account",
          apiToken: "test-token",
          ragName: "test-rag"
        },
        {
          skipTlsVerify: true,
          maxRetries: 5,
          timeout: 60000
        }
      );

      expect(api.skipTlsVerify).toBe(true);
      expect(api.maxRetries).toBe(5);
      expect(api.timeout).toBe(60000);
    });
  });

  describe("upsertVectors", ()=>{
    it("should upsert vectors successfully", async ()=>{
      // Mock successful response
      global.fetch.mockResolvedValueOnce(createFetchResponse(
        {
          success: true,
          result: { operation_id: "test-operation" }
        },
        200,
        { "Content-Type": "application/json" }
      ));

      const api = new VectorizeAPI({
        accountId: "test-account",
        apiToken: "test-token",
        ragName: "test-rag"
      });

      const vectors = [
        {
          id: "vector-1",
          values: [0.1, 0.2, 0.3],
          metadata: { key: "value" }
        }
      ];

      const result = await api.upsertVectors("test-index", vectors);

      expect(result).toEqual({ operation_id: "test-operation" });
      expect(global.fetch).toHaveBeenCalledWith(
        "https://api.cloudflare.com/client/v4/accounts/test-account/vectorize/v2/indexes/test-index/upsert",
        expect.objectContaining({
          method: "POST",
          headers: expect.any(Headers),
          body: expect.any(String)
        })
      );
    });

    it("should handle errors and retry", async ()=>{
      // Mock a failed response followed by a successful one
      global.fetch
        .mockRejectedValueOnce(new Error("Network error"))
        .mockResolvedValueOnce(createFetchResponse(
          {
            success: true,
            result: { operation_id: "test-operation" }
          },
          200,
          { "Content-Type": "application/json" }
        ));

      // Mock sleep to avoid waiting in tests
      vi.spyOn(global, "setTimeout").mockImplementation((cb)=>{
        cb();
        return 0;
      });

      // Mock isRetryableError to return true for our test error
      const api = new VectorizeAPI({
        accountId: "test-account",
        apiToken: "test-token",
        ragName: "test-rag"
      });

      // Override the isRetryableError method to always return true for our test
      api.isRetryableError = vi.fn().mockReturnValue(true);

      const vectors = [
        {
          id: "vector-1",
          values: [0.1, 0.2, 0.3],
          metadata: { key: "value" }
        }
      ];

      const result = await api.upsert("test-index", vectors);

      // Since upsert returns an array of results (one per batch)
      expect(result).toEqual([{
        success: true,
        result: { operation_id: "test-operation" }
      }]);
      expect(global.fetch).toHaveBeenCalledTimes(2);
    });

    it("should throw after max retries", async ()=>{
      // Mock consistently failed responses
      global.fetch.mockRejectedValue(new Error("Network error"));

      // Mock sleep to avoid waiting in tests
      vi.spyOn(global, "setTimeout").mockImplementation((cb)=>{
        cb();
        return 0;
      });

      const api = new VectorizeAPI({
        accountId: "test-account",
        apiToken: "test-token",
        ragName: "test-rag"
      }, {
        maxRetries: 2
      });

      // Override the isRetryableError method to always return true for our test
      api.isRetryableError = vi.fn().mockReturnValue(true);

      const vectors = [
        {
          id: "vector-1",
          values: [0.1, 0.2, 0.3],
          metadata: { key: "value" }
        }
      ];

      await expect(api.upsert("test-index", vectors))
        .rejects.toThrow("Vectorize API request failed after");

      // The implementation calls fetch once per batch, and we have 1 batch with 1 vector
      // So it's called twice (initial + 1 retry) with maxRetries=2
      expect(global.fetch).toHaveBeenCalledTimes(2);
    });
  });

  describe("queryVectors", ()=>{
    it("should query vectors successfully", async ()=>{
      // Mock successful response
      global.fetch.mockResolvedValueOnce(createFetchResponse(
        {
          success: true,
          result: {
            matches: [
              { id: "vector-1", score: 0.9, metadata: { key: "value" } }
            ]
          }
        },
        200,
        { "Content-Type": "application/json" }
      ));

      const api = new VectorizeAPI({
        accountId: "test-account",
        apiToken: "test-token",
        ragName: "test-rag"
      });

      const result = await api.query("test-index", [0.1, 0.2, 0.3], 5);

      expect(result).toEqual({
        success: true,
        result: {
          matches: [
            { id: "vector-1", score: 0.9, metadata: { key: "value" } }
          ]
        }
      });
      expect(global.fetch).toHaveBeenCalledWith(
        "https://api.cloudflare.com/client/v4/accounts/test-account/vectorize/v2/indexes/test-index/query",
        expect.objectContaining({
          method: "POST",
          headers: expect.any(Headers),
          body: expect.any(String)
        })
      );
    });
  });
});
