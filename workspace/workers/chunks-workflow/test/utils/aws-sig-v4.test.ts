import { describe, it, expect, vi, beforeEach } from "vitest";

// Import our mock implementation
import { generateAuthorizationHeader as mockGenerateAuthorizationHeader } from "../mocks/aws-sig-v4-mock";

// Mock the actual implementation
vi.mock("../../src/utils/aws-sig-v4", ()=>{
  return {
    generateAuthorizationHeader: mockGenerateAuthorizationHeader
  };
});

// Import the mocked module
import { generateAuthorizationHeader } from "../../src/utils/aws-sig-v4";

describe("aws-sig-v4", ()=>{
  // Mock crypto.subtle for consistent test results
  beforeEach(()=>{
    // Create a complete mock of crypto.subtle
    const mockSubtle = {
      importKey: vi.fn().mockResolvedValue({} as CryptoKey),
      sign: vi.fn().mockResolvedValue(new Uint8Array([1, 2, 3, 4]).buffer),
      digest: vi.fn().mockResolvedValue(new Uint8Array([1, 2, 3, 4]).buffer)
    } as unknown as SubtleCrypto;

    // Replace the entire subtle object
    Object.defineProperty(crypto, "subtle", {
      value: mockSubtle,
      configurable: true
    });
  });

  describe("generateAuthorizationHeader", ()=>{
    it("should generate authorization headers for GET request", async ()=>{
      const method = "GET";
      const url = new URL("http://local-minio:9000/bucket/test-key");
      const accessKey = "test-access-key";
      const secretKey = "test-secret-key";

      const headers = await generateAuthorizationHeader(
        method,
        url,
        accessKey,
        secretKey
      );

      expect(headers).toHaveProperty("Authorization");
      expect(headers).toHaveProperty("x-amz-date");
      expect(headers.Authorization).toContain("AWS4-HMAC-SHA256");
      expect(headers.Authorization).toContain("Credential=test-access-key");
    });

    it("should generate authorization headers for PUT request with payload", async ()=>{
      const method = "PUT";
      const url = new URL("http://local-minio:9000/bucket/test-key");
      const accessKey = "test-access-key";
      const secretKey = "test-secret-key";
      const payload = new TextEncoder().encode("test-data").buffer;
      const contentType = "application/octet-stream";

      const headers = await generateAuthorizationHeader(
        method,
        url,
        accessKey,
        secretKey,
        payload,
        contentType
      );

      expect(headers).toHaveProperty("Authorization");
      expect(headers).toHaveProperty("x-amz-date");
      expect(headers).toHaveProperty("x-amz-content-sha256");
      expect(headers.Authorization).toContain("AWS4-HMAC-SHA256");
      expect(headers.Authorization).toContain("Credential=test-access-key");
    });

    it("should handle URLs with query parameters", async ()=>{
      const method = "GET";
      const url = new URL("http://local-minio:9000/bucket/test-key?list-type=2&prefix=folder/");
      const accessKey = "test-access-key";
      const secretKey = "test-secret-key";

      const headers = await generateAuthorizationHeader(
        method,
        url,
        accessKey,
        secretKey
      );

      expect(headers).toHaveProperty("Authorization");
      expect(headers.Authorization).toContain("AWS4-HMAC-SHA256");
    });
  });
});
