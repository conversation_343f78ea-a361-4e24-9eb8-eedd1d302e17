import { describe, it, expect, vi, beforeEach } from "vitest";
import { createStorageClient } from "../../src/utils/storage-client";
import { generateAuthorizationHeader } from "../../src/utils/aws-sig-v4";

// Mock the aws-sig-v4 module
vi.mock("../../src/utils/aws-sig-v4", ()=>({
  generateAuthorizationHeader: vi.fn().mockResolvedValue({
    "Authorization": "AWS4-HMAC-SHA256 Credential=test",
    "x-amz-date": "20230101T000000Z"
  })
}));

describe("storage-client", ()=>{
  // Mock console.log to avoid cluttering test output
  beforeEach(()=>{
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});
  });

  describe("createStorageClient", ()=>{
    it("should create R2StorageClient for production environment", async ()=>{
      // Mock R2 bucket
      const mockR2 = {
        get: vi.fn().mockResolvedValue({ body: "test-data" }),
        put: vi.fn().mockResolvedValue({ key: "test-key" }),
        delete: vi.fn().mockResolvedValue(undefined),
        list: vi.fn().mockResolvedValue({ objects: [] })
      };

      const env = {
        ENVIRONMENT: "production",
        R2_BUCKET_URL: "https://r2.example.com",
        R2: mockR2
      };

      const client = createStorageClient(env);

      // Test get method
      const getResult = await client.get("test-key");
      expect(mockR2.get).toHaveBeenCalledWith("test-key");
      expect(getResult).toEqual({ body: "test-data" });

      // Test put method
      const putResult = await client.put("test-key", "test-data");
      expect(mockR2.put).toHaveBeenCalledWith("test-key", "test-data", undefined);
      expect(putResult).toEqual({ key: "test-key" });

      // Test delete method
      await client.delete("test-key");
      expect(mockR2.delete).toHaveBeenCalledWith("test-key");

      // Test list method
      const listResult = await client.list();
      expect(mockR2.list).toHaveBeenCalledWith(undefined);
      expect(listResult).toEqual({ objects: [] });
    });

    it("should create MinioStorageClient for local environment", async ()=>{
      // Mock fetch for MinIO client
      global.fetch = vi.fn().mockImplementation((url, options)=>{
        const urlString = url.toString();
        console.log(`Mocked fetch called with URL: ${urlString}, method: ${options?.method || "GET"}`);

        // HEAD request to check if bucket exists - handle any bucket name
        if((urlString.includes("test-bucket") || urlString.includes("rag-origin-files-local")) && options?.method === "HEAD") {
          return Promise.resolve({
            ok: true,
            status: 200,
            url: urlString,
            text: ()=>Promise.resolve("OK")
          });
        }

        // PUT request to create bucket - handle any bucket name
        if((urlString.includes("test-bucket") || urlString.includes("rag-origin-files-local")) && options?.method === "PUT") {
          return Promise.resolve({
            ok: true,
            status: 200,
            url: urlString,
            text: ()=>Promise.resolve("OK")
          });
        }

        // HEAD request to check if object exists
        if(urlString.includes("test-key") && options?.method === "HEAD") {
          return Promise.resolve({
            ok: false,
            status: 404,
            url: urlString,
            text: ()=>Promise.resolve("Not Found")
          });
        }

        // GET request to retrieve object
        if(urlString.includes("test-key") && (!options?.method || options.method === "GET") && !urlString.includes("list-type=2")) {
          const headers = new Headers({
            "content-type": "application/octet-stream"
          });

          // Ensure headers has forEach method
          if(!headers.forEach) {
            headers.forEach = function(callback){
              this.entries().forEach(([key, value])=>callback(value, key));
            };
          }

          return Promise.resolve({
            ok: true,
            status: 200,
            url: urlString,
            body: new ReadableStream({
              start(controller){
                controller.enqueue(new TextEncoder().encode("test-data"));
                controller.close();
              }
            }),
            text: ()=>Promise.resolve("test-data"),
            json: ()=>Promise.resolve({ data: "test-data" }),
            blob: ()=>Promise.resolve(new Blob(["test-data"], { type: "application/octet-stream" })),
            arrayBuffer: ()=>Promise.resolve(new TextEncoder().encode("test-data").buffer),
            headers: headers,
            clone: function(){
              return this;
            }
          });
        }

        // PUT request to upload object
        if(urlString.includes("test-key") && options?.method === "PUT") {
          const headers = new Headers({
            "content-type": "application/json",
            "etag": "\"test-etag\""
          });

          // Ensure headers has forEach method
          if(!headers.forEach) {
            headers.forEach = function(callback){
              this.entries().forEach(([key, value])=>callback(value, key));
            };
          }

          return Promise.resolve({
            ok: true,
            status: 200,
            url: urlString,
            text: ()=>Promise.resolve("Success"),
            json: ()=>Promise.resolve({ key: "test-key" }),
            blob: ()=>Promise.resolve(new Blob(["Success"], { type: "text/plain" })),
            arrayBuffer: ()=>Promise.resolve(new TextEncoder().encode("Success").buffer),
            headers: headers,
            clone: function(){
              return this;
            }
          });
        }

        // DELETE request to delete object
        if(urlString.includes("test-key") && options?.method === "DELETE") {
          const headers = new Headers({
            "content-type": "text/plain"
          });

          // Ensure headers has forEach method
          if(!headers.forEach) {
            headers.forEach = function(callback){
              this.entries().forEach(([key, value])=>callback(value, key));
            };
          }

          return Promise.resolve({
            ok: true,
            status: 204,
            url: urlString,
            text: ()=>Promise.resolve(""),
            json: ()=>Promise.resolve({}),
            blob: ()=>Promise.resolve(new Blob([""], { type: "text/plain" })),
            arrayBuffer: ()=>Promise.resolve(new ArrayBuffer(0)),
            headers: headers,
            clone: function(){
              return this;
            }
          });
        }

        // GET request to list objects
        if(urlString.includes("list-type=2") && (!options?.method || options.method === "GET")) {
          const headers = new Headers({
            "content-type": "application/xml"
          });

          // Ensure headers has forEach method
          if(!headers.forEach) {
            headers.forEach = function(callback){
              this.entries().forEach(([key, value])=>callback(value, key));
            };
          }

          const xmlResponse = "<ListBucketResult><Contents><Key>test-key</Key></Contents></ListBucketResult>";

          return Promise.resolve({
            ok: true,
            status: 200,
            url: urlString,
            text: ()=>Promise.resolve(xmlResponse),
            json: ()=>Promise.resolve({ objects: [{ key: "test-key" }] }),
            blob: ()=>Promise.resolve(new Blob([xmlResponse], { type: "application/xml" })),
            arrayBuffer: ()=>Promise.resolve(new TextEncoder().encode(xmlResponse).buffer),
            headers: headers,
            clone: function(){
              return this;
            }
          });
        }

        // Fallback for any other requests
        console.log(`Using fallback response for: ${urlString}, method: ${options?.method || "GET"}`);
        const headers = new Headers({
          "content-type": "application/octet-stream"
        });

        // Ensure headers has forEach method
        if(!headers.forEach) {
          headers.forEach = function(callback){
            this.entries().forEach(([key, value])=>callback(value, key));
          };
        }

        return Promise.resolve({
          ok: true,
          status: 200,
          url: urlString,
          text: ()=>Promise.resolve("Fallback response"),
          json: ()=>Promise.resolve({ success: true }),
          blob: ()=>Promise.resolve(new Blob(["fallback-data"], { type: "application/octet-stream" })),
          arrayBuffer: ()=>Promise.resolve(new TextEncoder().encode("fallback-data").buffer),
          body: new ReadableStream({
            start(controller){
              controller.enqueue(new TextEncoder().encode("fallback-data"));
              controller.close();
            }
          }),
          headers: headers,
          clone: function(){
            return this;
          }
        });
      });

      const env = {
        ENVIRONMENT: "local",
        R2_BUCKET_URL: "http://local-minio:9000",
        R2_ACCESS_KEY_ID: "test-access-key",
        R2_SECRET_ACCESS_KEY: "test-secret-key",
        BUCKET_NAME: "test-bucket"
      };

      const client = createStorageClient(env);

      // Test get method
      const getResult = await client.get("test-key");
      expect(fetch).toHaveBeenCalled();
      expect(generateAuthorizationHeader).toHaveBeenCalled();
      expect(getResult).toBeDefined();

      // Reset mocks
      vi.clearAllMocks();

      // Test put method
      const putResult = await client.put("test-key", "test-data");
      expect(fetch).toHaveBeenCalled();
      expect(generateAuthorizationHeader).toHaveBeenCalled();
      expect(putResult).toBeDefined();

      // Reset mocks
      vi.clearAllMocks();

      // Test delete method
      await client.delete("test-key");
      expect(fetch).toHaveBeenCalled();
      expect(generateAuthorizationHeader).toHaveBeenCalled();

      // Reset mocks
      vi.clearAllMocks();

      // Test list method
      const listResult = await client.list();
      expect(fetch).toHaveBeenCalled();
      expect(generateAuthorizationHeader).toHaveBeenCalled();
      expect(listResult).toBeDefined();
    });
  });
});
