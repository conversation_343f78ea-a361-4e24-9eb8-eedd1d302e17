import { describe, it, expect, vi, beforeEach } from "vitest";
import { createStorageClient } from "../../src/utils/storage-client";
import { generateAuthorizationHeader } from "../../src/utils/aws-sig-v4";
import { Env } from "../../src/types";

// Mock the aws-sig-v4 module
vi.mock("../../src/utils/aws-sig-v4", ()=>({
  generateAuthorizationHeader: vi.fn().mockResolvedValue({
    "Authorization": "AWS4-HMAC-SHA256 Credential=test",
    "x-amz-date": "20230101T000000Z"
  })
}));

describe("storage-client", ()=>{
  // Mock console.log to avoid cluttering test output
  beforeEach(()=>{
    vi.spyOn(console, "log").mockImplementation(()=>{});
  });

  describe("createStorageClient", ()=>{
    it("should create R2StorageClient for production environment", async ()=>{
      // Mock R2 bucket
      const mockR2 = {
        get: vi.fn().mockResolvedValue({ body: "test-data" }),
        put: vi.fn().mockResolvedValue({ key: "test-key" }),
        delete: vi.fn().mockResolvedValue(undefined),
        list: vi.fn().mockResolvedValue({ objects: [] })
      };

      // Create a mock that satisfies the Env interface
      const env: Env = {
        ENVIRONMENT: "production",
        R2_BUCKET_URL: "https://r2.example.com",
        R2: mockR2 as unknown as R2Bucket,
        // Mock the required properties from Env interface
        DB: {} as D1Database,
        LOGS_DB: {} as D1Database,
        AI: {
          run: vi.fn().mockResolvedValue([])
        },
        UNSTRUCTURED_WORKER_URL: "http://localhost:8000",
        OPENPARSE_API_KEY: "test-key",
        FFMPEG_WORKER_URL: "http://localhost:8001",
        OPENPARSE_API_URL: "http://localhost:8002",
        CLOUDFLARE_API_URL: "http://localhost:8003",
        CLOUDFLARE_ACCOUNT_ID: "test-account-id",
        CLOUDFLARE_API_TOKEN: "test-api-token",
        CHUNKS_VECTORIZED: {} as Workflow<any>,
        VIDEO_TO_AUDIO: {} as Workflow<any>,
        API_HOST: "localhost",
        R2_ACCESS_KEY_ID: "test-access-key",
        R2_SECRET_ACCESS_KEY: "test-secret-key"
      };

      const client = createStorageClient(env);

      // Test get method
      const getResult = await client.get("test-key");
      expect(mockR2.get).toHaveBeenCalledWith("test-key");
      expect(getResult).toEqual({ body: "test-data" });

      // Test put method
      const putResult = await client.put("test-key", "test-data");
      expect(mockR2.put).toHaveBeenCalledWith("test-key", "test-data", undefined);
      expect(putResult).toEqual({ key: "test-key" });

      // Test delete method
      await client.delete("test-key");
      expect(mockR2.delete).toHaveBeenCalledWith("test-key");

      // Test list method
      const listResult = await client.list();
      expect(mockR2.list).toHaveBeenCalledWith(undefined);
      expect(listResult).toEqual({ objects: [] });
    });

    it("should create MinioStorageClient for local environment", ()=>{
      // Create a mock that satisfies the Env interface
      const env = {
        ENVIRONMENT: "local",
        R2_BUCKET_URL: "http://local-minio:9000/",
        R2_ACCESS_KEY_ID: "test-access-key",
        R2_SECRET_ACCESS_KEY: "test-secret-key",
        // Mock the required properties from Env interface
        DB: {} as D1Database,
        LOGS_DB: {} as D1Database,
        R2: {} as R2Bucket,
        AI: {
          run: vi.fn().mockResolvedValue([])
        },
        UNSTRUCTURED_WORKER_URL: "http://localhost:8000",
        OPENPARSE_API_KEY: "test-key",
        FFMPEG_WORKER_URL: "http://localhost:8001",
        OPENPARSE_API_URL: "http://localhost:8002",
        CLOUDFLARE_API_URL: "http://localhost:8003",
        CLOUDFLARE_ACCOUNT_ID: "test-account-id",
        CLOUDFLARE_API_TOKEN: "test-api-token",
        CHUNKS_VECTORIZED: {} as Workflow<any>,
        VIDEO_TO_AUDIO: {} as Workflow<any>,
        API_HOST: "localhost"
      } as const;

      const client = createStorageClient(env);

      // Just verify the client was created successfully
      expect(client).toBeDefined();
      expect(client.get).toBeInstanceOf(Function);
      expect(client.put).toBeInstanceOf(Function);
      expect(client.delete).toBeInstanceOf(Function);
      expect(client.list).toBeInstanceOf(Function);
    });

  });
});
