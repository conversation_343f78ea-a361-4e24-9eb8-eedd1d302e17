import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { OpenParseProcessor } from "../src/processors/openparse";
import { UnstructuredProcessor } from "../src/processors/unstructured";
import { VectorizeAPI } from "../src/utils/vectorize-api";
import { createFetchResponse } from "./setup-comprehensive";

// Mock environment
const mockEnv = {};

describe("Basic Processor Functionality Tests", ()=>{
  beforeEach(()=>{
    // Mock console methods to avoid cluttering test output
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});

    // Reset fetch mock
    global.fetch = vi.fn();
  });

  afterEach(()=>{
    vi.restoreAllMocks();
  });

  describe("OpenParseProcessor", ()=>{
    it("should process documents with minimal content", async ()=>{
      // Mock fetch to return minimal content
      global.fetch.mockImplementation((url, options)=>{
        if(url.includes("/fileUrl/init")) {
          return Promise.resolve(createFetchResponse(
            {
              total_chunks: 1,
              session_id: "test-session-id"
            },
            200,
            { "Content-Type": "application/json" }
          ));
        }

        if(url.includes("/fileUrl/batch")) {
          const batchNumber = JSON.parse(options.body).batch_number;

          if(batchNumber === 0) {
            return Promise.resolve(createFetchResponse(
              [
                { id: "chunk-1", text: "A", metadata: {} }
              ],
              200,
              { "Content-Type": "application/json" }
            ));
          } else {
            return Promise.resolve(createFetchResponse(
              [],
              200,
              { "Content-Type": "application/json" }
            ));
          }
        }

        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Process document with minimal content
      const chunks = await processor.process(
        "https://example.com/minimal-file.pdf",
        { semantic: true }
      );

      // Verify result
      expect(chunks.length).toBeGreaterThan(0);
      expect(chunks[0].text).toBe("A");
    });

    it("should handle documents with special characters", async ()=>{
      // Text with special characters
      const specialCharsText = "Special characters: áéíóúñÁÉÍÓÚÑüÜ¿¡€£¥$@#%^&*()_+{}[]|\\:;\"'<>,.?/~`";

      // Mock fetch to return text with special characters
      global.fetch.mockImplementation((url, options)=>{
        if(url.includes("/fileUrl/init")) {
          return Promise.resolve(createFetchResponse(
            {
              total_chunks: 1,
              session_id: "test-session-id"
            },
            200,
            { "Content-Type": "application/json" }
          ));
        }

        if(url.includes("/fileUrl/batch")) {
          const batchNumber = JSON.parse(options.body).batch_number;

          if(batchNumber === 0) {
            return Promise.resolve(createFetchResponse(
              [
                { id: "chunk-1", text: specialCharsText, metadata: {} }
              ],
              200,
              { "Content-Type": "application/json" }
            ));
          } else {
            return Promise.resolve(createFetchResponse(
              [],
              200,
              { "Content-Type": "application/json" }
            ));
          }
        }

        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Process document with special characters
      const chunks = await processor.process(
        "https://example.com/special-chars-file.pdf",
        { semantic: true }
      );

      // Verify result
      expect(chunks.length).toBeGreaterThan(0);
      expect(chunks[0].text).toBe(specialCharsText);
    });
  });

  // TODO: Implement tests for UnstructuredProcessor
  describe("UnstructuredProcessor", ()=>{
    it.skip("should process documents correctly", async ()=>{
      // This test is skipped because it needs proper mocking of the Unstructured API
      // Implementation will be added in a future update
    });
  });

  // TODO: Implement tests for VectorizeAPI
  describe("VectorizeAPI", ()=>{
    it.skip("should upsert vectors successfully", async ()=>{
      // This test is skipped because it needs proper mocking of the Vectorize API
      // Implementation will be added in a future update
    });

    it.skip("should query vectors successfully", async ()=>{
      // This test is skipped because it needs proper mocking of the Vectorize API
      // Implementation will be added in a future update
    });
  });
});
