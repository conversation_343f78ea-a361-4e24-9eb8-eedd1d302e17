/**
 * Mock implementation of AWS Signature V4 for testing
 */

/**
 * Generate AWS Signature V4 authorization headers
 *
 * @param {Object} options - Options for generating the headers
 * @param {string} options.method - HTTP method (GET, PUT, etc.)
 * @param {string} options.url - URL for the request
 * @param {Object} options.headers - Headers for the request
 * @param {string} options.accessKey - AWS access key
 * @param {string} options.secretKey - AWS secret key
 * @param {string} options.region - AWS region
 * @param {string} options.service - AWS service
 * @param {string|Buffer} options.payload - Request payload
 * @returns {Object} - Headers with AWS signature
 */
export function generateAuthorizationHeader(options){
  const {
    method = "GET",
    url,
    headers = {},
    accessKey = "test-access-key",
    secretKey = "test-secret-key",
    region = "us-east-1",
    service = "s3",
    payload = ""
  } = options;

  // Generate a fixed date for testing
  const amzDate = "20230101T000000Z";
  const dateStamp = "20230101";

  // Generate a fixed signature for testing
  const signature = "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890";

  // Generate a fixed content hash for testing
  const contentSha256 = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";

  // Create authorization header
  const credential = `${accessKey}/${dateStamp}/${region}/${service}/aws4_request`;
  const authorization = `AWS4-HMAC-SHA256 Credential=${credential}, SignedHeaders=host;x-amz-content-sha256;x-amz-date, Signature=${signature}`;

  // Return headers
  return {
    "Authorization": authorization,
    "x-amz-date": amzDate,
    "x-amz-content-sha256": contentSha256,
    ...headers
  };
}

// Mock the aws-sig-v4 module
export default {
  generateAuthorizationHeader
};
