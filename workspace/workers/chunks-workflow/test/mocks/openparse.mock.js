// Mock implementation of OpenParseProcessor for testing
export class OpenParseProcessor {
  constructor(apiKey, apiUrl, env, options = {}){
    this.apiKey = apiKey;
    this.apiUrl = apiUrl;
    this.env = env;
    this.options = options;
    this.existingSessionId = null;
  }

  async initializeSession(fileUrl, config){
    // Mock implementation that matches the real one's behavior
    if(global.fetch.mock && global.fetch.mock.calls.length > 0) {
      const response = await global.fetch(`${this.apiUrl}/fileUrl/init`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          url: fileUrl,
          config: {
            semantic_chunking: config.semantic || false,
            embeddings_provider: config.embeddingsProvider || "none",
            minTokens: config.minTokens || 256,
            maxTokens: config.maxTokens || 1024,
            overlap: config.chunkOverlap || 200,
            useTokens: config.useTokens !== false,
            relevance_threshold: config.relevanceThreshold || 0.3,
          }
        })
      });

      if(!response.ok) {
        const errorText = await response.text();
        throw new Error(`❌ OpenParse init failed: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      this.existingSessionId = data.session_id;

      return {
        sessionId: data.session_id,
        totalChunks: data.total_chunks
      };
    }

    // Default mock response
    this.existingSessionId = "mock-session-id";
    return {
      sessionId: "mock-session-id",
      totalChunks: 10
    };
  }

  async processBatch(batchNumber, options = {}){
    const batchSize = options.batchSize || 50;

    if(!this.existingSessionId) {
      throw new Error("No active session");
    }

    if(global.fetch.mock && global.fetch.mock.calls.length > 0) {
      const response = await global.fetch(`${this.apiUrl}/fileUrl/batch`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          session_id: this.existingSessionId,
          batch_number: batchNumber,
          batch_size: batchSize
        })
      });

      if(!response.ok) {
        const errorText = await response.text();
        throw new Error(`❌ Failed to fetch batch: ${response.status} - ${errorText}`);
      }

      const chunks = await response.json();

      if(!Array.isArray(chunks)) {
        throw new Error("❌ Invalid batch response format");
      }

      // Return null if no chunks (end of processing)
      if(chunks.length === 0) {
        return null;
      }

      return chunks;
    }

    // Default mock response
    if(batchNumber > 0) {
      return null; // End of processing
    }

    return [
      { id: "mock-chunk-1", text: "Mock chunk 1", metadata: {} },
      { id: "mock-chunk-2", text: "Mock chunk 2", metadata: {} }
    ];
  }

  async process(fileUrl, config){
    await this.initializeSession(fileUrl, config);
    const chunks = [];

    let batchNumber = 0;
    let batch;

    do {
      batch = await this.processBatch(batchNumber, { batchSize: 50 });
      if(batch) {
        chunks.push(...batch);
      }
      batchNumber++;
    } while(batch !== null);

    return chunks;
  }

  async dispose(){
    if(this.existingSessionId) {
      try {
        // Try the primary cleanup endpoint
        try {
          await global.fetch(`${this.apiUrl}/cleanup/${this.existingSessionId}`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${this.apiKey}`
            }
          });
        }catch(error) {
          // If that fails, try the alternative endpoint
          await global.fetch(`${this.apiUrl}/session/${this.existingSessionId}/cleanup`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${this.apiKey}`
            }
          });
        }
      }catch(error) {
        // Ignore cleanup errors
        console.error("Error cleaning up session:", error);
      }
    }
  }
}
