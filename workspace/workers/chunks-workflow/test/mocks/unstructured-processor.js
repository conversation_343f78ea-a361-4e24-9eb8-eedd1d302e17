/**
 * Mock implementation of UnstructuredProcessor for testing
 */

export class UnstructuredProcessor {
  constructor(apiKey, apiUrl){
    this.apiKey = apiKey;
    this.apiUrl = apiUrl;
    this.sessionId = null;
  }

  async initializeSession(config, fileUrl){
    // Mock initialization
    this.sessionId = "test-session-id";
    return { sessionId: this.sessionId };
  }

  async processBatch(sessionId, offset, limit){
    // Mock processing
    return [
      {
        id: "chunk-1",
        text: "This is test chunk 1",
        metadata: {
          page: 1,
          type: "Title"
        }
      },
      {
        id: "chunk-2",
        text: "This is test chunk 2",
        metadata: {
          page: 1,
          type: "Paragraph"
        }
      }
    ];
  }

  async process(fileUrl, config){
    // Initialize session
    await this.initializeSession(config, fileUrl);

    // Process batches
    return this.processBatch(this.sessionId, 0, 100);
  }

  async dispose(){
    // Mock cleanup
    this.sessionId = null;
    return true;
  }
}

// Export the mock
export default {
  UnstructuredProcessor
};
