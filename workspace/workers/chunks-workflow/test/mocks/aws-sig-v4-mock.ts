/**
 * Mock implementation of AWS Signature V4 for testing
 */

/**
 * Generate mock AWS Signature V4 authorization headers
 * This function returns a fixed set of headers for testing purposes
 */
export async function generateAuthorizationHeader(
  method: string,
  url: URL,
  accessKey: string,
  secretKey: string,
  payload: ArrayBuffer | null = null,
  contentType: string = "",
  region: string = "us-east-1",
  service: string = "s3"
): Promise<Record<string, string>>{
  // Use fixed values for testing to ensure consistency
  const amzDate = "20230101T000000Z";
  const dateStamp = "20230101";
  const payloadHash = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"; // Empty payload hash

  // Create credential string
  const credentialScope = `${dateStamp}/${region}/${service}/aws4_request`;

  // Create authorization header
  const authorizationHeader = `AWS4-HMAC-SHA256 Credential=${accessKey}/${credentialScope}, SignedHeaders=host;x-amz-content-sha256;x-amz-date, Signature=abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890`;

  // Return headers
  const headers: Record<string, string> = {
    "Authorization": authorizationHeader,
    "x-amz-date": amzDate,
    "x-amz-content-sha256": payloadHash
  };

  if(contentType) {
    headers["Content-Type"] = contentType;
  }

  return headers;
}
