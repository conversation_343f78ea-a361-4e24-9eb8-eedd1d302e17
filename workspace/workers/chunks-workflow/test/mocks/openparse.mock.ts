// Mock implementation of OpenParseProcessor for testing
import { vi } from "vitest";

export class OpenParseProcessor {
  private apiKey: string;
  private apiUrl: string;
  // These properties are kept for API compatibility but not used in the mock
  private readonly env: any;
  private readonly options: any;
  private existingSessionId: string | null;

  constructor(apiKey: string, apiUrl: string, env: any, options: any = {}){
    this.apiKey = apiKey;
    this.apiUrl = apiUrl;
    this.env = env;
    this.options = options;
    this.existingSessionId = null;
  }

  async initializeSession(fileUrl: string, config: any){
    try {
      // This is a mock implementation that doesn't actually make API calls
      // Instead, it returns predefined responses based on the test case

      // Check if this is an error test case (status 500)
      if(global.fetch && typeof global.fetch === "function" && vi.isMockFunction(global.fetch)) {
        // Use the mocked fetch to get the expected response
        const response = await global.fetch(`${this.apiUrl}/fileUrl/init`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${this.apiKey}`
          },
          body: JSON.stringify({
            file_url: fileUrl,
            semantic_chunking: config.semantic || false,
            embeddings_provider: config.embeddingsProvider || "none",
            minTokens: config.minTokens || 256,
            maxTokens: config.maxTokens || 1024,
            overlap: config.chunkOverlap || 200,
            useTokens: config.useTokens !== false,
            relevanceThreshold: config.relevanceThreshold || 0.3,
          })
        });

        if(!response.ok) {
          const errorText = await response.text();
          throw new Error(`❌ OpenParse init failed: ${response.status} - ${errorText}`);
        }

        const data = await response.json() as { session_id: string, total_chunks: number };
        this.existingSessionId = data.session_id;

        return {
          sessionId: data.session_id,
          totalChunks: data.total_chunks
        };
      }

      // Default mock response
      this.existingSessionId = "test-session-id";
      return {
        sessionId: "test-session-id",
        totalChunks: 10
      };
    }catch(error) {
      console.error("❌ Failed to initialize OpenParse session:", error);
      throw error;
    }
  }

  async processBatch(batchNumber: number, options: any = {}){
    try {
      if(!this.existingSessionId) {
        throw new Error("Session not initialized. Call initializeSession first.");
      }

      // This is a mock implementation that doesn't actually make API calls
      // Instead, it returns predefined responses based on the test case

      // Check if this is an error test case
      if(global.fetch && typeof global.fetch === "function" && vi.isMockFunction(global.fetch)) {
        // Use the mocked fetch to get the expected response
        const response = await global.fetch(`${this.apiUrl}/fileUrl/batch`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${this.apiKey}`
          },
          body: JSON.stringify({
            session_id: this.existingSessionId,
            batch_number: batchNumber,
            batch_size: options.batchSize || 50
          })
        });

        if(!response.ok) {
          const errorText = await response.text();

          if(response.status === 404) {
            throw new Error("Session expired");
          }

          throw new Error(`❌ Failed to fetch batch: ${response.status} - ${errorText}`);
        }

        const chunks = await response.json();

        if(!Array.isArray(chunks)) {
          throw new Error("❌ Invalid batch response format");
        }

        // Return null if no chunks (end of processing)
        if(chunks.length === 0) {
          return null;
        }

        return chunks;
      }

      // Default mock response
      if(batchNumber === 0) {
        return [
          {
            id: "chunk-1",
            text: "Test chunk 1",
            metadata: { page: 1 }
          },
          {
            id: "chunk-2",
            text: "Test chunk 2",
            metadata: { page: 2 }
          }
        ];
      } else {
        // End of processing
        return null;
      }
    }catch(error) {
      console.error(`❌ Error in processBatch ${batchNumber}:`, error);
      throw error;
    }
  }

  async process(fileUrl: string, config: any){
    await this.initializeSession(fileUrl, config);
    const chunks: Array<{ text: string, metadata?: any }> = [];

    let batchNumber = 0;
    let batch: Array<{ text: string, metadata?: any }> | null;

    do {
      batch = await this.processBatch(batchNumber, { batchSize: 50 });
      if(batch) {
        chunks.push(...batch);
      }
      batchNumber++;
    } while(batch !== null);

    return chunks;
  }

  async dispose(){
    if(this.existingSessionId) {
      try {
        // This is a mock implementation that doesn't actually make API calls
        // Instead, it simulates the cleanup process

        if(global.fetch && typeof global.fetch === "function" && vi.isMockFunction(global.fetch)) {
          // Try the primary cleanup endpoint
          try {
            const response = await global.fetch(`${this.apiUrl}/cleanup/${this.existingSessionId}`, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${this.apiKey}`
              }
            });

            if(!response.ok) {
              // If the endpoint doesn't exist (404), try the alternative endpoint
              if(response.status === 404) {
                // Try the session endpoint directly
                await global.fetch(`${this.apiUrl}/session/${this.existingSessionId}/cleanup`, {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${this.apiKey}`
                  }
                });
              }
            }
          }catch(cleanupError) {
            console.warn(`⚠️ Error during OpenParse cleanup: ${cleanupError.message}`);
          }
        }

        // Clear the session ID
        this.existingSessionId = null;
      }catch(error) {
        console.warn(`⚠️ Error cleaning up OpenParse session: ${error}`);
      }
    }

    return Promise.resolve();
  }
}
