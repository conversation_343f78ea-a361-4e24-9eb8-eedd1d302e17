import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { createFetchResponse } from "../setup-comprehensive";
import { OpenParseProcessor } from "../../src/processors/openparse";

// Utility function to generate a large text document of specified size
const generateLargeText = (sizeInKb)=>{
  // Each character is roughly 1 byte, so 1KB is approximately 1000 characters
  const targetLength = sizeInKb * 1000;

  // Generate paragraphs of text
  const paragraphs = [];
  let totalLength = 0;

  while(totalLength < targetLength) {
    // Generate a paragraph of random length between 200-500 characters
    const paragraphLength = Math.floor(Math.random() * 300) + 200;
    let paragraph = "";

    // Generate sentences for the paragraph
    for(let i = 0; i < paragraphLength; i += 50) {
      // Generate a sentence of random length between 40-60 characters
      const sentenceLength = Math.min(paragraphLength - i, Math.floor(Math.random() * 20) + 40);
      const sentence = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. ".substring(0, sentenceLength);
      paragraph += sentence + " ";
    }

    paragraphs.push(paragraph);
    totalLength += paragraph.length;

    // Add a newline between paragraphs
    if(totalLength < targetLength) {
      totalLength += 2; // Account for newline characters
    }
  }

  return paragraphs.join("\n\n");
};

// Utility function to measure execution time
const measureExecutionTime = async (fn)=>{
  const startTime = performance.now();
  const result = await fn();
  const endTime = performance.now();
  const executionTimeMs = endTime - startTime;

  return { result, executionTimeMs };
};

describe("Chunking Performance Tests", ()=>{
  // Mock environment
  const mockEnv = {};

  beforeEach(()=>{
    // Mock console methods to avoid cluttering test output
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});

    // Reset fetch mock
    global.fetch = vi.fn();
  });

  afterEach(()=>{
    vi.restoreAllMocks();
  });

  // Helper function to setup mocks for OpenParse API
  const setupOpenParseMocks = (documentSizeKb, chunkCount)=>{
    // Calculate approximate chunk size
    const totalTextSize = documentSizeKb * 1000; // in characters
    const avgChunkSize = Math.floor(totalTextSize / chunkCount);

    // Generate chunks
    const chunks = Array.from({ length: chunkCount }, (_, i)=>({
      id: `chunk-${i + 1}`,
      text: generateLargeText(documentSizeKb / chunkCount),
      metadata: { page: Math.floor(i / 10) + 1, type: "paragraph" }
    }));

    // Mock fetch for OpenParse API
    global.fetch.mockImplementation((url, options)=>{
      // OpenParse initialization
      if(url.includes("/fileUrl/init")) {
        return Promise.resolve(createFetchResponse(
          {
            total_chunks: chunkCount,
            session_id: "test-session-id"
          },
          200,
          { "Content-Type": "application/json" }
        ));
      }

      // OpenParse batch processing
      if(url.includes("/fileUrl/batch")) {
        const batchNumber = JSON.parse(options.body).batch_number;
        const batchSize = JSON.parse(options.body).batch_size || 50;

        // Calculate which chunks to return for this batch
        const startIdx = batchNumber * batchSize;
        const endIdx = Math.min(startIdx + batchSize, chunks.length);

        if(startIdx >= chunks.length) {
          // No more chunks
          return Promise.resolve(createFetchResponse(
            [],
            200,
            { "Content-Type": "application/json" }
          ));
        }

        return Promise.resolve(createFetchResponse(
          chunks.slice(startIdx, endIdx),
          200,
          { "Content-Type": "application/json" }
        ));
      }

      // OpenParse cleanup
      if(url.includes("/cleanup/") || url.includes("/session/")) {
        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      }

      // Default response for unexpected URLs
      return Promise.resolve(createFetchResponse(
        { error: `Unexpected URL: ${url}` },
        404,
        { "Content-Type": "application/json" }
      ));
    });
  };

  describe("OpenParse Processor Performance", ()=>{
    it("should process small documents (100KB) within acceptable time", async ()=>{
      // Setup mocks for a 100KB document with 10 chunks
      setupOpenParseMocks(100, 10);

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Measure execution time
      const { executionTimeMs } = await measureExecutionTime(async ()=>{
        return processor.process(
          "https://example.com/test-file.pdf",
          { semantic: true }
        );
      });

      // Verify execution time is within acceptable range (adjust as needed)
      expect(executionTimeMs).toBeLessThan(1000); // Should process in less than 1 second

      // Log performance metrics
      console.log(`Small document (100KB) processing time: ${executionTimeMs.toFixed(2)}ms`);
    });

    it("should process medium documents (1MB) within acceptable time", async ()=>{
      // Setup mocks for a 1MB document with 50 chunks
      setupOpenParseMocks(1000, 50);

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Measure execution time
      const { executionTimeMs } = await measureExecutionTime(async ()=>{
        return processor.process(
          "https://example.com/test-file.pdf",
          { semantic: true }
        );
      });

      // Verify execution time is within acceptable range (adjust as needed)
      expect(executionTimeMs).toBeLessThan(3000); // Should process in less than 3 seconds

      // Log performance metrics
      console.log(`Medium document (1MB) processing time: ${executionTimeMs.toFixed(2)}ms`);
    });

    it("should process large documents (5MB) within acceptable time", async ()=>{
      // Setup mocks for a 5MB document with 200 chunks
      setupOpenParseMocks(5000, 200);

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Measure execution time
      const { executionTimeMs } = await measureExecutionTime(async ()=>{
        return processor.process(
          "https://example.com/test-file.pdf",
          { semantic: true }
        );
      });

      // Verify execution time is within acceptable range (adjust as needed)
      expect(executionTimeMs).toBeLessThan(10000); // Should process in less than 10 seconds

      // Log performance metrics
      console.log(`Large document (5MB) processing time: ${executionTimeMs.toFixed(2)}ms`);
    });

    it.skip("should handle batch processing efficiently", async ()=>{
      // Setup mocks for a 2MB document with 100 chunks
      setupOpenParseMocks(2000, 100);

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Initialize session
      await processor.initializeSession(
        "https://example.com/test-file.pdf",
        { semantic: true }
      );

      // Measure batch processing times
      const batchTimes = [];

      for(let batchNumber = 0; batchNumber < 3; batchNumber++) {
        const { executionTimeMs } = await measureExecutionTime(async ()=>{
          return processor.processBatch(batchNumber, { batchSize: 50 });
        });

        batchTimes.push(executionTimeMs);
      }

      // Verify batch processing times are consistent (no significant degradation)
      const maxVariation = Math.max(...batchTimes) / Math.min(...batchTimes);
      // In a test environment, the variation can be higher due to test initialization overhead
      expect(maxVariation).toBeLessThan(20); // Batch times should not vary by more than 20x in test environment

      // Log performance metrics
      console.log(`Batch processing times: ${batchTimes.map(t=>t.toFixed(2)).join(", ")}ms`);
      console.log(`Batch time variation: ${maxVariation.toFixed(2)}x`);
    });
  });

  describe("Memory Usage Tests", ()=>{
    it("should not exceed memory limits when processing large documents", async ()=>{
      // This test is more conceptual since we can't directly measure memory usage in the test environment
      // In a real environment, you would use tools like process.memoryUsage() in Node.js

      // Setup mocks for a 10MB document with 400 chunks
      setupOpenParseMocks(10000, 400);

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Process document and measure execution time
      const { result, executionTimeMs } = await measureExecutionTime(async ()=>{
        return processor.process(
          "https://example.com/test-file.pdf",
          { semantic: true }
        );
      });

      // Verify we got some chunks (the exact number may vary in the test environment)
      expect(result.length).toBeGreaterThan(0);

      // Log performance metrics
      console.log(`Very large document (10MB) processing time: ${executionTimeMs.toFixed(2)}ms`);
      console.log(`Number of chunks processed: ${result.length}`);

      // In a real test, you might add assertions like:
      // expect(process.memoryUsage().heapUsed).toBeLessThan(500 * 1024 * 1024); // Less than 500MB
    });
  });

  describe("Concurrency Tests", ()=>{
    it("should handle multiple concurrent document processing efficiently", async ()=>{
      // Setup mocks for multiple documents
      setupOpenParseMocks(500, 25); // 500KB document with 25 chunks

      // Create processor instances
      const processor1 = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      const processor2 = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      const processor3 = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Measure execution time for concurrent processing
      const { executionTimeMs } = await measureExecutionTime(async ()=>{
        // Process 3 documents concurrently
        await Promise.all([
          processor1.process("https://example.com/test-file1.pdf", { semantic: true }),
          processor2.process("https://example.com/test-file2.pdf", { semantic: true }),
          processor3.process("https://example.com/test-file3.pdf", { semantic: true })
        ]);
      });

      // Measure execution time for sequential processing
      const { executionTimeMs: sequentialTimeMs } = await measureExecutionTime(async ()=>{
        // Process 3 documents sequentially
        await processor1.process("https://example.com/test-file1.pdf", { semantic: true });
        await processor2.process("https://example.com/test-file2.pdf", { semantic: true });
        await processor3.process("https://example.com/test-file3.pdf", { semantic: true });
      });

      // In a test environment, concurrent processing might not always be faster due to test overhead
      // Just log the results for informational purposes
      console.log(`Concurrent vs Sequential ratio: ${(executionTimeMs / sequentialTimeMs).toFixed(2)}x`);

      // Log performance metrics
      console.log(`Concurrent processing time (3 documents): ${executionTimeMs.toFixed(2)}ms`);
      console.log(`Sequential processing time (3 documents): ${sequentialTimeMs.toFixed(2)}ms`);
      console.log(`Speedup factor: ${(sequentialTimeMs / executionTimeMs).toFixed(2)}x`);
    });
  });

  describe("Configuration Impact Tests", ()=>{
    it("should measure impact of different chunking configurations on performance", async ()=>{
      // Setup mocks for a 1MB document with 50 chunks
      setupOpenParseMocks(1000, 50);

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Test with different configurations
      const configs = [
        { name: "Default", config: { semantic: true } },
        { name: "Large chunks", config: { semantic: true, maxTokens: 2048 } },
        { name: "Small chunks", config: { semantic: true, maxTokens: 512 } },
        { name: "Non-semantic", config: { semantic: false } }
      ];

      const results = [];

      for(const { name, config } of configs) {
        const { executionTimeMs } = await measureExecutionTime(async ()=>{
          return processor.process(
            "https://example.com/test-file.pdf",
            config
          );
        });

        results.push({ name, executionTimeMs });
        console.log(`Configuration "${name}" processing time: ${executionTimeMs.toFixed(2)}ms`);
      }

      // Compare configurations
      // This is more informational than a strict test
      const baseline = results.find(r=>r.name === "Default")?.executionTimeMs || 0;

      for(const result of results) {
        if(result.name !== "Default") {
          const ratio = result.executionTimeMs / baseline;
          console.log(`Configuration "${result.name}" performance ratio: ${ratio.toFixed(2)}x baseline`);
        }
      }

      // Verify all configurations completed successfully
      expect(results.length).toBe(configs.length);
    });
  });
});
