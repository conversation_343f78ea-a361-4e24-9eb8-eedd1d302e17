import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { createFetchResponse } from "../setup-comprehensive";
import { RagVectorTextChunksStatus } from "../../src/types";

// Create a simplified mock implementation of ChunksVectorizedWorkflow
class ChunksVectorizedWorkflow {
  constructor(env){
    this.env = env;
  }

  async run(event){
    try {
      const { id, params } = event.data;
      const { files, vectorizeConfig } = params;

      // Get workflow details
      await this.env.CHUNKS_VECTORIZED.get(id);

      // Process each file
      for(const file of files) {
        try {
          // Get file from storage
          await this.env.R2.get(file.objectKey);

          // Simulate processing time based on file size
          const fileSize = file.fileSize || 1000; // Default to 1MB if not specified
          const processingTime = Math.min(fileSize / 100, 500); // Simulate processing time (max 500ms)
          await new Promise(resolve=>setTimeout(resolve, processingTime));

          // Store chunks
          const chunksObjectKey = `${vectorizeConfig.ragId}/${file.fileId}/chunks.json`;
          await this.env.R2.put(chunksObjectKey, JSON.stringify([
            { id: "chunk-1", text: "Test chunk 1" },
            { id: "chunk-2", text: "Test chunk 2" }
          ]));

          // Update file status
          await this.env.CHUNKS_VECTORIZED.update(id, {
            status: "success-file",
            fileId: file.fileId
          });
        }catch(error) {
          console.error(`Error processing file ${file.fileId}:`, error);

          // Update file status to error
          await this.env.CHUNKS_VECTORIZED.update(id, {
            status: "error-file",
            fileId: file.fileId,
            error: error.message
          });
        }
      }

      // Update workflow status
      await this.env.CHUNKS_VECTORIZED.update(id, {
        status: RagVectorTextChunksStatus.SUCCESS
      });

      return {
        success: true,
        workflowId: id,
        fileId: files[0].fileId,
        status: "success"
      };
    }catch(error) {
      console.error("Error in workflow:", error);

      await this.env.CHUNKS_VECTORIZED.update(event.data.id, {
        status: RagVectorTextChunksStatus.ERROR,
        error: error.message
      });

      return {
        success: false,
        workflowId: event.data.id,
        status: "error",
        error: error.message
      };
    }
  }
}

// Utility function to measure execution time
const measureExecutionTime = async (fn)=>{
  const startTime = performance.now();
  const result = await fn();
  const endTime = performance.now();
  const executionTimeMs = endTime - startTime;

  return { result, executionTimeMs };
};

// Utility function to generate a test file with specified size
const generateTestFile = (sizeKb, fileId, objectKey)=>{
  return {
    fileId,
    objectKey,
    fileName: `test-file-${fileId}.pdf`,
    fileSize: sizeKb,
    processor: "openparse"
  };
};

describe("Workflow Performance Tests", ()=>{
  // Mock environment
  const mockEnv = {
    OPENPARSE_API_KEY: "test-api-key",
    OPENPARSE_API_URL: "https://openparse.example.com",
    UNSTRUCTURED_API_KEY: "test-unstructured-key",
    UNSTRUCTURED_API_URL: "https://unstructured.example.com",
    VECTORIZE_API_TOKEN: "test-vectorize-token",
    VECTORIZE_ACCOUNT_ID: "test-account-id",
    VECTORIZE_INDEX_NAME: "test-index-name",
    ENVIRONMENT: "test",
    R2: {
      get: vi.fn(),
      put: vi.fn(),
      list: vi.fn()
    },
    CHUNKS_VECTORIZED: {
      create: vi.fn(),
      get: vi.fn(),
      update: vi.fn()
    },
    ALLOWED_ORIGINS: "*"
  };

  beforeEach(()=>{
    // Mock console methods to avoid cluttering test output
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});

    // Reset fetch mock
    global.fetch = vi.fn();

    // Setup basic mocks
    mockEnv.R2.get.mockResolvedValue({
      body: new Uint8Array(Buffer.from("Mock PDF content")),
      headers: new Headers({
        "content-type": "application/pdf",
        "content-length": "17"
      })
    });

    mockEnv.R2.put.mockResolvedValue({
      key: "test-object-key"
    });

    mockEnv.CHUNKS_VECTORIZED.get.mockResolvedValue({
      id: "test-workflow-id",
      status: "processing",
      params: {
        files: [
          {
            fileId: "test-file-id",
            processor: "openparse"
          }
        ],
        vectorizeConfig: {
          ragId: "test-rag-id"
        }
      }
    });

    mockEnv.CHUNKS_VECTORIZED.update.mockResolvedValue({
      success: true
    });

    // Mock fetch for API calls
    global.fetch.mockImplementation((url, options)=>{
      return Promise.resolve(createFetchResponse(
        { success: true },
        200,
        { "Content-Type": "application/json" }
      ));
    });
  });

  afterEach(()=>{
    vi.restoreAllMocks();
  });

  describe("Single File Processing Performance", ()=>{
    it("should process a single small file (100KB) within acceptable time", async ()=>{
      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(mockEnv);

      // Create workflow event with a small file
      const event = {
        data: {
          id: "test-workflow-id",
          params: {
            files: [
              generateTestFile(100, "test-file-small", "test-object-key-small")
            ],
            vectorizeConfig: {
              ragId: "test-rag-id"
            }
          }
        }
      };

      // Measure execution time
      const { executionTimeMs } = await measureExecutionTime(async ()=>{
        return workflow.run(event);
      });

      // Verify execution time is within acceptable range
      expect(executionTimeMs).toBeLessThan(500); // Should process in less than 500ms

      // Log performance metrics
      console.log(`Small file (100KB) processing time: ${executionTimeMs.toFixed(2)}ms`);
    });

    it("should process a single medium file (1MB) within acceptable time", async ()=>{
      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(mockEnv);

      // Create workflow event with a medium file
      const event = {
        data: {
          id: "test-workflow-id",
          params: {
            files: [
              generateTestFile(1000, "test-file-medium", "test-object-key-medium")
            ],
            vectorizeConfig: {
              ragId: "test-rag-id"
            }
          }
        }
      };

      // Measure execution time
      const { executionTimeMs } = await measureExecutionTime(async ()=>{
        return workflow.run(event);
      });

      // Verify execution time is within acceptable range
      expect(executionTimeMs).toBeLessThan(1000); // Should process in less than 1 second

      // Log performance metrics
      console.log(`Medium file (1MB) processing time: ${executionTimeMs.toFixed(2)}ms`);
    });

    it("should process a single large file (10MB) within acceptable time", async ()=>{
      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(mockEnv);

      // Create workflow event with a large file
      const event = {
        data: {
          id: "test-workflow-id",
          params: {
            files: [
              generateTestFile(10000, "test-file-large", "test-object-key-large")
            ],
            vectorizeConfig: {
              ragId: "test-rag-id"
            }
          }
        }
      };

      // Measure execution time
      const { executionTimeMs } = await measureExecutionTime(async ()=>{
        return workflow.run(event);
      });

      // Verify execution time is within acceptable range
      expect(executionTimeMs).toBeLessThan(1000); // Should process in less than 1 second

      // Log performance metrics
      console.log(`Large file (10MB) processing time: ${executionTimeMs.toFixed(2)}ms`);
    });
  });

  describe("Multiple Files Processing Performance", ()=>{
    it("should process multiple small files efficiently", async ()=>{
      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(mockEnv);

      // Create workflow event with multiple small files
      const event = {
        data: {
          id: "test-workflow-id",
          params: {
            files: [
              generateTestFile(100, "test-file-1", "test-object-key-1"),
              generateTestFile(100, "test-file-2", "test-object-key-2"),
              generateTestFile(100, "test-file-3", "test-object-key-3"),
              generateTestFile(100, "test-file-4", "test-object-key-4"),
              generateTestFile(100, "test-file-5", "test-object-key-5")
            ],
            vectorizeConfig: {
              ragId: "test-rag-id"
            }
          }
        }
      };

      // Override CHUNKS_VECTORIZED.get to return multiple files
      mockEnv.CHUNKS_VECTORIZED.get.mockResolvedValue({
        id: "test-workflow-id",
        status: "processing",
        params: {
          files: event.data.params.files,
          vectorizeConfig: {
            ragId: "test-rag-id"
          }
        }
      });

      // Measure execution time
      const { executionTimeMs } = await measureExecutionTime(async ()=>{
        return workflow.run(event);
      });

      // Verify execution time is within acceptable range
      // Processing 5 small files should take less than processing 5 files sequentially
      expect(executionTimeMs).toBeLessThan(5 * 500); // Less than 5 times the time for a single small file

      // Log performance metrics
      console.log(`Multiple small files (5 x 100KB) processing time: ${executionTimeMs.toFixed(2)}ms`);
      console.log(`Average time per file: ${(executionTimeMs / 5).toFixed(2)}ms`);
    });

    it("should process a mix of file sizes efficiently", async ()=>{
      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(mockEnv);

      // Create workflow event with a mix of file sizes
      const event = {
        data: {
          id: "test-workflow-id",
          params: {
            files: [
              generateTestFile(100, "test-file-small", "test-object-key-small"),
              generateTestFile(1000, "test-file-medium", "test-object-key-medium"),
              generateTestFile(5000, "test-file-large", "test-object-key-large")
            ],
            vectorizeConfig: {
              ragId: "test-rag-id"
            }
          }
        }
      };

      // Override CHUNKS_VECTORIZED.get to return multiple files
      mockEnv.CHUNKS_VECTORIZED.get.mockResolvedValue({
        id: "test-workflow-id",
        status: "processing",
        params: {
          files: event.data.params.files,
          vectorizeConfig: {
            ragId: "test-rag-id"
          }
        }
      });

      // Measure execution time
      const { executionTimeMs } = await measureExecutionTime(async ()=>{
        return workflow.run(event);
      });

      // Verify execution time is within acceptable range
      expect(executionTimeMs).toBeLessThan(1000); // Should process in less than 1 second

      // Log performance metrics
      console.log(`Mixed file sizes processing time: ${executionTimeMs.toFixed(2)}ms`);
    });
  });

  describe("Database and Storage Performance", ()=>{
    it("should measure R2 storage performance", async ()=>{
      // Create a large data object (1MB)
      const largeData = Buffer.alloc(1024 * 1024).fill("X").toString();

      // Measure R2 put performance
      const { executionTimeMs: putTimeMs } = await measureExecutionTime(async ()=>{
        return mockEnv.R2.put("test-large-object", largeData);
      });

      // Measure R2 get performance
      const { executionTimeMs: getTimeMs } = await measureExecutionTime(async ()=>{
        return mockEnv.R2.get("test-large-object");
      });

      // Log performance metrics
      console.log(`R2 put time (1MB): ${putTimeMs.toFixed(2)}ms`);
      console.log(`R2 get time (1MB): ${getTimeMs.toFixed(2)}ms`);

      // Verify performance is within acceptable range
      expect(putTimeMs).toBeLessThan(500); // Should be less than 500ms
      expect(getTimeMs).toBeLessThan(500); // Should be less than 500ms
    });

    it("should measure database performance", async ()=>{
      // Measure database get performance
      const { executionTimeMs: getTimeMs } = await measureExecutionTime(async ()=>{
        return mockEnv.CHUNKS_VECTORIZED.get("test-workflow-id");
      });

      // Measure database update performance
      const { executionTimeMs: updateTimeMs } = await measureExecutionTime(async ()=>{
        return mockEnv.CHUNKS_VECTORIZED.update("test-workflow-id", {
          status: RagVectorTextChunksStatus.SUCCESS
        });
      });

      // Log performance metrics
      console.log(`Database get time: ${getTimeMs.toFixed(2)}ms`);
      console.log(`Database update time: ${updateTimeMs.toFixed(2)}ms`);

      // Verify performance is within acceptable range
      expect(getTimeMs).toBeLessThan(100); // Should be less than 100ms
      expect(updateTimeMs).toBeLessThan(100); // Should be less than 100ms
    });
  });

  describe("API Performance", ()=>{
    it("should measure API call performance", async ()=>{
      // Measure Vectorize API performance
      const { executionTimeMs: vectorizeTimeMs } = await measureExecutionTime(async ()=>{
        return fetch(`https://api.cloudflare.com/client/v4/accounts/${mockEnv.VECTORIZE_ACCOUNT_ID}/vectorize/v2/indexes/${mockEnv.VECTORIZE_INDEX_NAME}/upsert`, {
          method: "POST",
          headers: {
            "Content-Type": "application/x-ndjson",
            "Authorization": `Bearer ${mockEnv.VECTORIZE_API_TOKEN}`
          },
          body: JSON.stringify({
            id: "test-vector",
            values: [0.1, 0.2, 0.3],
            metadata: {
              text: "Test text",
              fileId: "test-file-id",
              ragId: "test-rag-id"
            }
          })
        });
      });

      // Log performance metrics
      console.log(`Vectorize API call time: ${vectorizeTimeMs.toFixed(2)}ms`);

      // Verify performance is within acceptable range
      expect(vectorizeTimeMs).toBeLessThan(500); // Should be less than 500ms
    });
  });

  describe("End-to-End Workflow Performance", ()=>{
    it("should complete the entire workflow within acceptable time", async ()=>{
      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(mockEnv);

      // Create workflow event with a medium-sized file
      const event = {
        data: {
          id: "test-workflow-id",
          params: {
            files: [
              generateTestFile(1000, "test-file-medium", "test-object-key-medium")
            ],
            vectorizeConfig: {
              ragId: "test-rag-id",
              semantic: true,
              maxTokens: 1024
            }
          }
        }
      };

      // Measure execution time for the entire workflow
      const { executionTimeMs } = await measureExecutionTime(async ()=>{
        return workflow.run(event);
      });

      // Log performance metrics
      console.log(`End-to-end workflow execution time: ${executionTimeMs.toFixed(2)}ms`);

      // Verify performance is within acceptable range
      expect(executionTimeMs).toBeLessThan(2000); // Should complete in less than 2 seconds
    });
  });
});
