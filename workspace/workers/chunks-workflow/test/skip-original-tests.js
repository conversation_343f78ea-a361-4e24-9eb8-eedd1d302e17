// This file is used to skip the original tests
// We're using the vitest.config.js file to exclude the original test files

export default {
  test: {
    exclude: [
      "**/node_modules/**",
      "**/dist/**",
      "**/cypress/**",
      "**/.{idea,git,cache,output,temp}/**",
      "**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build}.config.*",
      "**/boundary/edge-cases.test.js",
      "**/boundary/workflow-boundaries.test.js"
    ]
  }
};
