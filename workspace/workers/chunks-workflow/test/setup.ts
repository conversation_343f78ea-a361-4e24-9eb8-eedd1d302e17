import { vi } from 'vitest';

// Mock global fetch
global.fetch = vi.fn();

// Mock Cloudflare Workers environment
global.ExecutionContext = vi.fn();

// Mock Cloudflare Workers types
vi.mock('cloudflare:workers', () => ({
  WorkflowEntrypoint: class {},
  WorkflowEvent: class {},
  WorkflowStep: class {
    do: vi.fn().mockImplementation((name, options, fn) => {
      if (typeof options === 'function') {
        return options();
      }
      if (typeof fn === 'function') {
        return fn();
      }
      return Promise.resolve();
    })
  },
}));

vi.mock('cloudflare:workflows', () => ({
  NonRetryableError: class extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'NonRetryableError';
    }
  },
}));

// Reset all mocks before each test
beforeEach(() => {
  vi.resetAllMocks();
});
