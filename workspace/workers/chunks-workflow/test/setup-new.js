import { vi } from "vitest";

// Mock Cloudflare Workers API
class WorkflowStep {
  do(name, fn){
    return fn();
  }
}

class WorkflowEvent {
  constructor(payload){
    this.payload = payload;
  }
}

class WorkflowEntrypoint {
  constructor(ctx, env){
    this.ctx = ctx;
    this.env = env;
  }
}

// Mock crypto.subtle for tests
if(!global.crypto) {
  global.crypto = {
    subtle: {
      digest: async (algorithm, data)=>{
        return new Uint8Array([1, 2, 3, 4]).buffer;
      },
      importKey: async ()=>{
        return {};
      },
      sign: async ()=>{
        return new Uint8Array([1, 2, 3, 4]).buffer;
      }
    },
    getRandomValues: (buffer)=>buffer,
    randomUUID: ()=>"00000000-0000-0000-0000-000000000000"
  };
}

// Mock Headers class if not available
if(!global.Headers) {
  global.Headers = class Headers {
    constructor(init){
      this.headers = {};
      if(init) {
        if(init instanceof Headers) {
          Object.assign(this.headers, init.headers);
        } else if(Array.isArray(init)) {
          init.forEach(([key, value])=>{
            this.headers[key.toLowerCase()] = value;
          });
        } else if(typeof init === "object") {
          Object.entries(init).forEach(([key, value])=>{
            this.headers[key.toLowerCase()] = value;
          });
        }
      }
    }

    append(name, value){
      this.headers[name.toLowerCase()] = value;
    }

    delete(name){
      delete this.headers[name.toLowerCase()];
    }

    get(name){
      return this.headers[name.toLowerCase()] || null;
    }

    has(name){
      return name.toLowerCase() in this.headers;
    }

    set(name, value){
      this.headers[name.toLowerCase()] = value;
    }

    forEach(callback){
      Object.entries(this.headers).forEach(([key, value])=>{
        callback(value, key, this);
      });
    }
  };
}

// Mock Response class if not available
if(!global.Response) {
  global.Response = class Response {
    constructor(body, init = {}){
      this.body = body;
      this.status = init.status || 200;
      this.statusText = init.statusText || "";
      this.headers = new Headers(init.headers);
      this.ok = this.status >= 200 && this.status < 300;
    }

    async json(){
      if(typeof this.body === "string") {
        return JSON.parse(this.body);
      } else if(this.body && typeof this.body.json === "function") {
        return this.body.json();
      }
      return this.body;
    }

    async text(){
      if(typeof this.body === "string") {
        return this.body;
      } else if(this.body && typeof this.body.text === "function") {
        return this.body.text();
      } else if(this.body && typeof this.body.toString === "function") {
        return this.body.toString();
      }
      return String(this.body || "");
    }
  };
}

// Patch global.fetch to ensure Response objects have text and json methods
const originalFetch = global.fetch;
global.fetch = async (...args)=>{
  const response = await (originalFetch ? originalFetch(...args) : Promise.resolve(new Response()));

  // Ensure response has text method
  if(!response.text) {
    response.text = async ()=>{
      if(typeof response.body === "string") {
        return response.body;
      } else if(response.body && typeof response.body.text === "function") {
        return response.body.text();
      } else if(response.body && typeof response.body.toString === "function") {
        return response.body.toString();
      }
      return String(response.body || "");
    };
  }

  // Ensure response has json method
  if(!response.json) {
    response.json = async ()=>{
      try {
        if(typeof response.body === "string") {
          return JSON.parse(response.body);
        } else if(response.body && typeof response.body.json === "function") {
          return response.body.json();
        }
        return response.body;
      }catch(e) {
        throw new Error(`Failed to parse JSON: ${e.message}`);
      }
    };
  }

  return response;
};

// Mock Request class if not available
if(!global.Request) {
  global.Request = class Request {
    constructor(input, init = {}){
      if(input instanceof Request) {
        this.url = input.url;
        this.method = input.method;
        this.headers = new Headers(input.headers);
        this.body = input.body;
      } else {
        this.url = input;
        this.method = init.method || "GET";
        this.headers = new Headers(init.headers);
        this.body = init.body;
      }
    }

    async json(){
      if(typeof this.body === "string") {
        return JSON.parse(this.body);
      } else if(this.body && typeof this.body.json === "function") {
        return this.body.json();
      }
      return this.body;
    }

    async text(){
      if(typeof this.body === "string") {
        return this.body;
      } else if(this.body && typeof this.body.text === "function") {
        return this.body.text();
      } else if(this.body && typeof this.body.toString === "function") {
        return this.body.toString();
      }
      return String(this.body || "");
    }
  };
}

// Mock URL class if not available
if(!global.URL) {
  global.URL = class URL {
    constructor(url, base){
      if(base) {
        this.href = new URL(base).origin + url;
      } else {
        this.href = url;
      }

      // Parse URL parts
      try {
        const urlParts = this.href.match(/^(https?:\/\/)?([^\/]+)?(\/[^?#]*)?(\?[^#]*)?(#.*)?$/);
        this.protocol = urlParts[1] || "http://";
        this.hostname = urlParts[2] || "localhost";
        this.pathname = urlParts[3] || "/";
        this.search = urlParts[4] || "";
        this.hash = urlParts[5] || "";
        this.origin = this.protocol + this.hostname;
        this.host = this.hostname;
      }catch(e) {
        // Fallback for simple string URLs
        this.protocol = "http://";
        this.hostname = "localhost";
        this.pathname = "/";
        this.search = "";
        this.hash = "";
        this.origin = "http://localhost";
        this.host = "localhost";
      }
    }

    toString(){
      return this.href;
    }
  };
}

// Mock FormData class if not available
if(!global.FormData) {
  global.FormData = class FormData {
    constructor(){
      this.data = new Map();
    }

    append(name, value){
      this.data.set(name, value);
    }

    delete(name){
      this.data.delete(name);
    }

    get(name){
      return this.data.get(name);
    }

    getAll(name){
      return this.data.has(name) ? [this.data.get(name)] : [];
    }

    has(name){
      return this.data.has(name);
    }

    set(name, value){
      this.data.set(name, value);
    }

    forEach(callback){
      this.data.forEach((value, key)=>{
        callback(value, key, this);
      });
    }
  };
}

// Add process method to UnstructuredProcessor prototype if needed
try {
  const { UnstructuredProcessor } = require("../src/processors/unstructured");
  if(UnstructuredProcessor && !UnstructuredProcessor.prototype.process) {
    UnstructuredProcessor.prototype.process = async function(fileUrl, config){
      const sessionResult = await this.initializeSession(config, fileUrl);
      return this.processBatch(sessionResult.sessionId, 0, 100);
    };
  }
}catch(e) {
  console.warn("Could not add process method to UnstructuredProcessor:", e.message);
}

// Mock cloudflare:workers namespace
vi.mock("cloudflare:workers", ()=>{
  return {
    WorkflowStep,
    WorkflowEvent,
    WorkflowEntrypoint
  };
}, { virtual: true });

// Reset all mocks before each test
beforeEach(()=>{
  vi.resetAllMocks();

  // Reset fetch mock
  if(originalFetch) {
    global.fetch = originalFetch;
  } else {
    global.fetch = vi.fn().mockImplementation((...args)=>{
      return Promise.resolve(new Response("{}", {
        status: 200,
        headers: { "Content-Type": "application/json" }
      }));
    });
  }
});
