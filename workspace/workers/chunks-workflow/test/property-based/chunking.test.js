import { describe, it, expect, vi } from "vitest";
import * as fc from "fast-check";
import { OpenParseProcessor } from "../../src/processors/openparse";
import { createFetchResponse } from "../setup-comprehensive";

describe("Chunking Properties", ()=>{
  // Mock fetch for testing
  const mockFetch = (responses)=>{
    let callCount = 0;
    global.fetch = vi.fn().mockImplementation(()=>{
      const response = responses[callCount] || responses[responses.length - 1];
      callCount++;
      return Promise.resolve(response);
    });
  };

  // Helper function to create a processor with mocked responses
  const createProcessorWithMocks = (sessionResponse, chunksResponses)=>{
    const responses = [
      // Session initialization response
      createFetchResponse(sessionResponse, 200, { "Content-Type": "application/json" }),
      // Chunk responses
      ...chunksResponses.map(chunks=>createFetchResponse(chunks, 200, { "Content-Type": "application/json" })
      ),
      // Empty response to end processing
      createFetchResponse([], 200, { "Content-Type": "application/json" }),
      // Cleanup response
      createFetchResponse({ success: true }, 200, { "Content-Type": "application/json" })
    ];

    mockFetch(responses);

    return new OpenParseProcessor(
      "test-api-key",
      "https://openparse.example.com",
      {},
      { useStreaming: false }
    );
  };

  // Property: All chunks combined should contain all the original text content
  it("should preserve all text content when chunking", async ()=>{
    await fc.assert(
      fc.asyncProperty(
        // Generate an array of paragraphs (1-10 paragraphs)
        fc.array(fc.string({ minLength: 10, maxLength: 100 }), { minLength: 1, maxLength: 10 }),
        async (paragraphs)=>{
          // Create original text
          const originalText = paragraphs.join("\n\n");

          // Create mock chunks based on paragraphs
          const mockChunks = paragraphs.map((text, index)=>({
            id: `chunk-${index}`,
            text,
            metadata: { page: 1 }
          }));

          // Setup processor with mocks
          const processor = createProcessorWithMocks(
            { total_chunks: mockChunks.length, session_id: "test-session-id" },
            [mockChunks] // All chunks in one batch
          );

          // Process the text
          const chunks = await processor.process(
            "https://example.com/test-file.txt",
            { semantic: true }
          );

          // Verify: All chunks combined should contain all paragraphs
          const combinedText = chunks.map(c=>c.text).join(" ");

          // Check if each paragraph is contained in the combined text
          // We use a loose check because chunking might add or remove whitespace
          return paragraphs.every(paragraph=>{
            // Remove whitespace for comparison
            const normalizedParagraph = paragraph.replace(/\\s+/g, " ").trim();
            const normalizedCombined = combinedText.replace(/\\s+/g, " ").trim();

            return normalizedCombined.includes(normalizedParagraph);
          });
        }
      ),
      { numRuns: 25 } // Run 25 test cases
    );
  });

  // Property: Chunk count should be reasonable for the input size
  it("should create a reasonable number of chunks based on input size", async ()=>{
    await fc.assert(
      fc.asyncProperty(
        // Generate text of varying sizes
        fc.integer(1, 10).chain(paragraphCount=>fc.tuple(
            fc.constant(paragraphCount),
            fc.array(
              fc.string({ minLength: 50, maxLength: 200 }),
              { minLength: 1, maxLength: 10 }
            )
          )
        ),
        async ([paragraphCount, paragraphs])=>{
          // Create original text
          const originalText = paragraphs.join("\n\n");

          // Create mock chunks based on paragraphs
          const mockChunks = paragraphs.map((text, index)=>({
            id: `chunk-${index}`,
            text,
            metadata: { page: 1 }
          }));

          // Setup processor with mocks
          const processor = createProcessorWithMocks(
            { total_chunks: mockChunks.length, session_id: "test-session-id" },
            [mockChunks] // All chunks in one batch
          );

          // Process the text
          const chunks = await processor.process(
            "https://example.com/test-file.txt",
            { semantic: true }
          );

          // Verify: Number of chunks should be reasonable
          // For this test, we expect the number of chunks to match the mock chunks
          // This is because our mock is returning exactly what we're providing
          return chunks.length === mockChunks.length;
        }
      ),
      { numRuns: 15 } // Run 15 test cases
    );
  });

  // Property: Chunking should be deterministic
  it("should produce the same chunks for the same input and configuration", async ()=>{
    await fc.assert(
      fc.asyncProperty(
        // Generate text and configuration
        fc.tuple(
          fc.array(fc.string({ minLength: 20, maxLength: 100 }), { minLength: 1, maxLength: 5 }),
          fc.boolean(), // semantic chunking on/off
          fc.integer(100, 500) // maxTokens
        ),
        async ([paragraphs, semantic, maxTokens])=>{
          // Create original text
          const originalText = paragraphs.join("\n\n");

          // Create mock chunks
          const mockChunks = paragraphs.map((text, index)=>({
            id: `chunk-${index}`,
            text,
            metadata: { page: 1 }
          }));

          // Setup processor with mocks
          const processor1 = createProcessorWithMocks(
            { total_chunks: mockChunks.length, session_id: "test-session-id-1" },
            [mockChunks]
          );

          // Process the text - first run
          const chunks1 = await processor1.process(
            "https://example.com/test-file.txt",
            { semantic, maxTokens }
          );

          // Reset fetch mock
          vi.resetAllMocks();

          // Setup second processor with the same mocks
          const processor2 = createProcessorWithMocks(
            { total_chunks: mockChunks.length, session_id: "test-session-id-2" },
            [mockChunks]
          );

          // Process the text - second run
          const chunks2 = await processor2.process(
            "https://example.com/test-file.txt",
            { semantic, maxTokens }
          );

          // Verify: Both runs should produce the same number of chunks
          if(chunks1.length !== chunks2.length) return false;

          // Verify: Chunks should have the same text content
          return chunks1.every((chunk, i)=>chunk.text === chunks2[i].text
          );
        }
      ),
      { numRuns: 10 } // Run 10 test cases
    );
  });
});
