import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { OpenParseProcessor } from "../../src/processors/openparse";

// Mock environment
const mockEnv = {};

// Create a mock processor that we can control for testing
class MockOpenParseProcessor extends OpenParseProcessor {
  constructor(apiKey, apiUrl, env, options){
    super(apiKey, apiUrl, env, options);
    this.mockResponses = {
      initializeSession: null,
      processBatch: []
    };
    this.mockErrors = {
      initializeSession: null,
      processBatch: null
    };
  }

  // Override to use our mocks
  async initializeSession(fileUrl, config){
    if(this.mockErrors.initializeSession) {
      throw this.mockErrors.initializeSession;
    }
    if(this.mockResponses.initializeSession) {
      return this.mockResponses.initializeSession;
    }
    return super.initializeSession(fileUrl, config);
  }

  // Override to use our mocks
  async processBatch(batchNumber, config){
    if(this.mockErrors.processBatch) {
      throw this.mockErrors.processBatch;
    }
    if(this.mockResponses.processBatch[batchNumber] !== undefined) {
      return this.mockResponses.processBatch[batchNumber];
    }
    return super.processBatch(batchNumber, config);
  }

  // Override the process method to use our mocks directly
  async process(fileUrl, config){
    if(this.mockErrors.process) {
      throw this.mockErrors.process;
    }
    if(this.mockResponses.process) {
      return this.mockResponses.process;
    }

    // Call the original process method
    try {
      await this.initializeSession(fileUrl, config);
      const chunks = [];

      let batchNumber = 0;
      let batch;

      do {
        batch = await this.processBatch(batchNumber, { batchSize: 50 });
        if(batch) {
          chunks.push(...batch);
        }
        batchNumber++;
      } while(batch !== null);

      return chunks;
    }catch(error) {
      throw error;
    }
  }

  // Set mock responses
  setMockResponses(responses){
    this.mockResponses = { ...this.mockResponses, ...responses };
  }

  // Set mock errors
  setMockErrors(errors){
    this.mockErrors = { ...this.mockErrors, ...errors };
  }
}

describe("Boundary Testing - Edge Cases (Fixed Mock)", ()=>{
  beforeEach(()=>{
    // Mock console methods to avoid cluttering test output
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});

    // Reset fetch mock
    global.fetch = vi.fn();
  });

  afterEach(()=>{
    vi.restoreAllMocks();
  });

  describe("Empty and Minimal Inputs", ()=>{
    it("should handle empty documents gracefully", async ()=>{
      // Create processor instance with mock
      const processor = new MockOpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Set mock responses
      processor.setMockResponses({
        process: [] // Empty array for empty document
      });

      // Process empty document
      const chunks = await processor.process(
        "https://example.com/empty-file.pdf",
        { semantic: true }
      );

      // Verify result
      expect(chunks).toEqual([]);
    });
  });

  describe("Malformed Inputs", ()=>{
    it("should handle malformed URLs", async ()=>{
      // Create processor instance with mock
      const processor = new MockOpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Set mock error for initializeSession
      processor.setMockErrors({
        initializeSession: new Error("Invalid URL")
      });

      // Process document with malformed URL
      await expect(processor.process(
        "not-a-valid-url",
        { semantic: true }
      )).rejects.toThrow("Invalid URL");
    });

    it("should handle malformed API responses", async ()=>{
      // Create processor instance with mock
      const processor = new MockOpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Set mock error for initializeSession
      processor.setMockErrors({
        initializeSession: new Error("Malformed API response: Missing required fields")
      });

      // Process document with malformed API response
      await expect(processor.process(
        "https://example.com/test-file.pdf",
        { semantic: true }
      )).rejects.toThrow("Malformed API response");
    });

    it("should handle invalid JSON responses", async ()=>{
      // Create processor instance with mock
      const processor = new MockOpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Set mock error for initializeSession
      processor.setMockErrors({
        initializeSession: new Error("Invalid JSON response")
      });

      // Process document with invalid JSON response
      await expect(processor.process(
        "https://example.com/test-file.pdf",
        { semantic: true }
      )).rejects.toThrow("Invalid JSON response");
    });
  });

  describe("Edge Case Configurations", ()=>{
    it("should handle extreme configuration values", async ()=>{
      // Create processor instance with mock
      const processor = new MockOpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Set mock responses
      processor.setMockResponses({
        process: [
          { id: "chunk-1", text: "Test chunk with extreme config", metadata: {} }
        ]
      });

      // Process document with extreme configuration values
      const chunks = await processor.process(
        "https://example.com/test-file.pdf",
        {
          semantic: true,
          maxTokens: 10000, // Extreme value
          minTokens: 1, // Extreme value
          chunkOverlap: 5000, // Extreme value
          relevanceThreshold: 0.99 // Extreme value
        }
      );

      // Verify result
      expect(chunks.length).toBe(1);
      expect(chunks[0].text).toBe("Test chunk with extreme config");
    });

    it("should handle missing configuration values", async ()=>{
      // Create processor instance with mock
      const processor = new MockOpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Set mock responses
      processor.setMockResponses({
        process: [
          { id: "chunk-1", text: "Test chunk with default config", metadata: {} }
        ]
      });

      // Process document with missing configuration values
      const chunks = await processor.process(
        "https://example.com/test-file.pdf",
        {} // Empty config
      );

      // Verify result
      expect(chunks.length).toBe(1);
      expect(chunks[0].text).toBe("Test chunk with default config");
    });
  });

  describe("Unusual File Types", ()=>{
    it("should handle unusual file types", async ()=>{
      // Create processor instance with mock
      const processor = new MockOpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Set mock responses
      processor.setMockResponses({
        process: [
          { id: "chunk-1", text: "Test chunk from unusual file", metadata: {} }
        ]
      });

      // Process unusual file types
      const unusualFileTypes = [
        "https://example.com/test-file.svg", // SVG file
        "https://example.com/test-file.epub", // EPUB file
        "https://example.com/test-file.mobi", // MOBI file
        "https://example.com/test-file.rtf", // RTF file
        "https://example.com/test-file.odt", // ODT file
        "https://example.com/test-file.csv", // CSV file
        "https://example.com/test-file.xml", // XML file
        "https://example.com/test-file.json", // JSON file
        "https://example.com/test-file.md", // Markdown file
        "https://example.com/test-file.tex", // LaTeX file
      ];

      for(const fileUrl of unusualFileTypes) {
        const chunks = await processor.process(fileUrl, { semantic: true });

        // Verify result
        expect(chunks.length).toBe(1);
        expect(chunks[0].text).toBe("Test chunk from unusual file");
      }
    });
  });

  describe("Error Conditions", ()=>{
    it("should handle API rate limiting", async ()=>{
      // Create processor instance with mock
      const processor = new MockOpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Set mock responses
      processor.setMockResponses({
        process: [
          { id: "chunk-1", text: "Test chunk after rate limit", metadata: {} }
        ]
      });

      // Set up a retry mechanism
      let callCount = 0;
      const originalProcess = processor.process;
      processor.process = async (fileUrl, config)=>{
        callCount++;
        if(callCount === 1) {
          throw new Error("Rate limit exceeded");
        }
        return originalProcess.call(processor, fileUrl, config);
      };

      // Process document with rate limiting
      try {
        await processor.process(
          "https://example.com/test-file.pdf",
          { semantic: true }
        );
        // If we get here, the test failed
        throw new Error("Expected rate limit error");
      }catch(error) {
        // Verify error
        expect(error.message).toBe("Rate limit exceeded");
        expect(callCount).toBe(1); // Should have tried once
      }
    });

    it("should handle API authentication errors", async ()=>{
      // Create processor instance with mock
      const processor = new MockOpenParseProcessor(
        "invalid-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Set mock error for initializeSession
      processor.setMockErrors({
        initializeSession: new Error("Authentication failed: Invalid API key")
      });

      // Process document with authentication error
      await expect(processor.process(
        "https://example.com/test-file.pdf",
        { semantic: true }
      )).rejects.toThrow("Authentication failed");
    });

    it("should handle API server errors", async ()=>{
      // Create processor instance with mock
      const processor = new MockOpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Set mock error for initializeSession
      processor.setMockErrors({
        initializeSession: new Error("Server error: Internal server error")
      });

      // Process document with server error
      await expect(processor.process(
        "https://example.com/test-file.pdf",
        { semantic: true }
      )).rejects.toThrow("Server error");
    });
  });
});
