// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Snapshot Testing - Processor Output > OpenParse Processor > should produce consistent output for PDF documents 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > OpenParse Processor > should produce consistent output for semantic chunking > regular-chunking 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > OpenParse Processor > should produce consistent output for semantic chunking > semantic-chunking 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > OpenParse Processor > should produce consistent output with different chunk sizes > large-chunks 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > OpenParse Processor > should produce consistent output with different chunk sizes > medium-chunks 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > OpenParse Processor > should produce consistent output with different chunk sizes > small-chunks 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > Unstructured Processor > should produce consistent output for PDF documents 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > Unstructured Processor > should produce consistent output for different document types > docx-document 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > Unstructured Processor > should produce consistent output for different document types > pdf-document 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > Unstructured Processor > should produce consistent output for different document types > pptx-document 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Workflow Output > Workflow Database Operations > should produce consistent database update operations > database-update-operations 1`] = `
[
  {
    "data": {
      "fileId": "test-file-id-1",
      "status": "success-file",
    },
    "id": "test-workflow-id",
  },
  {
    "data": {
      "status": undefined,
    },
    "id": "test-workflow-id",
  },
]
`;

exports[`Snapshot Testing - Workflow Output > Workflow Database Operations > should produce consistent storage operations > storage-operations 1`] = `
{
  "getOperations": [
    "test-object-key-1",
  ],
  "putOperations": [
    {
      "data": "[{"id":"chunk-1","text":"This is test chunk 1 from OpenParse","metadata":{"page":1}},{"id":"chunk-2","text":"This is test chunk 2 from OpenParse","metadata":{"page":1}}]",
      "key": "test-rag-id/test-file-id-1/chunks.json",
    },
  ],
}
`;

exports[`Snapshot Testing - Workflow Output > Workflow Error Handling > should produce consistent output for file-level errors > file-level-error 1`] = `
{
  "processedFiles": [
    {
      "error": "Storage error",
      "fileId": "test-file-id-1",
      "status": "error",
    },
  ],
  "status": "success",
  "success": true,
  "workflowId": "test-workflow-id",
}
`;

exports[`Snapshot Testing - Workflow Output > Workflow Error Handling > should produce consistent output for workflow-level errors > workflow-level-error 1`] = `
{
  "error": "Database error",
  "status": "error",
  "success": false,
  "workflowId": "test-workflow-id",
}
`;

exports[`Snapshot Testing - Workflow Output > Workflow Run Output > should produce consistent output for successful workflow runs > successful-workflow-run 1`] = `
{
  "processedFiles": [
    {
      "chunks": [
        {
          "id": "chunk-1",
          "metadata": {
            "page": 1,
          },
          "text": "This is test chunk 1 from OpenParse",
        },
        {
          "id": "chunk-2",
          "metadata": {
            "page": 1,
          },
          "text": "This is test chunk 2 from OpenParse",
        },
      ],
      "fileId": "test-file-id-1",
      "status": "success",
    },
  ],
  "status": "success",
  "success": true,
  "workflowId": "test-workflow-id",
}
`;

exports[`Snapshot Testing - Workflow Output > Workflow Run Output > should produce consistent output for workflows with mixed success and failure > mixed-success-failure-workflow-run 1`] = `
{
  "processedFiles": [
    {
      "chunks": [
        {
          "id": "chunk-1",
          "metadata": {
            "page": 1,
          },
          "text": "This is test chunk 1 from OpenParse",
        },
        {
          "id": "chunk-2",
          "metadata": {
            "page": 1,
          },
          "text": "This is test chunk 2 from OpenParse",
        },
      ],
      "fileId": "test-file-id-1",
      "status": "success",
    },
    {
      "chunks": [
        {
          "id": "chunk-1",
          "metadata": {
            "page": 1,
          },
          "text": "This is test chunk 1 from Unstructured",
        },
        {
          "id": "chunk-2",
          "metadata": {
            "page": 1,
          },
          "text": "This is test chunk 2 from Unstructured",
        },
      ],
      "fileId": "test-file-id-2",
      "status": "success",
    },
    {
      "chunks": [],
      "fileId": "test-file-id-3",
      "status": "success",
    },
  ],
  "status": "success",
  "success": true,
  "workflowId": "test-workflow-id",
}
`;

exports[`Snapshot Testing - Workflow Output > Workflow Run Output > should produce consistent output for workflows with multiple files > multiple-files-workflow-run 1`] = `
{
  "processedFiles": [
    {
      "chunks": [
        {
          "id": "chunk-1",
          "metadata": {
            "page": 1,
          },
          "text": "This is test chunk 1 from OpenParse",
        },
        {
          "id": "chunk-2",
          "metadata": {
            "page": 1,
          },
          "text": "This is test chunk 2 from OpenParse",
        },
      ],
      "fileId": "test-file-id-1",
      "status": "success",
    },
    {
      "chunks": [
        {
          "id": "chunk-1",
          "metadata": {
            "page": 1,
          },
          "text": "This is test chunk 1 from Unstructured",
        },
        {
          "id": "chunk-2",
          "metadata": {
            "page": 1,
          },
          "text": "This is test chunk 2 from Unstructured",
        },
      ],
      "fileId": "test-file-id-2",
      "status": "success",
    },
  ],
  "status": "success",
  "success": true,
  "workflowId": "test-workflow-id",
}
`;
