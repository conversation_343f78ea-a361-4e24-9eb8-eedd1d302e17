import { vi } from "vitest";

/**
 * Comprehensive test setup file for chunks-workflow tests
 *
 * This file provides:
 * 1. Mocks for Cloudflare Workers API
 * 2. Mocks for Web API objects (Response, Request, Headers, URL, etc.)
 * 3. Mocks for crypto functions
 * 4. Utility functions for creating mock responses
 * 5. Automatic reset of mocks between tests
 */

// ===== Cloudflare Workers API Mocks =====

// Mock WorkflowStep class
class WorkflowStep {
  do(name, fn){
    return fn();
  }
}

// Mock WorkflowEvent class
class WorkflowEvent {
  constructor(payload){
    this.payload = payload;
  }
}

// Mock WorkflowEntrypoint class
class WorkflowEntrypoint {
  constructor(ctx, env){
    this.ctx = ctx;
    this.env = env;
  }
}

// Mock the cloudflare:workers module
vi.mock("cloudflare:workers", ()=>{
  return {
    WorkflowStep,
    WorkflowEvent,
    WorkflowEntrypoint
  };
}, { virtual: true });

// ===== Web API Mocks =====

// Mock Headers class
global.Headers = class Headers {
  constructor(init){
    this.headers = {};
    if(init) {
      if(init instanceof Headers) {
        Object.assign(this.headers, init.headers);
      } else if(Array.isArray(init)) {
        init.forEach(([key, value])=>{
          this.headers[key.toLowerCase()] = value;
        });
      } else if(typeof init === "object") {
        Object.entries(init).forEach(([key, value])=>{
          this.headers[key.toLowerCase()] = value;
        });
      }
    }
  }

  append(name, value){
    this.headers[name.toLowerCase()] = value;
  }

  delete(name){
    delete this.headers[name.toLowerCase()];
  }

  get(name){
    return this.headers[name.toLowerCase()] || null;
  }

  has(name){
    return name.toLowerCase() in this.headers;
  }

  set(name, value){
    this.headers[name.toLowerCase()] = value;
  }

  forEach(callback){
    Object.entries(this.headers).forEach(([key, value])=>{
      callback(value, key, this);
    });
  }

  // Convert to object for easier inspection in tests
  toObject(){
    return { ...this.headers };
  }
};

// Mock Response class
global.Response = class Response {
  constructor(body, init = {}){
    this.body = body;
    this.status = init.status || 200;
    this.statusText = init.statusText || "";
    this.headers = new Headers(init.headers);
    this.ok = this.status >= 200 && this.status < 300;
  }

  async json(){
    try {
      if(typeof this.body === "string") {
        return JSON.parse(this.body);
      } else if(this.body && typeof this.body.json === "function") {
        return this.body.json();
      }
      return this.body;
    }catch(e) {
      throw new Error(`Failed to parse JSON: ${e.message}`);
    }
  }

  async text(){
    if(typeof this.body === "string") {
      return this.body;
    } else if(this.body && typeof this.body.text === "function") {
      return this.body.text();
    } else if(this.body && typeof this.body.toString === "function") {
      return this.body.toString();
    }
    return String(this.body || "");
  }

  clone(){
    return new Response(this.body, {
      status: this.status,
      statusText: this.statusText,
      headers: this.headers
    });
  }
};

// Mock Request class
global.Request = class Request {
  constructor(input, init = {}){
    if(input instanceof Request) {
      this.url = input.url;
      this.method = input.method;
      this.headers = new Headers(input.headers);
      this.body = input.body;
    } else {
      this.url = input;
      this.method = init.method || "GET";
      this.headers = new Headers(init.headers);
      this.body = init.body;
    }
  }

  async json(){
    try {
      if(typeof this.body === "string") {
        return JSON.parse(this.body);
      } else if(this.body && typeof this.body.json === "function") {
        return this.body.json();
      }
      return this.body;
    }catch(e) {
      throw new Error(`Failed to parse JSON: ${e.message}`);
    }
  }

  async text(){
    if(typeof this.body === "string") {
      return this.body;
    } else if(this.body && typeof this.body.text === "function") {
      return this.body.text();
    } else if(this.body && typeof this.body.toString === "function") {
      return this.body.toString();
    }
    return String(this.body || "");
  }

  clone(){
    return new Request(this.url, {
      method: this.method,
      headers: this.headers,
      body: this.body
    });
  }
};

// Mock URL class
global.URL = class URL {
  constructor(url, base){
    // Handle base URL
    if(base) {
      if(typeof base === "string") {
        this.href = new URL(base).origin + (url.startsWith("/") ? url : "/" + url);
      } else {
        this.href = base.origin + (url.startsWith("/") ? url : "/" + url);
      }
    } else {
      this.href = url;
    }

    // Parse URL parts
    try {
      // Simple URL parsing
      const urlRegex = /^(https?:\/\/)?([^\/]+)?(\/[^?#]*)?(\?[^#]*)?(#.*)?$/;
      const urlParts = this.href.match(urlRegex);

      if(urlParts) {
        this.protocol = urlParts[1] || "http://";
        this.hostname = urlParts[2] || "localhost";
        this.pathname = urlParts[3] || "/";
        this.search = urlParts[4] || "";
        this.hash = urlParts[5] || "";
        this.host = this.hostname;
        this.origin = this.protocol + this.hostname;
      } else {
        // Fallback for simple string URLs
        this.protocol = "http://";
        this.hostname = "localhost";
        this.pathname = "/";
        this.search = "";
        this.hash = "";
        this.host = "localhost";
        this.origin = "http://localhost";
      }
    }catch(e) {
      // Fallback for any parsing errors
      this.protocol = "http://";
      this.hostname = "localhost";
      this.pathname = "/";
      this.search = "";
      this.hash = "";
      this.host = "localhost";
      this.origin = "http://localhost";
    }
  }

  toString(){
    return this.href;
  }

  // Add searchParams for URL query parameters
  get searchParams(){
    const params = new URLSearchParams(this.search);
    return params;
  }
};

// Mock URLSearchParams
global.URLSearchParams = class URLSearchParams {
  constructor(init){
    this.params = new Map();

    if(init) {
      if(typeof init === "string") {
        // Remove leading ? if present
        const query = init.startsWith("?") ? init.substring(1) : init;

        // Parse query string
        query.split("&").forEach(pair=>{
          if(pair) {
            const [key, value] = pair.split("=");
            this.append(decodeURIComponent(key), decodeURIComponent(value || ""));
          }
        });
      } else if(init instanceof URLSearchParams) {
        init.forEach((value, key)=>{
          this.append(key, value);
        });
      } else if(typeof init === "object") {
        Object.entries(init).forEach(([key, value])=>{
          this.append(key, value);
        });
      }
    }
  }

  append(name, value){
    const values = this.params.get(name) || [];
    values.push(String(value));
    this.params.set(name, values);
  }

  delete(name){
    this.params.delete(name);
  }

  get(name){
    const values = this.params.get(name);
    return values ? values[0] : null;
  }

  getAll(name){
    return this.params.get(name) || [];
  }

  has(name){
    return this.params.has(name);
  }

  set(name, value){
    this.params.set(name, [String(value)]);
  }

  forEach(callback){
    this.params.forEach((values, key)=>{
      values.forEach(value=>{
        callback(value, key, this);
      });
    });
  }

  toString(){
    const pairs = [];
    this.params.forEach((values, key)=>{
      values.forEach(value=>{
        pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      });
    });
    return pairs.join("&");
  }
};

// Mock FormData
global.FormData = class FormData {
  constructor(){
    this.data = new Map();
  }

  append(name, value){
    this.data.set(name, value);
  }

  delete(name){
    this.data.delete(name);
  }

  get(name){
    return this.data.get(name);
  }

  getAll(name){
    return this.data.has(name) ? [this.data.get(name)] : [];
  }

  has(name){
    return this.data.has(name);
  }

  set(name, value){
    this.data.set(name, value);
  }

  forEach(callback){
    this.data.forEach((value, key)=>{
      callback(value, key, this);
    });
  }
};

// ===== Crypto Mocks =====

// Mock crypto.subtle
if(!global.crypto) {
  global.crypto = {
    subtle: {
      digest: async (algorithm, data)=>{
        // Return a fixed hash for testing
        return new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]).buffer;
      },
      importKey: async (format, keyData, algorithm, extractable, keyUsages)=>{
        // Return a mock key
        return { type: "secret", algorithm, extractable, usages: keyUsages };
      },
      sign: async (algorithm, key, data)=>{
        // Return a fixed signature for testing
        return new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]).buffer;
      }
    },
    getRandomValues: (buffer)=>{
      // Fill buffer with deterministic values for testing
      for(let i = 0; i < buffer.length; i++) {
        buffer[i] = i % 256;
      }
      return buffer;
    },
    randomUUID: ()=>"00000000-0000-0000-0000-000000000000"
  };
}

// ===== Fetch Mock =====

// Create a helper for creating fetch responses
const createFetchResponse = (body, status = 200, headers = {})=>{
  // Ensure content type is set
  const contentType = headers["Content-Type"] || headers["content-type"] || "application/json";
  let responseBody = body;

  // Convert body to string if it's an object and content type is JSON
  if(typeof body === "object" && contentType.includes("json")) {
    responseBody = JSON.stringify(body);
  }

  // Create a basic response object
  const response = {
    body: responseBody,
    status,
    statusText: status >= 200 && status < 300 ? "OK" : "Error",
    headers: new Headers({
      "Content-Type": contentType,
      ...headers
    }),
    ok: status >= 200 && status < 300
  };

  // Enhance the response with all required methods
  return enhanceResponse(response);
};

// Create a mock fetch function
global.fetch = vi.fn().mockImplementation((url, options = {})=>{
  // Default to a successful JSON response
  return Promise.resolve(createFetchResponse(
    { success: true },
    200,
    { "Content-Type": "application/json" }
  ));
});

// Helper function to enhance Response objects with required methods
function enhanceResponse(response){
  if(!response) return response;

  // If response is already a proper Response object with all required methods, return it
  if(response.text && response.json && response.blob && response.arrayBuffer &&
      response.headers && typeof response.headers.forEach === "function") {
    return response;
  }

  // Create a copy of the response to avoid modifying the original
  const enhancedResponse = { ...response };

  // Ensure ok property is set based on status
  if(enhancedResponse.status !== undefined && enhancedResponse.ok === undefined) {
    enhancedResponse.ok = enhancedResponse.status >= 200 && enhancedResponse.status < 300;
  }

  // Ensure statusText is set
  if(enhancedResponse.statusText === undefined) {
    enhancedResponse.statusText = enhancedResponse.ok ? "OK" : "Error";
  }

  // Add text method if missing
  if(!enhancedResponse.text) {
    enhancedResponse.text = async ()=>{
      // For error responses with JSON body, stringify it
      if(!enhancedResponse.ok && typeof enhancedResponse.body === "object") {
        try {
          return JSON.stringify(enhancedResponse.body);
        }catch(e) {
          console.error("Error stringifying response body:", e);
        }
      }

      // Handle different body types
      if(typeof enhancedResponse.body === "string") {
        return enhancedResponse.body;
      } else if(enhancedResponse.body && typeof enhancedResponse.body.text === "function") {
        return enhancedResponse.body.text();
      } else if(enhancedResponse.body && typeof enhancedResponse.body.toString === "function") {
        return enhancedResponse.body.toString();
      } else if(enhancedResponse.body instanceof ArrayBuffer || enhancedResponse.body instanceof Uint8Array) {
        // Convert ArrayBuffer to string
        return new TextDecoder().decode(enhancedResponse.body);
      } else if(enhancedResponse.body === null || enhancedResponse.body === undefined) {
        return "";
      }

      // Fallback for other types
      return String(enhancedResponse.body || "");
    };
  }

  // Add json method if missing
  if(!enhancedResponse.json) {
    enhancedResponse.json = async ()=>{
      try {
        // First get the text content
        const text = await enhancedResponse.text();

        // Try to parse as JSON
        if(text.trim() === "") {
          return {}; // Return empty object for empty responses
        }

        try {
          return JSON.parse(text);
        }catch(parseError) {
          console.error("Failed to parse JSON:", text);
          throw new Error(`Failed to parse JSON: ${parseError.message}. Response text: ${text.substring(0, 100)}...`);
        }
      }catch(e) {
        // Fallback to other methods if text() fails
        if(typeof enhancedResponse.body === "string") {
          return JSON.parse(enhancedResponse.body);
        } else if(enhancedResponse.body && typeof enhancedResponse.body.json === "function") {
          return enhancedResponse.body.json();
        } else if(typeof enhancedResponse.body === "object" && enhancedResponse.body !== null) {
          return enhancedResponse.body;
        }

        throw new Error(`Failed to parse JSON: ${e.message}`);
      }
    };
  }

  // Add blob method if missing
  if(!enhancedResponse.blob) {
    enhancedResponse.blob = async ()=>{
      if(enhancedResponse.body instanceof Blob) {
        return enhancedResponse.body;
      }
      const contentType = enhancedResponse.headers?.get?.("content-type") || "application/octet-stream";
      if(typeof enhancedResponse.body === "string") {
        return new Blob([enhancedResponse.body], { type: contentType });
      }
      return new Blob([new Uint8Array(0)], { type: contentType });
    };
  }

  // Add arrayBuffer method if missing
  if(!enhancedResponse.arrayBuffer) {
    enhancedResponse.arrayBuffer = async ()=>{
      if(enhancedResponse.body instanceof ArrayBuffer) {
        return enhancedResponse.body;
      }
      if(typeof enhancedResponse.body === "string") {
        return new TextEncoder().encode(enhancedResponse.body).buffer;
      }
      return new ArrayBuffer(0);
    };
  }

  // Ensure headers has forEach method
  if(!enhancedResponse.headers) {
    enhancedResponse.headers = new Headers();
  } else if(!enhancedResponse.headers.forEach) {
    const originalHeaders = enhancedResponse.headers;
    const newHeaders = new Headers();

    // Copy headers if possible
    if(typeof originalHeaders === "object") {
      Object.entries(originalHeaders).forEach(([key, value])=>{
        newHeaders.set(key, value);
      });
    }

    enhancedResponse.headers = newHeaders;
  }

  // Add clone method if missing
  if(!enhancedResponse.clone) {
    enhancedResponse.clone = function(){
      return enhanceResponse(this);
    };
  }

  return enhancedResponse;
}

// Add the createFetchResponse helper to global scope for tests
global.createFetchResponse = createFetchResponse;

// ===== Test Utilities =====

// Helper to create mock responses (using our createFetchResponse helper)
global.createMockResponse = (body, status = 200, headers = {})=>{
  return createFetchResponse(body, status, headers);
};

// Helper to create mock error responses
global.createErrorResponse = (status, message)=>{
  return createFetchResponse(
    { error: message },
    status,
    { "Content-Type": "application/json" }
  );
};

// Helper to mock fetch for specific URLs
global.mockFetchForUrl = (urlPattern, responseOrFn, options = {})=>{
  const { method = "GET", status = 200, headers = {} } = options;

  // Update the global fetch mock
  global.fetch = vi.fn().mockImplementation((url, requestOptions = {})=>{
    // Check if the URL matches the pattern
    if(url.match(urlPattern) && (!method || requestOptions.method === method)) {
      // If responseOrFn is a function, call it with the request details
      if(typeof responseOrFn === "function") {
        return Promise.resolve(responseOrFn(url, requestOptions));
      }

      // Otherwise, return the response
      return Promise.resolve(createFetchResponse(responseOrFn, status, headers));
    }

    // Default response for other URLs
    return Promise.resolve(createFetchResponse(
      { success: true },
      200,
      { "Content-Type": "application/json" }
    ));
  });

  // Return the mock function for chaining
  return global.fetch;
};

// Helper to mock fetch errors
global.mockFetchError = (urlPattern, errorMessage, options = {})=>{
  const { method = "GET" } = options;

  // Update the global fetch mock
  global.fetch = vi.fn().mockImplementation((url, requestOptions = {})=>{
    // Check if the URL matches the pattern
    if(url.match(urlPattern) && (!method || requestOptions.method === method)) {
      // Return a rejected promise
      return Promise.reject(new Error(errorMessage));
    }

    // Default response for other URLs
    return Promise.resolve(createFetchResponse(
      { success: true },
      200,
      { "Content-Type": "application/json" }
    ));
  });

  // Return the mock function for chaining
  return global.fetch;
};

// Helper to mock AWS signature headers
global.createAwsSignatureHeaders = (accessKey = "test-access-key", region = "us-east-1", service = "s3")=>{
  // Use fixed values for testing to ensure consistency
  const amzDate = "20230101T000000Z";
  const dateStamp = "20230101";
  const signature = "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890";
  const contentSha256 = "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";

  // Create credential string
  const credential = `${accessKey}/${dateStamp}/${region}/${service}/aws4_request`;

  // Create authorization header
  const authorization = `AWS4-HMAC-SHA256 Credential=${credential}, SignedHeaders=host;x-amz-content-sha256;x-amz-date, Signature=${signature}`;

  // Return headers
  return {
    "Authorization": authorization,
    "x-amz-date": amzDate,
    "x-amz-content-sha256": contentSha256
  };
};

// Helper to mock AWS signature v4 implementation
global.mockAwsSignatureV4 = ()=>{
  // Mock the aws-sig-v4 module
  vi.mock("../../src/utils/aws-sig-v4", ()=>{
    return {
      generateAuthorizationHeader: (options)=>{
        return global.createAwsSignatureHeaders(
          options.accessKey,
          options.region,
          options.service
        );
      }
    };
  }, { virtual: true });
};

// ===== Processor Mocks =====

// Mock UnstructuredProcessor
class MockUnstructuredProcessor {
  constructor(apiKey, apiUrl){
    this.apiKey = apiKey;
    this.apiUrl = apiUrl;
    this.sessionId = null;
  }

  async initializeSession(config, fileUrl){
    // Check for error case
    if(global.fetch.mock && global.fetch.mock.calls.length > 0 &&
        global.fetch.mock.results[0].value &&
        !global.fetch.mock.results[0].value.ok) {
      throw new Error("Failed to initialize unstructured session");
    }

    // Mock initialization
    this.sessionId = "test-session-id";
    return { sessionId: this.sessionId };
  }

  async processBatch(sessionId, offset, limit){
    // Check for error case
    if(global.fetch.mock && global.fetch.mock.calls.length > 0 &&
        global.fetch.mock.results[0].value &&
        !global.fetch.mock.results[0].value.ok) {
      throw new Error("Failed to get chunks from unstructured");
    }

    // Mock processing
    return [
      {
        id: "chunk-1",
        text: "This is test chunk 1",
        metadata: {
          page: 1,
          type: "Title"
        }
      },
      {
        id: "chunk-2",
        text: "This is test chunk 2",
        metadata: {
          page: 1,
          type: "Paragraph"
        }
      }
    ];
  }

  async process(fileUrl, config){
    // Initialize session
    await this.initializeSession(config, fileUrl);

    // Process batches
    return this.processBatch(this.sessionId, 0, 100);
  }

  async dispose(){
    // Check for error case
    if(global.fetch.mock && global.fetch.mock.calls.length > 0 &&
        global.fetch.mock.results[0].value &&
        !global.fetch.mock.results[0].value.ok) {
      throw new Error("Failed to dispose unstructured session");
    }

    // Mock cleanup
    this.sessionId = null;
    return true;
  }
}

// Mock OpenParseProcessor
class MockOpenParseProcessor {
  constructor(apiKey, apiUrl){
    this.apiKey = apiKey;
    this.apiUrl = apiUrl;
    this.sessionId = null;
  }

  async initializeSession(fileUrl, config){
    // Check for error case
    if(global.fetch.mock && global.fetch.mock.calls.length > 0 &&
        global.fetch.mock.results[0].value &&
        !global.fetch.mock.results[0].value.ok) {
      throw new Error("Failed to initialize OpenParse session");
    }

    // Mock initialization
    this.sessionId = "test-session-id";
    return { sessionId: this.sessionId };
  }

  async processBatch(sessionId, offset, limit){
    // Check for error case
    if(global.fetch.mock && global.fetch.mock.calls.length > 0 &&
        global.fetch.mock.results[0].value &&
        !global.fetch.mock.results[0].value.ok) {
      throw new Error("Failed to get chunks from OpenParse");
    }

    // Mock processing
    return [
      {
        id: "chunk-1",
        text: "This is test chunk 1",
        metadata: {
          page: 1,
          type: "Title"
        }
      },
      {
        id: "chunk-2",
        text: "This is test chunk 2",
        metadata: {
          page: 1,
          type: "Paragraph"
        }
      }
    ];
  }

  async process(fileUrl, config){
    // Initialize session
    await this.initializeSession(fileUrl, config);

    // Process batches
    return this.processBatch(this.sessionId, 0, 100);
  }

  async dispose(){
    // Check for error case
    if(global.fetch.mock && global.fetch.mock.calls.length > 0 &&
        global.fetch.mock.results[0].value &&
        !global.fetch.mock.results[0].value.ok) {
      throw new Error("Failed to dispose OpenParse session");
    }

    // Mock cleanup
    this.sessionId = null;
    return true;
  }
}

// Add processor mocks to global scope
global.MockUnstructuredProcessor = MockUnstructuredProcessor;
global.MockOpenParseProcessor = MockOpenParseProcessor;

// Mock the processor modules
vi.mock("../src/processors/unstructured", ()=>{
  return {
    UnstructuredProcessor: MockUnstructuredProcessor
  };
}, { virtual: true });

vi.mock("../src/processors/openparse", ()=>{
  return {
    OpenParseProcessor: MockOpenParseProcessor
  };
}, { virtual: true });

// ===== Test Setup Hooks =====

// Store original implementations
const originalFetch = global.fetch;
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

// Reset all mocks before each test
beforeEach(()=>{
  // Reset all mocks
  vi.resetAllMocks();

  // Reset fetch mock
  global.fetch = vi.fn().mockImplementation((url, options = {})=>{
    // Default to a successful JSON response
    return Promise.resolve(createFetchResponse(
      { success: true },
      200,
      { "Content-Type": "application/json" }
    ));
  });

  // Apply our patched fetch implementation to ensure all responses have required methods
  const originalFetchMock = global.fetch;
  global.fetch = async (...args)=>{
    try {
      const response = await originalFetchMock(...args);

      // Ensure response has all required methods
      const enhancedResponse = enhanceResponse(response);
      return enhancedResponse;
    }catch(error) {
      console.error("Error in patched fetch:", error);
      throw error;
    }
  };

  // Mock console methods to avoid cluttering test output
  console.log = vi.fn();
  console.warn = vi.fn();
  console.error = vi.fn();
});

// Restore original implementations after each test
afterEach(()=>{
  // Restore console methods
  console.log = originalConsoleLog;
  console.warn = originalConsoleWarn;
  console.error = originalConsoleError;
});

// Clean up after all tests
afterAll(()=>{
  // Restore all mocks
  vi.restoreAllMocks();
});

// ===== Export Test Utilities =====

export {
  // Cloudflare Workers mocks
  WorkflowStep,
  WorkflowEvent,
  WorkflowEntrypoint,

  // Response helpers
  createFetchResponse,
  createMockResponse,
  createErrorResponse,

  // Fetch helpers
  mockFetchForUrl,
  mockFetchError,

  // AWS helpers
  createAwsSignatureHeaders,
  mockAwsSignatureV4,

  // Processor mocks
  MockUnstructuredProcessor,
  MockOpenParseProcessor
};
