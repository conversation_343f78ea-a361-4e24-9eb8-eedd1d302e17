import { describe, it, expect } from "vitest";
import * as fc from "fast-check";

// Simple utility functions for testing
const normalizeText = (text: string)=>text.replace(/\s+/g, " ").trim();

const splitIntoChunks = (text: string, maxChunkSize: number)=>{
  const words = text.split(/\s+/);
  const chunks = [];
  let currentChunk = "";

  for(const word of words) {
    if((currentChunk + " " + word).length > maxChunkSize && currentChunk.length > 0) {
      chunks.push(currentChunk);
      currentChunk = word;
    } else {
      currentChunk += (currentChunk ? " " : "") + word;
    }
  }

  if(currentChunk.length > 0) {
    chunks.push(currentChunk);
  }

  return chunks;
};

describe("Property-Based Tests for Text Chunking", ()=>{
  // Property: All chunks combined should contain all the original text content
  it("should preserve all text content when chunking", ()=>{
    fc.assert(
      fc.property(
        // Generate text of varying sizes
        fc.string({ minLength: 10, maxLength: 1000 }),
        // Generate max chunk size
        fc.integer({ min: 50, max: 500 }),
        (text, maxChunkSize)=>{
          // Split text into chunks
          const chunks = splitIntoChunks(text, maxChunkSize);

          // Combine chunks back into text
          const combinedText = chunks.join(" ");

          // Verify: Combined text should contain all words from original text
          const originalWords = new Set(normalizeText(text).split(" ").filter(Boolean));
          const combinedWords = new Set(normalizeText(combinedText).split(" ").filter(Boolean));

          // Check if all original words are in the combined text
          return Array.from(originalWords).every(word=>combinedWords.has(word));
        }
      ),
      { numRuns: 100 } // Run 100 test cases
    );
  });

  // Property: No chunk should exceed the maximum size
  it("should respect maximum chunk size", ()=>{
    fc.assert(
      fc.property(
        // Generate text of varying sizes
        fc.string({ minLength: 10, maxLength: 1000 }),
        // Generate max chunk size
        fc.integer({ min: 50, max: 500 }),
        (text, maxChunkSize)=>{
          // Split text into chunks
          const chunks = splitIntoChunks(text, maxChunkSize);

          // Verify: No chunk should exceed the maximum size
          return chunks.every(chunk=>chunk.length <= maxChunkSize);
        }
      ),
      { numRuns: 100 } // Run 100 test cases
    );
  });

  // Property: Chunking should be deterministic
  it("should produce the same chunks for the same input", ()=>{
    fc.assert(
      fc.property(
        // Generate text of varying sizes
        fc.string({ minLength: 10, maxLength: 1000 }),
        // Generate max chunk size
        fc.integer({ min: 50, max: 500 }),
        (text, maxChunkSize)=>{
          // Split text into chunks twice
          const chunks1 = splitIntoChunks(text, maxChunkSize);
          const chunks2 = splitIntoChunks(text, maxChunkSize);

          // Verify: Both runs should produce the same chunks
          if(chunks1.length !== chunks2.length) return false;

          return chunks1.every((chunk, i)=>chunk === chunks2[i]);
        }
      ),
      { numRuns: 100 } // Run 100 test cases
    );
  });

  // Property: Chunk count should be reasonable for the input size
  it("should create a reasonable number of chunks based on input size", ()=>{
    fc.assert(
      fc.property(
        // Generate text of varying sizes
        fc.string({ minLength: 10, maxLength: 1000 }),
        // Generate max chunk size
        fc.integer({ min: 50, max: 500 }),
        (text, maxChunkSize)=>{
          // Split text into chunks
          const chunks = splitIntoChunks(text, maxChunkSize);

          // Verify: Number of chunks should be reasonable
          // Expected number of chunks is approximately text length / max chunk size
          const expectedChunks = Math.max(1, Math.ceil(text.length / maxChunkSize));

          // Allow some flexibility (±2 chunks or ±50%)
          const minAcceptable = Math.max(1, expectedChunks - Math.max(2, expectedChunks * 0.5));
          const maxAcceptable = expectedChunks + Math.max(2, expectedChunks * 0.5);

          return chunks.length >= minAcceptable && chunks.length <= maxAcceptable;
        }
      ),
      { numRuns: 100 } // Run 100 test cases
    );
  });
});
