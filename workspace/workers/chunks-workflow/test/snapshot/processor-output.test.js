import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { createFetchResponse } from "../setup-comprehensive";
import { OpenParseProcessor } from "../../src/processors/openparse";
import { UnstructuredProcessor } from "../../src/processors/unstructured";

// Mock environment
const mockEnv = {};

describe("Snapshot Testing - Processor Output", ()=>{
  beforeEach(()=>{
    // Mock console methods to avoid cluttering test output
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});

    // Reset fetch mock
    global.fetch = vi.fn();
  });

  afterEach(()=>{
    vi.restoreAllMocks();
  });

  describe("OpenParse Processor", ()=>{
    it("should produce consistent output for PDF documents", async ()=>{
      // Mock fetch for OpenParse API
      global.fetch.mockImplementation((url, options)=>{
        if(url.includes("/fileUrl/init")) {
          return Promise.resolve(createFetchResponse(
            {
              total_chunks: 3,
              session_id: "test-session-id"
            },
            200,
            { "Content-Type": "application/json" }
          ));
        }

        if(url.includes("/fileUrl/batch")) {
          const batchNumber = JSON.parse(options.body).batch_number;

          if(batchNumber === 0) {
            return Promise.resolve(createFetchResponse(
              [
                {
                  id: "chunk-1",
                  text: "This is the title of the document",
                  metadata: {
                    page: 1,
                    type: "Title"
                  }
                },
                {
                  id: "chunk-2",
                  text: "This is the first paragraph of the document. It contains some text that will be processed by the OpenParse processor.",
                  metadata: {
                    page: 1,
                    type: "Paragraph"
                  }
                },
                {
                  id: "chunk-3",
                  text: "This is the second paragraph of the document. It also contains some text that will be processed by the OpenParse processor.",
                  metadata: {
                    page: 1,
                    type: "Paragraph"
                  }
                }
              ],
              200,
              { "Content-Type": "application/json" }
            ));
          } else {
            return Promise.resolve(createFetchResponse(
              [],
              200,
              { "Content-Type": "application/json" }
            ));
          }
        }

        if(url.includes("/cleanup/") || url.includes("/session/")) {
          return Promise.resolve(createFetchResponse(
            { success: true },
            200,
            { "Content-Type": "application/json" }
          ));
        }

        return Promise.resolve(createFetchResponse(
          { error: `Unexpected URL: ${url}` },
          404,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Process document
      const chunks = await processor.process(
        "https://example.com/test-file.pdf",
        { semantic: true }
      );

      // Verify output matches snapshot
      expect(chunks).toMatchSnapshot();
    });

    it("should produce consistent output for semantic chunking", async ()=>{
      // Mock fetch for OpenParse API with semantic chunking
      global.fetch.mockImplementation((url, options)=>{
        if(url.includes("/fileUrl/init")) {
          return Promise.resolve(createFetchResponse(
            {
              total_chunks: 2,
              session_id: "test-session-id"
            },
            200,
            { "Content-Type": "application/json" }
          ));
        }

        if(url.includes("/fileUrl/batch")) {
          const batchNumber = JSON.parse(options.body).batch_number;
          const requestBody = JSON.parse(options.body);

          // Check if semantic chunking is enabled
          const isSemanticChunking = requestBody.semantic_chunking === true || requestBody.semantic === true;

          if(batchNumber === 0) {
            // Return semantically chunked content
            if(isSemanticChunking) {
              return Promise.resolve(createFetchResponse(
                [
                  {
                    id: "semantic-chunk-1",
                    text: "This is a semantic chunk about artificial intelligence. AI is the simulation of human intelligence processes by machines, especially computer systems. These processes include learning, reasoning, and self-correction.",
                    metadata: {
                      page: 1,
                      type: "Semantic"
                    }
                  },
                  {
                    id: "semantic-chunk-2",
                    text: "This is a semantic chunk about machine learning. Machine learning is a subset of AI that provides systems the ability to automatically learn and improve from experience without being explicitly programmed.",
                    metadata: {
                      page: 1,
                      type: "Semantic"
                    }
                  }
                ],
                200,
                { "Content-Type": "application/json" }
              ));
            } else {
              // Return regular chunks
              return Promise.resolve(createFetchResponse(
                [
                  {
                    id: "chunk-1",
                    text: "This is the first paragraph about artificial intelligence.",
                    metadata: {
                      page: 1,
                      type: "Paragraph"
                    }
                  },
                  {
                    id: "chunk-2",
                    text: "This is the second paragraph about machine learning.",
                    metadata: {
                      page: 1,
                      type: "Paragraph"
                    }
                  }
                ],
                200,
                { "Content-Type": "application/json" }
              ));
            }
          } else {
            return Promise.resolve(createFetchResponse(
              [],
              200,
              { "Content-Type": "application/json" }
            ));
          }
        }

        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Process document with semantic chunking
      const semanticChunks = await processor.process(
        "https://example.com/test-file.pdf",
        { semantic: true }
      );

      // Verify semantic output matches snapshot
      expect(semanticChunks).toMatchSnapshot("semantic-chunking");

      // Process document without semantic chunking
      const regularChunks = await processor.process(
        "https://example.com/test-file.pdf",
        { semantic: false }
      );

      // Verify regular output matches snapshot
      expect(regularChunks).toMatchSnapshot("regular-chunking");
    });

    it("should produce consistent output with different chunk sizes", async ()=>{
      // Mock fetch for OpenParse API with different chunk sizes
      global.fetch.mockImplementation((url, options)=>{
        if(url.includes("/fileUrl/init")) {
          return Promise.resolve(createFetchResponse(
            {
              total_chunks: 2,
              session_id: "test-session-id"
            },
            200,
            { "Content-Type": "application/json" }
          ));
        }

        if(url.includes("/fileUrl/batch")) {
          const batchNumber = JSON.parse(options.body).batch_number;
          const requestBody = JSON.parse(options.body);

          // Check chunk size configuration
          const maxTokens = requestBody.maxTokens || 1024; // Default

          if(batchNumber === 0) {
            if(maxTokens <= 512) {
              // Small chunks
              return Promise.resolve(createFetchResponse(
                [
                  {
                    id: "small-chunk-1",
                    text: "This is a small chunk.",
                    metadata: {
                      page: 1,
                      type: "Paragraph"
                    }
                  },
                  {
                    id: "small-chunk-2",
                    text: "This is another small chunk.",
                    metadata: {
                      page: 1,
                      type: "Paragraph"
                    }
                  },
                  {
                    id: "small-chunk-3",
                    text: "This is yet another small chunk.",
                    metadata: {
                      page: 1,
                      type: "Paragraph"
                    }
                  },
                  {
                    id: "small-chunk-4",
                    text: "This is the final small chunk.",
                    metadata: {
                      page: 1,
                      type: "Paragraph"
                    }
                  }
                ],
                200,
                { "Content-Type": "application/json" }
              ));
            } else if(maxTokens >= 2048) {
              // Large chunks
              return Promise.resolve(createFetchResponse(
                [
                  {
                    id: "large-chunk-1",
                    text: "This is a large chunk that contains more text. It has multiple sentences and covers more content than a small chunk would. This is to simulate the behavior of the OpenParse processor when configured to use larger chunk sizes.",
                    metadata: {
                      page: 1,
                      type: "Paragraph"
                    }
                  }
                ],
                200,
                { "Content-Type": "application/json" }
              ));
            } else {
              // Medium chunks (default)
              return Promise.resolve(createFetchResponse(
                [
                  {
                    id: "medium-chunk-1",
                    text: "This is a medium-sized chunk. It contains a moderate amount of text.",
                    metadata: {
                      page: 1,
                      type: "Paragraph"
                    }
                  },
                  {
                    id: "medium-chunk-2",
                    text: "This is another medium-sized chunk. It also contains a moderate amount of text.",
                    metadata: {
                      page: 1,
                      type: "Paragraph"
                    }
                  }
                ],
                200,
                { "Content-Type": "application/json" }
              ));
            }
          } else {
            return Promise.resolve(createFetchResponse(
              [],
              200,
              { "Content-Type": "application/json" }
            ));
          }
        }

        return Promise.resolve(createFetchResponse(
          { success: true },
          200,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instance
      const processor = new OpenParseProcessor(
        "test-api-key",
        "https://openparse.example.com",
        mockEnv,
        { useStreaming: false }
      );

      // Process document with small chunks
      const smallChunks = await processor.process(
        "https://example.com/test-file.pdf",
        { semantic: true, maxTokens: 512 }
      );

      // Verify small chunks output matches snapshot
      expect(smallChunks).toMatchSnapshot("small-chunks");

      // Process document with medium chunks (default)
      const mediumChunks = await processor.process(
        "https://example.com/test-file.pdf",
        { semantic: true, maxTokens: 1024 }
      );

      // Verify medium chunks output matches snapshot
      expect(mediumChunks).toMatchSnapshot("medium-chunks");

      // Process document with large chunks
      const largeChunks = await processor.process(
        "https://example.com/test-file.pdf",
        { semantic: true, maxTokens: 2048 }
      );

      // Verify large chunks output matches snapshot
      expect(largeChunks).toMatchSnapshot("large-chunks");
    });
  });

  describe("Unstructured Processor", ()=>{
    it("should produce consistent output for PDF documents", async ()=>{
      // Mock fetch for Unstructured API
      global.fetch.mockImplementation((url, options)=>{
        if(url.includes("/general/v0/general")) {
          return Promise.resolve(createFetchResponse(
            [
              {
                type: "Title",
                element_id: "title-1",
                text: "This is the title of the document",
                metadata: {
                  page_number: 1
                }
              },
              {
                type: "NarrativeText",
                element_id: "text-1",
                text: "This is the first paragraph of the document. It contains some text that will be processed by the Unstructured processor.",
                metadata: {
                  page_number: 1
                }
              },
              {
                type: "NarrativeText",
                element_id: "text-2",
                text: "This is the second paragraph of the document. It also contains some text that will be processed by the Unstructured processor.",
                metadata: {
                  page_number: 1
                }
              }
            ],
            200,
            { "Content-Type": "application/json" }
          ));
        }

        return Promise.resolve(createFetchResponse(
          { error: `Unexpected URL: ${url}` },
          404,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instance
      const processor = new UnstructuredProcessor(
        "test-api-key",
        "https://unstructured.example.com",
        mockEnv
      );

      // Process document
      const chunks = await processor.process(
        "https://example.com/test-file.pdf",
        {}
      );

      // Verify output matches snapshot
      expect(chunks).toMatchSnapshot();
    });

    it("should produce consistent output for different document types", async ()=>{
      // Mock fetch for Unstructured API with different document types
      global.fetch.mockImplementation((url, options)=>{
        if(url.includes("/general/v0/general")) {
          const fileUrl = JSON.parse(options.body).files[0].url;

          if(fileUrl.endsWith(".pdf")) {
            // PDF document
            return Promise.resolve(createFetchResponse(
              [
                {
                  type: "Title",
                  element_id: "pdf-title-1",
                  text: "This is a PDF document",
                  metadata: {
                    page_number: 1
                  }
                },
                {
                  type: "NarrativeText",
                  element_id: "pdf-text-1",
                  text: "This is the content of the PDF document.",
                  metadata: {
                    page_number: 1
                  }
                }
              ],
              200,
              { "Content-Type": "application/json" }
            ));
          } else if(fileUrl.endsWith(".docx")) {
            // DOCX document
            return Promise.resolve(createFetchResponse(
              [
                {
                  type: "Title",
                  element_id: "docx-title-1",
                  text: "This is a DOCX document",
                  metadata: {
                    page_number: 1
                  }
                },
                {
                  type: "NarrativeText",
                  element_id: "docx-text-1",
                  text: "This is the content of the DOCX document.",
                  metadata: {
                    page_number: 1
                  }
                }
              ],
              200,
              { "Content-Type": "application/json" }
            ));
          } else if(fileUrl.endsWith(".pptx")) {
            // PPTX document
            return Promise.resolve(createFetchResponse(
              [
                {
                  type: "Title",
                  element_id: "pptx-title-1",
                  text: "This is a PPTX document",
                  metadata: {
                    page_number: 1
                  }
                },
                {
                  type: "NarrativeText",
                  element_id: "pptx-text-1",
                  text: "This is the content of the PPTX document.",
                  metadata: {
                    page_number: 1
                  }
                }
              ],
              200,
              { "Content-Type": "application/json" }
            ));
          } else {
            // Default
            return Promise.resolve(createFetchResponse(
              [
                {
                  type: "Title",
                  element_id: "default-title-1",
                  text: "This is a document",
                  metadata: {
                    page_number: 1
                  }
                },
                {
                  type: "NarrativeText",
                  element_id: "default-text-1",
                  text: "This is the content of the document.",
                  metadata: {
                    page_number: 1
                  }
                }
              ],
              200,
              { "Content-Type": "application/json" }
            ));
          }
        }

        return Promise.resolve(createFetchResponse(
          { error: `Unexpected URL: ${url}` },
          404,
          { "Content-Type": "application/json" }
        ));
      });

      // Create processor instance
      const processor = new UnstructuredProcessor(
        "test-api-key",
        "https://unstructured.example.com",
        mockEnv
      );

      // Process PDF document
      const pdfChunks = await processor.process(
        "https://example.com/test-file.pdf",
        {}
      );

      // Verify PDF output matches snapshot
      expect(pdfChunks).toMatchSnapshot("pdf-document");

      // Process DOCX document
      const docxChunks = await processor.process(
        "https://example.com/test-file.docx",
        {}
      );

      // Verify DOCX output matches snapshot
      expect(docxChunks).toMatchSnapshot("docx-document");

      // Process PPTX document
      const pptxChunks = await processor.process(
        "https://example.com/test-file.pptx",
        {}
      );

      // Verify PPTX output matches snapshot
      expect(pptxChunks).toMatchSnapshot("pptx-document");
    });
  });
});
