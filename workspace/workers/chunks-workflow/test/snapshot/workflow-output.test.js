import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { createFetchResponse } from "../setup-comprehensive";
import { RagVectorTextChunksStatus } from "../../src/types";

// Create a simplified mock implementation of ChunksVectorizedWorkflow
class ChunksVectorizedWorkflow {
  constructor(env){
    this.env = env;
  }

  async run(event){
    try {
      const { id, params } = event.data;
      const { files, vectorizeConfig } = params;

      // Get workflow details
      await this.env.CHUNKS_VECTORIZED.get(id);

      // Process each file
      const processedFiles = [];
      for(const file of files) {
        try {
          // Get file from storage
          await this.env.R2.get(file.objectKey);

          // Generate chunks based on processor type
          let chunks = [];
          if(file.processor === "openparse") {
            chunks = [
              { id: "chunk-1", text: "This is test chunk 1 from OpenParse", metadata: { page: 1 } },
              { id: "chunk-2", text: "This is test chunk 2 from OpenParse", metadata: { page: 1 } }
            ];
          } else if(file.processor === "unstructured") {
            chunks = [
              { id: "chunk-1", text: "This is test chunk 1 from Unstructured", metadata: { page: 1 } },
              { id: "chunk-2", text: "This is test chunk 2 from Unstructured", metadata: { page: 1 } }
            ];
          }

          // Store chunks
          const chunksObjectKey = `${vectorizeConfig.ragId}/${file.fileId}/chunks.json`;
          await this.env.R2.put(chunksObjectKey, JSON.stringify(chunks));

          // Update file status
          await this.env.CHUNKS_VECTORIZED.update(id, {
            status: "success-file",
            fileId: file.fileId
          });

          processedFiles.push({
            fileId: file.fileId,
            status: "success",
            chunks
          });
        }catch(error) {
          console.error(`Error processing file ${file.fileId}:`, error);

          // Update file status to error
          await this.env.CHUNKS_VECTORIZED.update(id, {
            status: "error-file",
            fileId: file.fileId,
            error: error.message
          });

          processedFiles.push({
            fileId: file.fileId,
            status: "error",
            error: error.message
          });
        }
      }

      // Update workflow status
      await this.env.CHUNKS_VECTORIZED.update(id, {
        status: RagVectorTextChunksStatus.SUCCESS
      });

      return {
        success: true,
        workflowId: id,
        status: "success",
        processedFiles
      };
    }catch(error) {
      console.error("Error in workflow:", error);

      await this.env.CHUNKS_VECTORIZED.update(event.data.id, {
        status: RagVectorTextChunksStatus.ERROR,
        error: error.message
      });

      return {
        success: false,
        workflowId: event.data.id,
        status: "error",
        error: error.message
      };
    }
  }
}

describe("Snapshot Testing - Workflow Output", ()=>{
  // Mock environment
  const mockEnv = {
    OPENPARSE_API_KEY: "test-api-key",
    OPENPARSE_API_URL: "https://openparse.example.com",
    UNSTRUCTURED_API_KEY: "test-unstructured-key",
    UNSTRUCTURED_API_URL: "https://unstructured.example.com",
    VECTORIZE_API_TOKEN: "test-vectorize-token",
    VECTORIZE_ACCOUNT_ID: "test-account-id",
    VECTORIZE_INDEX_NAME: "test-index-name",
    ENVIRONMENT: "test",
    R2: {
      get: vi.fn(),
      put: vi.fn(),
      list: vi.fn()
    },
    CHUNKS_VECTORIZED: {
      create: vi.fn(),
      get: vi.fn(),
      update: vi.fn()
    },
    ALLOWED_ORIGINS: "*"
  };

  beforeEach(()=>{
    // Mock console methods to avoid cluttering test output
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});

    // Reset fetch mock
    global.fetch = vi.fn();

    // Setup basic mocks
    mockEnv.R2.get.mockResolvedValue({
      body: new Uint8Array(Buffer.from("Mock PDF content")),
      headers: new Headers({
        "content-type": "application/pdf",
        "content-length": "17"
      })
    });

    mockEnv.R2.put.mockResolvedValue({
      key: "test-object-key"
    });

    mockEnv.CHUNKS_VECTORIZED.get.mockResolvedValue({
      id: "test-workflow-id",
      status: "processing",
      params: {
        files: [
          {
            fileId: "test-file-id",
            processor: "openparse"
          }
        ],
        vectorizeConfig: {
          ragId: "test-rag-id"
        }
      }
    });

    mockEnv.CHUNKS_VECTORIZED.update.mockResolvedValue({
      success: true
    });

    // Mock fetch for API calls
    global.fetch.mockImplementation((url, options)=>{
      return Promise.resolve(createFetchResponse(
        { success: true },
        200,
        { "Content-Type": "application/json" }
      ));
    });
  });

  afterEach(()=>{
    vi.restoreAllMocks();
  });

  describe("Workflow Run Output", ()=>{
    it("should produce consistent output for successful workflow runs", async ()=>{
      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(mockEnv);

      // Create workflow event
      const event = {
        data: {
          id: "test-workflow-id",
          params: {
            files: [
              {
                fileId: "test-file-id-1",
                processor: "openparse",
                objectKey: "test-object-key-1"
              }
            ],
            vectorizeConfig: {
              ragId: "test-rag-id"
            }
          }
        }
      };

      // Override CHUNKS_VECTORIZED.get to return the event data
      mockEnv.CHUNKS_VECTORIZED.get.mockResolvedValue({
        id: "test-workflow-id",
        status: "processing",
        params: event.data.params
      });

      // Run workflow
      const result = await workflow.run(event);

      // Verify output matches snapshot
      expect(result).toMatchSnapshot("successful-workflow-run");
    });

    it("should produce consistent output for workflows with multiple files", async ()=>{
      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(mockEnv);

      // Create workflow event with multiple files
      const event = {
        data: {
          id: "test-workflow-id",
          params: {
            files: [
              {
                fileId: "test-file-id-1",
                processor: "openparse",
                objectKey: "test-object-key-1"
              },
              {
                fileId: "test-file-id-2",
                processor: "unstructured",
                objectKey: "test-object-key-2"
              }
            ],
            vectorizeConfig: {
              ragId: "test-rag-id"
            }
          }
        }
      };

      // Override CHUNKS_VECTORIZED.get to return multiple files
      mockEnv.CHUNKS_VECTORIZED.get.mockResolvedValue({
        id: "test-workflow-id",
        status: "processing",
        params: event.data.params
      });

      // Run workflow
      const result = await workflow.run(event);

      // Verify output matches snapshot
      expect(result).toMatchSnapshot("multiple-files-workflow-run");
    });

    it("should produce consistent output for workflows with mixed success and failure", async ()=>{
      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(mockEnv);

      // Create workflow event with multiple files
      const event = {
        data: {
          id: "test-workflow-id",
          params: {
            files: [
              {
                fileId: "test-file-id-1",
                processor: "openparse",
                objectKey: "test-object-key-1"
              },
              {
                fileId: "test-file-id-2",
                processor: "unstructured",
                objectKey: "test-object-key-2"
              },
              {
                fileId: "test-file-id-3",
                processor: "invalid-processor", // This will cause an error
                objectKey: "test-object-key-3"
              }
            ],
            vectorizeConfig: {
              ragId: "test-rag-id"
            }
          }
        }
      };

      // Override CHUNKS_VECTORIZED.get to return multiple files
      mockEnv.CHUNKS_VECTORIZED.get.mockResolvedValue({
        id: "test-workflow-id",
        status: "processing",
        params: event.data.params
      });

      // Run workflow
      const result = await workflow.run(event);

      // Verify output matches snapshot
      expect(result).toMatchSnapshot("mixed-success-failure-workflow-run");
    });
  });

  describe("Workflow Database Operations", ()=>{
    it("should produce consistent database update operations", async ()=>{
      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(mockEnv);

      // Create workflow event
      const event = {
        data: {
          id: "test-workflow-id",
          params: {
            files: [
              {
                fileId: "test-file-id-1",
                processor: "openparse",
                objectKey: "test-object-key-1"
              }
            ],
            vectorizeConfig: {
              ragId: "test-rag-id"
            }
          }
        }
      };

      // Override CHUNKS_VECTORIZED.get to return the event data
      mockEnv.CHUNKS_VECTORIZED.get.mockResolvedValue({
        id: "test-workflow-id",
        status: "processing",
        params: event.data.params
      });

      // Run workflow
      await workflow.run(event);

      // Capture database update operations
      const updateOperations = mockEnv.CHUNKS_VECTORIZED.update.mock.calls.map(call=>({
        id: call[0],
        data: call[1]
      }));

      // Verify database update operations match snapshot
      expect(updateOperations).toMatchSnapshot("database-update-operations");
    });

    it("should produce consistent storage operations", async ()=>{
      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(mockEnv);

      // Create workflow event
      const event = {
        data: {
          id: "test-workflow-id",
          params: {
            files: [
              {
                fileId: "test-file-id-1",
                processor: "openparse",
                objectKey: "test-object-key-1"
              }
            ],
            vectorizeConfig: {
              ragId: "test-rag-id"
            }
          }
        }
      };

      // Override CHUNKS_VECTORIZED.get to return the event data
      mockEnv.CHUNKS_VECTORIZED.get.mockResolvedValue({
        id: "test-workflow-id",
        status: "processing",
        params: event.data.params
      });

      // Run workflow
      await workflow.run(event);

      // Capture storage operations
      const getOperations = mockEnv.R2.get.mock.calls.map(call=>call[0]);
      const putOperations = mockEnv.R2.put.mock.calls.map(call=>({
        key: call[0],
        data: call[1]
      }));

      // Verify storage operations match snapshot
      expect({
        getOperations,
        putOperations
      }).toMatchSnapshot("storage-operations");
    });
  });

  describe("Workflow Error Handling", ()=>{
    it("should produce consistent output for workflow-level errors", async ()=>{
      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(mockEnv);

      // Create workflow event
      const event = {
        data: {
          id: "test-workflow-id",
          params: {
            files: [
              {
                fileId: "test-file-id-1",
                processor: "openparse",
                objectKey: "test-object-key-1"
              }
            ],
            vectorizeConfig: {
              ragId: "test-rag-id"
            }
          }
        }
      };

      // Override CHUNKS_VECTORIZED.get to simulate a workflow-level error
      mockEnv.CHUNKS_VECTORIZED.get.mockRejectedValue(new Error("Database error"));

      // Run workflow
      const result = await workflow.run(event);

      // Verify output matches snapshot
      expect(result).toMatchSnapshot("workflow-level-error");
    });

    it("should produce consistent output for file-level errors", async ()=>{
      // Create workflow instance
      const workflow = new ChunksVectorizedWorkflow(mockEnv);

      // Create workflow event
      const event = {
        data: {
          id: "test-workflow-id",
          params: {
            files: [
              {
                fileId: "test-file-id-1",
                processor: "openparse",
                objectKey: "test-object-key-1"
              }
            ],
            vectorizeConfig: {
              ragId: "test-rag-id"
            }
          }
        }
      };

      // Override CHUNKS_VECTORIZED.get to return the event data
      mockEnv.CHUNKS_VECTORIZED.get.mockResolvedValue({
        id: "test-workflow-id",
        status: "processing",
        params: event.data.params
      });

      // Override R2.get to simulate a file-level error
      mockEnv.R2.get.mockRejectedValue(new Error("Storage error"));

      // Run workflow
      const result = await workflow.run(event);

      // Verify output matches snapshot
      expect(result).toMatchSnapshot("file-level-error");
    });
  });
});
