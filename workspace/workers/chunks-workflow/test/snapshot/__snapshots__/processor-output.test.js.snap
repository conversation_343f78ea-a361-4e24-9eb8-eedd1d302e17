// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Snapshot Testing - Processor Output > OpenParse Processor > should produce consistent output for PDF documents 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > OpenParse Processor > should produce consistent output for semantic chunking > regular-chunking 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > OpenParse Processor > should produce consistent output for semantic chunking > semantic-chunking 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > OpenParse Processor > should produce consistent output with different chunk sizes > large-chunks 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > OpenParse Processor > should produce consistent output with different chunk sizes > medium-chunks 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > OpenParse Processor > should produce consistent output with different chunk sizes > small-chunks 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > Unstructured Processor > should produce consistent output for PDF documents 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > Unstructured Processor > should produce consistent output for different document types > docx-document 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > Unstructured Processor > should produce consistent output for different document types > pdf-document 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;

exports[`Snapshot Testing - Processor Output > Unstructured Processor > should produce consistent output for different document types > pptx-document 1`] = `
[
  {
    "id": "chunk-1",
    "metadata": {
      "page": 1,
      "type": "Title",
    },
    "text": "This is test chunk 1",
  },
  {
    "id": "chunk-2",
    "metadata": {
      "page": 1,
      "type": "Paragraph",
    },
    "text": "This is test chunk 2",
  },
]
`;
