import { describe, it, expect, vi, beforeEach } from "vitest";
import app from "../src/index";
import { ChunksVectorizedWorkflow } from "../src/workflows/chunks-vectorized";
import { createStorageClient } from "../src/utils/storage-client";

// Mock the workflow
vi.mock("../src/workflows/chunks-vectorized", ()=>({
  ChunksVectorizedWorkflow: vi.fn().mockImplementation(()=>({
    run: vi.fn().mockResolvedValue(undefined)
  }))
}));

// Mock the storage client
vi.mock("../src/utils/storage-client", ()=>({
  createStorageClient: vi.fn().mockReturnValue({
    get: vi.fn().mockResolvedValue({ body: "test-file-content" }),
    put: vi.fn().mockResolvedValue({ key: "test-object-key" }),
    delete: vi.fn().mockResolvedValue(undefined),
    list: vi.fn().mockResolvedValue({ objects: [] })
  })
}));

describe("API Routes", ()=>{
  // Mock console methods to avoid cluttering test output
  beforeEach(()=>{
    vi.spyOn(console, "log").mockImplementation(()=>{});
    vi.spyOn(console, "warn").mockImplementation(()=>{});
    vi.spyOn(console, "error").mockImplementation(()=>{});

    // Mock crypto.randomUUID
    vi.spyOn(crypto, "randomUUID").mockReturnValue("********-0000-0000-0000-************");
  });

  describe("POST /api/process", ()=>{
    it("should create a workflow for a single file", async ()=>{
      // Mock environment
      const env = {
        ALLOWED_ORIGINS: "*",
        CHUNKS_VECTORIZED: {
          create: vi.fn().mockResolvedValue({ id: "test-workflow-id" })
        }
      };

      // Create request
      const req = new Request("https://example.com/api/process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          files: [
            {
              fileId: "test-file-id",
              target: "test-target",
              fileName: "test-file.pdf",
              bucket: "test-bucket",
              objectKey: "test-object-key",
              processor: "openparse",
              processorConfig: {
                semantic_chunking: true,
                embeddings_provider: "cloudflare"
              }
            }
          ],
          vectorizeConfig: {
            accountId: "test-account",
            apiToken: "test-token",
            ragName: "test-rag",
            ragId: "test-rag-id",
            whitelabelId: "test-whitelabel",
            auth0Token: "test-auth0-token"
          }
        })
      });

      // Process request
      const res = await app.fetch(req, env);
      const data = await res.json();

      // Verify response
      expect(res.status).toBe(200);
      expect(data).toEqual({
        success: true,
        workflowId: "wf-test-target-test-file-id-********-0000-0000-0000-************"
      });

      // Verify workflow creation
      expect(env.CHUNKS_VECTORIZED.create).toHaveBeenCalledWith({
        id: "wf-test-target-test-file-id-********-0000-0000-0000-************",
        params: expect.objectContaining({
          files: [expect.objectContaining({
            fileId: "test-file-id",
            processor: "openparse"
          })],
          vectorizeConfig: expect.objectContaining({
            ragId: "test-rag-id"
          })
        })
      });
    });

    it("should handle validation errors", async ()=>{
      // Mock environment
      const env = {
        ALLOWED_ORIGINS: "*",
        CHUNKS_VECTORIZED: {
          create: vi.fn().mockResolvedValue({ id: "test-workflow-id" })
        }
      };

      // Create request with invalid data (missing required fields)
      const req = new Request("https://example.com/api/process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          files: [
            {
              // Missing required fields
              processor: "openparse"
            }
          ],
          // Missing vectorizeConfig
        })
      });

      // Process request
      const res = await app.fetch(req, env);

      // Verify response
      expect(res.status).toBe(400); // Bad request due to validation error
    });
  });

  describe("POST /api/process/batch", ()=>{
    it("should create a workflow for multiple files", async ()=>{
      // Mock environment
      const env = {
        ALLOWED_ORIGINS: "*",
        CHUNKS_VECTORIZED: {
          create: vi.fn().mockResolvedValue({ id: "test-workflow-id" })
        }
      };

      // Create request
      const req = new Request("https://example.com/api/process/batch", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          files: [
            {
              fileId: "test-file-id-1",
              target: "test-target",
              fileName: "test-file-1.pdf",
              bucket: "test-bucket",
              objectKey: "test-object-key-1",
              processor: "openparse"
            },
            {
              fileId: "test-file-id-2",
              target: "test-target",
              fileName: "test-file-2.pdf",
              bucket: "test-bucket",
              objectKey: "test-object-key-2",
              processor: "unstructured"
            }
          ],
          vectorizeConfig: {
            accountId: "test-account",
            apiToken: "test-token",
            ragName: "test-rag",
            ragId: "test-rag-id",
            whitelabelId: "test-whitelabel",
            auth0Token: "test-auth0-token"
          }
        })
      });

      // Process request
      const res = await app.fetch(req, env);
      const data = await res.json();

      // Verify response
      expect(res.status).toBe(200);
      expect(data).toEqual({
        success: true,
        workflowId: "wf-batch-********-0000-0000-0000-************"
      });

      // Verify workflow creation
      expect(env.CHUNKS_VECTORIZED.create).toHaveBeenCalledWith({
        id: "wf-batch-********-0000-0000-0000-************",
        params: expect.objectContaining({
          files: expect.arrayContaining([
            expect.objectContaining({ fileId: "test-file-id-1" }),
            expect.objectContaining({ fileId: "test-file-id-2" })
          ]),
          vectorizeConfig: expect.objectContaining({
            ragId: "test-rag-id"
          })
        })
      });
    });
  });

  describe("POST /api/process/stream", ()=>{
    it("should process a file stream and create a workflow", async ()=>{
      // Mock environment
      const env = {
        ALLOWED_ORIGINS: "*",
        ENVIRONMENT: "production",
        CHUNKS_VECTORIZED: {
          create: vi.fn().mockResolvedValue({ id: "test-workflow-id" })
        },
        R2: {
          put: vi.fn().mockResolvedValue({ key: "test-object-key" })
        },
        VECTORIZE_API_TOKEN: "test-token",
        VECTORIZE_ACCOUNT_ID: "test-account",
        VECTORIZE_INDEX_NAME: "test-index",
        OPENPARSE_API_KEY: "test-key",
        OPENPARSE_API_URL: "https://openparse.example.com",
        UNSTRUCTURED_API_KEY: "test-key",
        UNSTRUCTURED_API_URL: "https://unstructured.example.com"
      };

      // Mock the createStorageClient function
      vi.mocked(createStorageClient).mockReturnValue({
        put: vi.fn().mockResolvedValue({}),
        get: vi.fn().mockResolvedValue({}),
        delete: vi.fn().mockResolvedValue({}),
        list: vi.fn().mockResolvedValue([])
      });

      // Create a file buffer
      const fileBuffer = new ArrayBuffer(10);

      // Create request with file data
      const req = new Request("https://example.com/api/process/stream", {
        method: "POST",
        headers: {
          "Content-Type": "application/octet-stream",
          "x-file-name": "test-file.pdf",
          "x-file-id": "test-file-id",
          "x-target": "test-target",
          "x-processor": "openparse",
          "x-processor-config": JSON.stringify({
            semantic_chunking: true,
            embeddings_provider: "cloudflare"
          }),
          "x-vectorize-config": JSON.stringify({
            accountId: "test-account",
            apiToken: "test-token",
            ragName: "test-rag",
            ragId: "test-rag-id",
            whitelabelId: "test-whitelabel",
            auth0Token: "test-auth0-token"
          })
        },
        body: fileBuffer
      });

      // Process request
      const res = await app.fetch(req, env);

      // Verify response status
      expect(res.status).toBe(200);

      // Check the response body as text first
      const text = await res.text();

      // Try to parse as JSON
      try {
        const data = JSON.parse(text);
        expect(data).toEqual({
          id: "test-workflow-id",
          success: true,
          objectKey: expect.stringContaining("test-target/test-file-id/")
        });
      }catch(e) {
        // If it's not valid JSON, fail the test
        console.error("Failed to parse response as JSON:", text);
        throw new Error("Response is not valid JSON: " + text);
      }

      // Verify storage client was created
      expect(createStorageClient).toHaveBeenCalledWith(expect.anything());

      // Verify workflow creation
      expect(env.CHUNKS_VECTORIZED.create).toHaveBeenCalled();

      // Get the actual call arguments
      const createCall = env.CHUNKS_VECTORIZED.create.mock.calls[0][0];

      // Verify the ID format
      expect(createCall.id).toMatch(/^wf-test-target-/);

      // Verify the files array
      expect(createCall.params.files).toHaveLength(1);
      expect(createCall.params.files[0].fileId).toBe("test-file-id");
      expect(createCall.params.files[0].processor).toBe("openparse");

      // Verify vectorize config
      expect(createCall.params.vectorizeConfig.ragId).toBe("test-rag-id");
    });

    it("should handle missing headers", async ()=>{
      // Mock environment
      const env = {
        ALLOWED_ORIGINS: "*",
        ENVIRONMENT: "production",
        CHUNKS_VECTORIZED: {
          create: vi.fn().mockResolvedValue({ id: "test-workflow-id" })
        },
        R2: {
          put: vi.fn().mockResolvedValue({ key: "test-object-key" })
        },
        VECTORIZE_API_TOKEN: "test-token",
        VECTORIZE_ACCOUNT_ID: "test-account",
        VECTORIZE_INDEX_NAME: "test-index",
        OPENPARSE_API_KEY: "test-key",
        OPENPARSE_API_URL: "https://openparse.example.com",
        UNSTRUCTURED_API_KEY: "test-key",
        UNSTRUCTURED_API_URL: "https://unstructured.example.com"
      };

      // Mock the createStorageClient function
      vi.mocked(createStorageClient).mockReturnValue({
        put: vi.fn().mockResolvedValue({}),
        get: vi.fn().mockResolvedValue({}),
        delete: vi.fn().mockResolvedValue({}),
        list: vi.fn().mockResolvedValue([])
      });

      // Create request with missing headers
      const req = new Request("https://example.com/api/process/stream", {
        method: "POST",
        headers: {
          "Content-Type": "application/octet-stream",
          // Missing required headers
        },
        body: new ArrayBuffer(10)
      });

      // Process request
      const res = await app.fetch(req, env);

      // Verify response is an error (either 400 or 500 is acceptable)
      expect(res.status).toBeGreaterThanOrEqual(400);

      // Check the response body as text first
      const text = await res.text();

      // The error message might vary, but should indicate a problem with the request
      // It could be 'Missing required headers' or a more generic error message
      expect(text).toBeTruthy();

      // Try to parse as JSON if possible
      try {
        const data = JSON.parse(text);
        // The error property should exist, but the exact message might vary
        expect(data).toHaveProperty("error");
      }catch(e) {
        // If it's not valid JSON, that's okay as long as the text contains some error message
        console.log("Response is not valid JSON, but contains error message");
      }
    });
  });

  describe("POST /api/logs/chunks-workflow", ()=>{
    it("should store workflow logs", async ()=>{
      // Mock environment
      const env = {
        ALLOWED_ORIGINS: "*",
        LOGS_DB: {
          prepare: vi.fn().mockReturnValue({
            bind: vi.fn().mockReturnThis(),
            run: vi.fn().mockResolvedValue({})
          })
        }
      };

      // Create request with log data
      const req = new Request("https://example.com/api/logs/chunks-workflow", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify([
          {
            scriptName: "chunks-vectorized",
            outcome: "success",
            eventTimestamp: new Date().toISOString(),
            logs: ["Log message 1", "Log message 2"],
            exceptions: []
          }
        ])
      });

      // Process request
      const res = await app.fetch(req, env);
      const data = await res.json();

      // Verify response
      expect(res.status).toBe(200);
      expect(data).toEqual({
        success: true
      });

      // Verify logs were stored
      expect(env.LOGS_DB.prepare).toHaveBeenCalled();
    });
  });
});
