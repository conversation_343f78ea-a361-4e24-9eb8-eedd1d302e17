import { describe, it, expect } from "vitest";
import { RagVectorTextChunksStatus } from "../src/types";

describe("types", ()=>{
  describe("RagVectorTextChunksStatus", ()=>{
    it("should have the correct enum values", ()=>{
      expect(RagVectorTextChunksStatus.CHUNKING).toBe("file to chunks");
      expect(RagVectorTextChunksStatus.FAILED_TO_CHUNK).toBe("failed to chunk");
      expect(RagVectorTextChunksStatus.EDITING).toBe("editing chunks");
      expect(RagVectorTextChunksStatus.STORING).toBe("chunks to storage");
      expect(RagVectorTextChunksStatus.VECTORIZING).toBe("chunks to vectors");
      expect(RagVectorTextChunksStatus.FAILED_TO_VECTORIZE).toBe("failed to vectorize");
      expect(RagVectorTextChunksStatus.COMPLETED).toBe("completed");
    });
  });
});
