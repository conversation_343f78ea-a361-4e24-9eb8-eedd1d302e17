interface RelevanceResponse {
  is_relevant: boolean,
  confidence: number,
  reasoning: string,
}

export async function evaluateRelevance(model: any, chunk: { text: string }): Promise<number>{
  const prompt = {
    messages: [
      {
        role: "system",
        content: "You are an AI trained to evaluate text chunks for relevance and information value. Analyze the content and determine if it contains meaningful information."
      },
      {
        role: "user",
        content: chunk.text
      }
    ],
    function_call: {
      name: "evaluate_chunk_relevance",
      arguments: JSON.stringify({
        text: chunk.text
      })
    },
    functions: [
      {
        name: "evaluate_chunk_relevance",
        description: "Evaluate if a text chunk contains meaningful information",
        parameters: {
          type: "object",
          properties: {
            is_relevant: {
              type: "boolean",
              description: "Whether the chunk contains meaningful information"
            },
            confidence: {
              type: "number",
              description: "Confidence score between 0 and 1"
            },
            reasoning: {
              type: "string",
              description: "Brief explanation of the evaluation"
            }
          },
          required: ["is_relevant", "confidence", "reasoning"]
        }
      }
    ]
  };

  const response = await model.run(prompt);
  const result = JSON.parse(response.function_call.arguments) as RelevanceResponse;
  return result.confidence;
}
