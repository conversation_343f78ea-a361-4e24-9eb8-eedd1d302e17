import { NonRetryableError } from "cloudflare:workflows";


export async function ensureChunksTable(
  whitelabelId: string,
  db: D1Database
): Promise<void>{
  if(!db) {
    throw new NonRetryableError("D1 Database instance is required");
  }

  const tableName = `d1_chunks_${whitelabelId.replace(/-/g, "_")}`;

  try {
    // Create table if it doesn't exist
    await db.prepare(`
      CREATE TABLE IF NOT EXISTS ${tableName} (
        id TEXT PRIMARY KEY,
        text TEXT NOT NULL,
        metadata TEXT
      )
    `).run();

    // Add index for better query performance
    await db.prepare(`
      CREATE INDEX IF NOT EXISTS idx_${tableName}_metadata 
      ON ${tableName}((json_extract(metadata, '$.fileObjectKey')))
    `).run();

    console.log(`✅ Successfully ensured table ${tableName} exists.`);
  }catch(error) {
    console.error(`❌ Error ensuring table ${tableName}:`, error);
    throw new NonRetryableError(`❌ Failed to ensure table exists: ${error instanceof Error ? error.message : String(error)}`);
  }
}
