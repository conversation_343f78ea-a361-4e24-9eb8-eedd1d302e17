// /**
//  * Storage client implementation for handling file storage operations
//  * with support for both R2 and MinIO S3 interfaces.
//  */

// import { Env } from "../types";
// import { generateAuthorizationHeader } from "./aws-sig-v4";

// // Create a global memory storage that is compatible with Cloudflare Workers
// // @ts-ignore - Extend the global object with our memory storage
// if (typeof globalThis.memoryStorage === 'undefined') {
//   // @ts-ignore - Add memoryStorage to globalThis
//   globalThis.memoryStorage = new Map<string, {
//     content: any;
//     metadata?: { contentType?: string; [key: string]: any };
//     timestamp: number;
//   }>();
// }

// /**
//  * Interface for a unified storage client that works with both R2 and MinIO
//  */
// export interface StorageClient {
//   get(key: string): Promise<R2ObjectBody | null>,
//   put(key: string, value: ReadableStream | ArrayBuffer | ArrayBufferView | string | null | Blob, options?: R2PutOptions): Promise<R2Object>,
//   delete(key: string): Promise<void>,
//   list(options?: R2ListOptions): Promise<R2Objects>,
//   presignedPutUrl?(key: string, expires?: number): Promise<string>, // Optional method to get presigned URLs
// }

// /**
//  * Creates an appropriate storage client based on the environment.
//  * Uses MinIO client for local/dev environments and R2 client for production.
//  */
// export function createStorageClient(env: Env): StorageClient{
//   const isLocalEnvironment = env.ENVIRONMENT === "local" || env.ENVIRONMENT === "development";

//   if(isLocalEnvironment) {
//     console.log(`🔄 [STORAGE] Using MinIO S3 client for local environment: ${env.ENVIRONMENT}`);
//     console.log(`🔄 [STORAGE] Storage URL: ${env.R2_BUCKET_URL}`);
//     return new MinioStorageClient(env);
//   } else {
//     console.log(`🔄 [STORAGE] Using Cloudflare R2 client for environment: ${env.ENVIRONMENT}`);
//     console.log(`🔄 [STORAGE] Storage URL: ${env.R2_BUCKET_URL}`);
//     return new R2StorageClient(env);
//   }
// }

// /**
//  * R2 storage client implementation
//  */
// class R2StorageClient implements StorageClient {
//   constructor(private env: Env){}

//   async get(key: string): Promise<R2ObjectBody | null>{
//     return this.env.R2.get(key);
//   }

//   async put(key: string, value: ReadableStream | ArrayBuffer | ArrayBufferView | string | null | Blob, options?: R2PutOptions): Promise<R2Object>{
//     return this.env.R2.put(key, value, options);
//   }

//   async delete(key: string): Promise<void>{
//     await this.env.R2.delete(key);
//   }

//   async list(options?: R2ListOptions): Promise<R2Objects>{
//     return this.env.R2.list(options);
//   }
// }

// /**
//  * MinIO S3 storage client implementation using pure fetch with AWS SigV4.
//  * Designed for reliability in Cloudflare Workers with Docker networking.
//  */
// class MinioStorageClient implements StorageClient {
//   private baseUrl: string;
//   private bucketName: string = "rag-origin-files-local";
//   private alternateBucketName: string = "rag-files-local"; // Add alternate bucket name
//   private accessKey: string;
//   private secretKey: string;
//   private maxRetries: number = 5;
//   private retryDelayMs: number = 1000;

//   constructor(private env: Env) {
//     // Use the R2_BUCKET_URL from environment if available, or DNS name
//     this.baseUrl = this.env.R2_BUCKET_URL || "http://minio.divinci.local:9000";

//     console.log(`🔄 [STORAGE] Using MinIO endpoint: ${this.baseUrl}`);

//     this.accessKey = this.env.R2_ACCESS_KEY_ID || "minioadmin";
//     this.secretKey = this.env.R2_SECRET_ACCESS_KEY || "minioadmin";

//     console.log(`🔄 [STORAGE] Using credentials:`, {
//       accessKey: this.accessKey,
//       secretKey: this.secretKey ? "Set [hidden]" : "Missing"
//     });

//     // Check bucket on startup
//     this.checkOrCreateBucket()
//       .then(exists => console.log(`🔄 [STORAGE] Bucket check: ${exists ? "exists" : "created"}`))
//       .catch(err => console.warn(`⚠️ [STORAGE] Bucket check failed: ${err.message}`));

//     // Connectivity test commented out since we know it's working
//     // this.testConnectivity().catch(err =>
//     //   console.warn(`⚠️ [STORAGE] Connectivity test failed: ${err.message}`)
//     // );
//   }

//   /**
//    * Tests connectivity by creating and retrieving a small test file
//    */
//   private async testConnectivity(): Promise<void> {
//     const testKey = `connectivity-test/test-${Date.now()}.txt`;
//     const testContent = `Test file created at ${new Date().toISOString()}`;

//     try {
//       console.log(`🔄 [STORAGE] Running connectivity test...`);

//       // Try to put a test file
//       await this.put(testKey, testContent, {
//         httpMetadata: { contentType: 'text/plain' },
//         customMetadata: {
//           purpose: 'connectivity-test',
//           timestamp: Date.now().toString()
//         }
//       });

//       console.log(`✅ [STORAGE] Test file created successfully: ${testKey}`);

//       // Try to retrieve the test file
//       const retrieved = await this.get(testKey);

//       if (retrieved) {
//         const content = await retrieved.text();
//         console.log(`✅ [STORAGE] Retrieved test file: ${content.substring(0, 30)}...`);

//         // Delete the test file to clean up
//         await this.delete(testKey);
//         console.log(`✅ [STORAGE] Connectivity test completed successfully`);
//       } else {
//         console.warn(`⚠️ [STORAGE] Could not retrieve test file`);
//       }
//     } catch (error) {
//       console.error(`❌ [STORAGE] Connectivity test failed: ${error.message}`);
//       throw error;
//     }
//   }

//   /**
//    * Checks if bucket exists, creates it if not
//    * @returns {Promise<boolean>} true if bucket exists, false if created
//    */
//   private async checkOrCreateBucket(): Promise<boolean> {
//     try {
//       console.log(`🔄 [STORAGE] Checking bucket: ${this.bucketName}`);
//       // Try to check if bucket exists
//       const headUrl = new URL(`${this.baseUrl}/${this.bucketName}`);

//       // Generate auth headers
//       const headHeaders = await generateAuthorizationHeader(
//         "HEAD",
//         headUrl,
//         this.accessKey,
//         this.secretKey,
//         null,
//         ""
//       );

//       // Make the HEAD request
//       const response = await fetch(headUrl.toString(), {
//         method: 'HEAD',
//         headers: headHeaders,
//         signal: AbortSignal.timeout(5000) // 5 second timeout
//       });

//       if (response.ok) {
//         console.log(`✅ [STORAGE] Bucket exists: ${this.bucketName}`);
//         return true;
//       }

//       if (response.status === 404) {
//         console.log(`🔄 [STORAGE] Bucket doesn't exist: ${this.bucketName}, creating...`);
//         return await this.createBucket();
//       }

//       console.warn(`⚠️ [STORAGE] Unexpected response checking bucket: ${response.status}`);
//       return false;
//     } catch (error) {
//       console.warn(`⚠️ [STORAGE] Error checking bucket: ${error.message}`);
//       // Try to create the bucket anyway
//       try {
//         return await this.createBucket();
//       } catch (createError) {
//         console.error(`❌ [STORAGE] Failed to create bucket: ${createError.message}`);
//         return false;
//       }
//     }
//   }

//   /**
//    * Creates the bucket if it doesn't exist
//    */
//   private async createBucket(): Promise<boolean> {
//     try {
//       console.log(`🔄 [STORAGE] Creating bucket: ${this.bucketName}`);

//       // Create URL for bucket creation
//       const putUrl = new URL(`${this.baseUrl}/${this.bucketName}`);

//       // Generate authorization headers
//       const putHeaders = await generateAuthorizationHeader(
//         "PUT",
//         putUrl,
//         this.accessKey,
//         this.secretKey,
//         null,
//         ""
//       );

//       // Make the bucket creation request
//       const response = await fetch(putUrl.toString(), {
//         method: 'PUT',
//         headers: putHeaders,
//         signal: AbortSignal.timeout(5000) // 5 second timeout
//       });

//       if (response.ok) {
//         console.log(`✅ [STORAGE] Successfully created bucket: ${this.bucketName}`);
//         return true;
//       } else if (response.status === 409) {
//         // 409 Conflict means the bucket already exists, which is okay
//         console.log(`🔄 [STORAGE] Bucket already exists (409 Conflict)`);
//         return true;
//       } else {
//         console.warn(`⚠️ [STORAGE] Failed to create bucket: ${response.status}`);
//         return false;
//       }
//     } catch (error) {
//       console.error(`❌ [STORAGE] Error creating bucket: ${error.message}`);
//       return false;
//     }
//   }

//   /**
//    * Helper method to implement exponential backoff for retry operations
//    */
//   private async withRetry<T>(
//     operation: string,
//     fn: () => Promise<T>,
//     skipRetryForErrors: string[] = []
//   ): Promise<T> {
//     let lastError: Error | null = null;
//     let attemptCount = 0;

//     while (attemptCount < this.maxRetries) {
//       attemptCount++;

//       try {
//         console.log(`🔄 [STORAGE] Attempt ${attemptCount}/${this.maxRetries}: ${operation}`);
//         const result = await fn();
//         console.log(`✅ [STORAGE] Operation successful: ${operation}`);
//         return result;
//       } catch (error: any) {
//         lastError = error;
//         const errorMsg = error?.message || String(error);
//         console.log(`⚠️ [STORAGE] Attempt ${attemptCount} failed: ${errorMsg}`);

//         // Check if we should skip retry for this type of error
//         const shouldSkipRetry = skipRetryForErrors.some(errText =>
//           errorMsg.toLowerCase().includes(errText.toLowerCase())
//         );

//         if (shouldSkipRetry) {
//           console.log(`⚠️ [STORAGE] Skipping retry for known error: ${errorMsg}`);
//           break;
//         }

//         if (attemptCount < this.maxRetries) {
//           const delayMs = this.retryDelayMs * Math.pow(2, attemptCount - 1); // Exponential backoff
//           console.log(`🔄 [STORAGE] Waiting ${delayMs}ms before retry...`);
//           await new Promise(resolve => setTimeout(resolve, delayMs));
//         }
//       }
//     }

//     throw lastError || new Error(`All ${this.maxRetries} attempts for ${operation} failed`);
//   }

//   async get(key: string): Promise<R2ObjectBody | null> {
//     try {
//       // Extract the filename for logging
//       const filename = key.split("/").pop() || key;
//       console.log(`🔄 [STORAGE] Getting object from storage: ${key}`);

//       // Try with the primary bucket first, then try alternate bucket if needed
//       let headResponse = null;
//       let usedBucketName = this.bucketName;
//       let encodedPath = '';

//       // First try with the primary bucket
//       try {
//         // Encode the key for S3
//         const pathSegments = key.split("/");
//         encodedPath = `${this.bucketName}/${pathSegments.map(segment => encodeURIComponent(segment)).join("/")}`;

//         // Create the URL for a HEAD request
//         const headUrl = new URL(`${this.baseUrl}/${encodedPath}`);

//         // Generate auth headers
//         const headHeaders = await generateAuthorizationHeader(
//           "HEAD",
//           headUrl,
//           this.accessKey,
//           this.secretKey,
//           null,
//           ""
//         );

//         // Make the HEAD request
//         headResponse = await this.withRetry(
//           `HEAD check for ${key}`,
//           async () => {
//             console.log(`🔄 [STORAGE] Checking if file exists: ${headUrl}`);
//             const response = await fetch(headUrl.toString(), {
//               method: 'HEAD',
//               headers: headHeaders,
//               signal: AbortSignal.timeout(5000) // 5 second timeout
//             });

//             if (response.status === 404) {
//               return null; // Object doesn't exist
//             }

//             if (!response.ok) {
//               const errText = `HTTP error: ${response.status}`;
//               throw new Error(errText);
//             }

//             return response;
//           },
//           ['not found', '404'] // Skip retry for not found errors
//         );
//       } catch (error) {
//         console.log(`⚠️ [STORAGE] Error checking primary bucket: ${error.message}`);
//       }

//       // If not found in primary bucket, try alternate bucket
//       if (!headResponse) {
//         console.log(`🔄 [STORAGE] File not found in primary bucket, trying alternate bucket...`);

//         try {
//           // Encode the key for S3
//           const pathSegments = key.split("/");
//           encodedPath = `${this.alternateBucketName}/${pathSegments.map(segment => encodeURIComponent(segment)).join("/")}`;

//           // Create the URL for a HEAD request
//           const headUrl = new URL(`${this.baseUrl}/${encodedPath}`);

//           // Generate auth headers
//           const headHeaders = await generateAuthorizationHeader(
//             "HEAD",
//             headUrl,
//             this.accessKey,
//             this.secretKey,
//             null,
//             ""
//           );

//           // Make the HEAD request
//           headResponse = await this.withRetry(
//             `HEAD check for ${key} in ${this.alternateBucketName}`,
//             async () => {
//               console.log(`🔄 [STORAGE] Checking if file exists in alternate bucket: ${headUrl}`);
//               const response = await fetch(headUrl.toString(), {
//                 method: 'HEAD',
//                 headers: headHeaders,
//                 signal: AbortSignal.timeout(5000) // 5 second timeout
//               });

//               if (response.status === 404) {
//                 return null; // Object doesn't exist
//               }

//               if (!response.ok) {
//                 const errText = `HTTP error: ${response.status}`;
//                 throw new Error(errText);
//               }

//               return response;
//             },
//             ['not found', '404'] // Skip retry for not found errors
//           );

//           if (headResponse) {
//             console.log(`✅ [STORAGE] File found in alternate bucket: ${this.alternateBucketName}`);
//             usedBucketName = this.alternateBucketName;
//           }
//         } catch (error) {
//           console.log(`⚠️ [STORAGE] Error checking alternate bucket ${this.alternateBucketName}: ${error.message}`);
//         }
//       }

//       if (!headResponse) {
//         console.log(`🔄 [STORAGE] File not found in any bucket: ${filename}`);
//         return null;
//       }

//         // Get the metadata from the HEAD response
//         const contentType = headResponse.headers.get('Content-Type') || 'application/octet-stream';
//         const contentLength = parseInt(headResponse.headers.get('Content-Length') || '0', 10);
//         const etag = headResponse.headers.get('ETag') || '';

//         console.log(`🔄 [STORAGE] File exists, fetching content: ${filename}, size: ${contentLength}`);

//         // Create the URL for a GET request
//         const getUrl = new URL(`${this.baseUrl}/${encodedPath}`);

//         // Generate auth headers for GET
//         const getHeaders = await generateAuthorizationHeader(
//           "GET",
//           getUrl,
//           this.accessKey,
//           this.secretKey,
//           null,
//           ""
//         );

//         // Make the GET request
//         const getResponse = await this.withRetry(
//           `GET object ${key}`,
//           async () => {
//             console.log(`🔄 [STORAGE] Fetching file content: ${getUrl}`);
//             const response = await fetch(getUrl.toString(), {
//               method: 'GET',
//               headers: getHeaders,
//               signal: AbortSignal.timeout(10000) // 10 second timeout
//             });

//             if (!response.ok) {
//               const errText = `HTTP error: ${response.status}`;
//               throw new Error(errText);
//             }

//             return response;
//           }
//         );

//         // Get the data as ArrayBuffer
//         const arrayBuffer = await getResponse.arrayBuffer();
//         const blob = new Blob([arrayBuffer], { type: contentType });

//         console.log(`✅ [STORAGE] Successfully fetched file: ${filename}, size: ${arrayBuffer.byteLength} bytes`);

//         // Create an R2ObjectBody-like object
//         const customR2Object = {
//           body: new ReadableStream({
//             start(controller) {
//               controller.enqueue(new Uint8Array(arrayBuffer));
//               controller.close();
//             }
//           }),
//           bodyUsed: false,
//           arrayBuffer: async () => arrayBuffer,
//           json: async () => JSON.parse(await blob.text()),
//           text: async () => await blob.text(),
//           blob: async () => blob,
//           size: arrayBuffer.byteLength,
//           etag: etag,
//           httpMetadata: {
//             contentType,
//             contentLanguage: headResponse.headers.get('Content-Language'),
//             contentDisposition: headResponse.headers.get('Content-Disposition'),
//             contentEncoding: headResponse.headers.get('Content-Encoding'),
//             cacheControl: headResponse.headers.get('Cache-Control'),
//             cacheExpiry: undefined
//           },
//           customMetadata: {
//             fileName: filename
//           },
//           key: key,
//           version: "",
//           writeHttpMetadata: () => ({}),
//           checksums: {
//             md5: undefined,
//             sha1: undefined,
//             sha256: undefined,
//             sha384: undefined,
//             sha512: undefined,
//             toJSON: () => ({})
//           }
//         };

//         // Add missing properties required by R2ObjectBody
//         const fullR2Object = {
//           ...customR2Object,
//           httpEtag: etag,
//           uploaded: new Date(headResponse.headers.get('Last-Modified') || new Date().toISOString()),
//           storageClass: "standard"
//         };

//         return fullR2Object as R2ObjectBody;
//       } catch (error: any) {
//         if (error?.message?.includes('not found') || error?.message?.includes('404')) {
//           console.log(`🔄 [STORAGE] File not found: ${filename}`);
//           return null;
//         }
//         throw error;
//       }
//     } catch (error: any) {
//       console.error(`❌ [STORAGE] Error getting object: ${error?.message || String(error)}`);
//       return null;
//     }
//   }

//   /**
//    * Direct PUT implementation using fetch with AWS Signature v4
//    */
//   put = async (key: string, value: ReadableStream | ArrayBuffer | ArrayBufferView | string | null | Blob, options?: R2PutOptions): Promise<R2Object> => {
//     try {
//       // Extract the filename for logging
//       const filename = key.split("/").pop() || key;
//       console.log(`🔄 [STORAGE] Putting object to storage: ${key}`);
//       console.log(`🔄 [STORAGE] Value type: ${value ? (value instanceof Blob ? "Blob" : typeof value) : "null"}`);

//       // Ensure bucket exists first
//       await this.checkOrCreateBucket();

//       // Convert value to ArrayBuffer for upload
//       let data: ArrayBuffer;
//       let contentType = options?.httpMetadata?.contentType || 'application/octet-stream';

//       // Convert the value to an ArrayBuffer based on its type
//       if (value instanceof Blob) {
//         console.log(`🔄 [STORAGE] Processing Blob, size: ${value.size}`);
//         data = await value.arrayBuffer();
//         if (value.type && contentType === 'application/octet-stream') {
//           contentType = value.type;
//         }
//       } else if (value instanceof ReadableStream) {
//         console.log(`🔄 [STORAGE] Processing ReadableStream`);
//         const reader = value.getReader();
//         const chunks: Uint8Array[] = [];

//         while (true) {
//           const { done, value: chunk } = await reader.read();
//           if (done) break;
//           if (chunk) {
//             chunks.push(chunk);
//           }
//         }

//         // Combine all chunks into a single ArrayBuffer
//         if (chunks.length > 0) {
//           const totalLength = chunks.reduce((len, chunk) => len + chunk.length, 0);
//           const result = new Uint8Array(totalLength);
//           let offset = 0;
//           for (const chunk of chunks) {
//             result.set(chunk, offset);
//             offset += chunk.length;
//           }
//           data = result.buffer;
//         } else {
//           data = new ArrayBuffer(0);
//         }
//       } else if (value instanceof ArrayBuffer) {
//         data = value;
//       } else if (ArrayBuffer.isView(value)) {
//         data = value.buffer;
//       } else if (typeof value === 'string') {
//         const encoder = new TextEncoder();
//         data = encoder.encode(value).buffer;
//         if (contentType === 'application/octet-stream') {
//           contentType = 'text/plain';
//         }
//       } else if (value === null) {
//         data = new ArrayBuffer(0);
//       } else {
//         throw new Error(`Unsupported value type: ${typeof value}`);
//       }

//       // Track the content length
//       const contentLength = data.byteLength;
//       console.log(`🔄 [STORAGE] Uploading file: ${key}, size: ${contentLength}, type: ${contentType}`);

//       // Encode the key properly for S3
//       const pathSegments = key.split("/");
//       const encodedPath = `${this.bucketName}/${pathSegments.map(segment => encodeURIComponent(segment)).join("/")}`;

//       // Create the URL for the PUT request
//       const putUrl = new URL(`${this.baseUrl}/${encodedPath}`);

//       // Generate AWS Signature v4 headers
//       const authHeaders = await generateAuthorizationHeader(
//         "PUT",
//         putUrl,
//         this.accessKey,
//         this.secretKey,
//         data,
//         contentType
//       );

//       // Merge with other required headers
//       const headers = {
//         ...authHeaders,
//         "Content-Type": contentType,
//         "Content-Length": String(contentLength),
//         "x-amz-acl": "public-read" // Make object publicly readable
//       };

//       // Add custom metadata headers
//       if (options?.customMetadata) {
//         for (const [k, v] of Object.entries(options.customMetadata)) {
//           headers[`x-amz-meta-${k}`] = String(v);
//         }
//       }

//       // Add HTTP metadata headers
//       if (options?.httpMetadata) {
//         const httpMeta = options.httpMetadata;
//         if (httpMeta.contentLanguage) headers["Content-Language"] = httpMeta.contentLanguage;
//         if (httpMeta.contentDisposition) headers["Content-Disposition"] = httpMeta.contentDisposition;
//         if (httpMeta.contentEncoding) headers["Content-Encoding"] = httpMeta.contentEncoding;
//         if (httpMeta.cacheControl) headers["Cache-Control"] = httpMeta.cacheControl;
//       }

//       // Upload with retry
//       let etag = '';

//       try {
//         // Use the withRetry helper for reliable uploads
//         const response = await this.withRetry(
//           `Upload ${key}`,
//           async () => {
//             // Log detailed upload info
//             console.log(`🔄 [STORAGE] Uploading to: ${putUrl.toString()}`);

//             // Use the fetch API to make the PUT request
//             const response = await fetch(putUrl.toString(), {
//               method: 'PUT',
//               headers: headers,
//               body: new Uint8Array(data),
//               signal: AbortSignal.timeout(15000) // 15 second timeout for larger files
//             });

//             console.log(`🔄 [STORAGE] Upload response: ${response.status}`);

//             if (!response.ok) {
//               const errText = await response.text().catch(() => `Status: ${response.status}`);
//               throw new Error(`S3 PUT failed: ${errText.substring(0, 200)}`);
//             }

//             return response;
//           }
//         );

//         // Extract the ETag from the response
//         etag = response.headers.get('ETag') || Date.now().toString();
//         console.log(`✅ [STORAGE] Upload successful: ${key}, ETag: ${etag}`);
//       } catch (uploadError: any) {
//         console.error(`❌ [STORAGE] Upload failed: ${uploadError?.message || String(uploadError)}`);
//         throw new Error(`Failed to upload file: ${uploadError?.message || String(uploadError)}`);
//       }

//       // Create an R2Object to return
//       const customR2Object = {
//         key,
//         version: "",
//         size: contentLength,
//         etag: etag,
//         httpEtag: etag,
//         uploaded: new Date(),
//         httpMetadata: {
//           contentType
//         },
//         customMetadata: options?.customMetadata || {},
//         checksums: {
//           md5: undefined,
//           sha1: undefined,
//           sha256: undefined,
//           sha384: undefined,
//           sha512: undefined
//         }
//       };

//       return customR2Object as R2Object;
//     } catch (error: any) {
//       console.error(`❌ [STORAGE] Error putting object: ${error?.message || String(error)}`);
//       throw error;
//     }
//   }

//   delete = async (key: string): Promise<void> => {
//     try {
//       const filename = key.split("/").pop() || key;
//       console.log(`🔄 [STORAGE] Deleting object from storage: ${key}`);

//       // Encode the key for S3
//       const pathSegments = key.split("/");
//       const encodedPath = `${this.bucketName}/${pathSegments.map(segment => encodeURIComponent(segment)).join("/")}`;

//       // Check if the object exists first
//       try {
//         // Create the URL for a HEAD request
//         const headUrl = new URL(`${this.baseUrl}/${encodedPath}`);
//         const headHeaders = await generateAuthorizationHeader(
//           "HEAD",
//           headUrl,
//           this.accessKey,
//           this.secretKey,
//           null,
//           ""
//         );

//         const headResponse = await fetch(headUrl.toString(), {
//           method: 'HEAD',
//           headers: headHeaders,
//           signal: AbortSignal.timeout(3000) // 3 second timeout
//         });

//         if (headResponse.status === 404) {
//           console.log(`🔄 [STORAGE] File not found, nothing to delete: ${key}`);
//           return;
//         }

//         // Delete the object
//         const deleteUrl = new URL(`${this.baseUrl}/${encodedPath}`);
//         const deleteHeaders = await generateAuthorizationHeader(
//           "DELETE",
//           deleteUrl,
//           this.accessKey,
//           this.secretKey,
//           null,
//           ""
//         );

//         // Use retry for DELETE operation
//         await this.withRetry(
//           `Delete ${key}`,
//           async () => {
//             console.log(`🔄 [STORAGE] Sending DELETE request: ${deleteUrl}`);
//             const response = await fetch(deleteUrl.toString(), {
//               method: 'DELETE',
//               headers: deleteHeaders,
//               signal: AbortSignal.timeout(5000) // 5 second timeout
//             });

//             console.log(`🔄 [STORAGE] Delete response: ${response.status}`);

//             if (!response.ok && response.status !== 404) {
//               throw new Error(`Delete failed with status: ${response.status}`);
//             }

//             return response;
//           },
//           ['not found', '404'] // Skip retry for not found errors
//         );

//         console.log(`✅ [STORAGE] Successfully deleted file: ${key}`);
//       } catch (error: any) {
//         // For delete operations, we log but don't throw since deletes are not critical
//         console.warn(`⚠️ [STORAGE] Error checking/deleting file: ${error?.message || String(error)}`);
//       }
//     } catch (error: any) {
//       console.warn(`⚠️ [STORAGE] Error deleting file: ${error?.message || String(error)}`);
//       // We don't re-throw here as deletes are not critical
//     }
//   }

//   list = async (options?: R2ListOptions): Promise<R2Objects> => {
//     try {
//       console.log(`🔄 [STORAGE] Listing objects from bucket: ${this.bucketName}`);

//       // Create URL for list operation
//       const listUrl = new URL(`${this.baseUrl}/${this.bucketName}`);

//       // Add query parameters for listing
//       listUrl.searchParams.append('list-type', '2');
//       if (options?.prefix) {
//         listUrl.searchParams.append('prefix', options.prefix);
//       }
//       if (options?.delimiter) {
//         listUrl.searchParams.append('delimiter', options.delimiter);
//       }
//       if (options?.cursor) {
//         listUrl.searchParams.append('continuation-token', options.cursor);
//       }
//       if (options?.limit) {
//         listUrl.searchParams.append('max-keys', String(options.limit));
//       }

//       // Generate auth headers
//       const listHeaders = await generateAuthorizationHeader(
//         "GET",
//         listUrl,
//         this.accessKey,
//         this.secretKey,
//         null,
//         ""
//       );

//       try {
//         // Make the request with retry
//         const response = await this.withRetry(
//           `List objects in ${this.bucketName}`,
//           async () => {
//             console.log(`🔄 [STORAGE] Listing objects: ${listUrl}`);
//             const response = await fetch(listUrl.toString(), {
//               method: 'GET',
//               headers: listHeaders,
//               signal: AbortSignal.timeout(5000) // 5 second timeout
//             });

//             console.log(`🔄 [STORAGE] List response: ${response.status}`);

//             if (response.status === 404) {
//               console.log(`🔄 [STORAGE] Bucket ${this.bucketName} not found`);
//               return null;
//             }

//             if (!response.ok) {
//               throw new Error(`List failed with status: ${response.status}`);
//             }

//             return response;
//           },
//           ['not found', '404'] // Skip retry for not found errors
//         );

//         if (!response) {
//           // Bucket doesn't exist
//           return {
//             objects: [],
//             truncated: false,
//             delimitedPrefixes: []
//           } as R2Objects;
//         }

//         // Parse the XML response
//         const responseText = await response.text();
//         const objects: R2Object[] = [];
//         const delimitedPrefixes: string[] = [];

//         // Extract common prefixes (folders) if any
//         const commonPrefixRegex = /<CommonPrefix><Prefix>([^<]+)<\/Prefix><\/CommonPrefix>/g;
//         let commonPrefixMatch: RegExpExecArray | null;
//         while ((commonPrefixMatch = commonPrefixRegex.exec(responseText)) !== null) {
//           delimitedPrefixes.push(commonPrefixMatch[1]);
//         }

//         // Extract objects
//         const keyRegex = /<Contents><Key>([^<]+)<\/Key>.*?<Size>([^<]+)<\/Size>.*?<LastModified>([^<]+)<\/LastModified>.*?<ETag>([^<]+)<\/ETag>/g;
//         let match: RegExpExecArray | null;
//         while ((match = keyRegex.exec(responseText)) !== null) {
//           const key = match[1];
//           const size = parseInt(match[2] || '0', 10);
//           const lastModified = new Date(match[3]);
//           const etag = match[4].replace(/&quot;/g, '').replace(/"/g, '');

//           objects.push({
//             key,
//             version: '',
//             size,
//             etag,
//             httpEtag: etag,
//             uploaded: lastModified,
//             httpMetadata: {
//               contentType: 'application/octet-stream' // We don't get this in list response
//             },
//             customMetadata: {},
//             checksums: {
//               md5: undefined,
//               sha1: undefined,
//               sha256: undefined,
//               sha384: undefined,
//               sha512: undefined
//             }
//           } as R2Object);
//         }

//         // Check if response is truncated
//         let truncated = false;
//         const isTruncatedMatch = /<IsTruncated>([^<]+)<\/IsTruncated>/i.exec(responseText);
//         if (isTruncatedMatch && isTruncatedMatch[1].toLowerCase() === 'true') {
//           truncated = true;
//         }

//         console.log(`✅ [STORAGE] Found ${objects.length} objects and ${delimitedPrefixes.length} prefixes in bucket: ${this.bucketName}`);

//         return {
//           objects,
//           truncated,
//           delimitedPrefixes
//         } as R2Objects;
//       } catch (error: any) {
//         if (error?.message?.includes('not found') || error?.message?.includes('404')) {
//           console.log(`🔄 [STORAGE] Bucket not found: ${this.bucketName}`);
//           // Return empty list for not found errors
//           return {
//             objects: [],
//             truncated: false,
//             delimitedPrefixes: []
//           } as R2Objects;
//         }
//         throw error;
//       }
//     } catch (error: any) {
//       console.error(`❌ [STORAGE] Error listing objects: ${error?.message || String(error)}`);
//       // Return empty list for all errors
//       return {
//         objects: [],
//         truncated: false,
//         delimitedPrefixes: []
//       } as R2Objects;
//     }
//   }
// }