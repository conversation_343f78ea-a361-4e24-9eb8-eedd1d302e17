export class VectorizeAPI {
  private readonly baseUrl: string;
  private readonly headers: Headers;
  private readonly skipTlsVerify: boolean;
  private readonly maxRetries: number = 3;
  private readonly initialRetryDelay: number = 1000; // 1 second
  private readonly maxRetryDelay: number = 10000; // 10 seconds
  private readonly timeout: number = 30000; // 30 seconds

  constructor(config: {
    accountId: string,
    apiToken: string,
    ragName: string,
  }, opts: {
    skipTlsVerify?: boolean,
    maxRetries?: number,
    timeout?: number,
  } = {}){
    this.baseUrl = `https://api.cloudflare.com/client/v4/accounts/${config.accountId}/vectorize/v2/indexes`;
    this.headers = new Headers({
      "Authorization": `Bearer ${config.apiToken}`,
      "Content-Type": "application/x-ndjson",
    });
    this.skipTlsVerify = opts.skipTlsVerify ?? false;
    if(opts.maxRetries) this.maxRetries = opts.maxRetries;
    if(opts.timeout) this.timeout = opts.timeout;
  }

  private async sleep(ms: number): Promise<void>{
    return new Promise(resolve=>setTimeout(resolve, ms));
  }

  private calculateRetryDelay(attempt: number): number{
    // Exponential backoff with jitter
    const exponentialDelay = Math.min(
      this.maxRetryDelay,
      this.initialRetryDelay * Math.pow(2, attempt - 1)
    );
    // Add random jitter ±20%
    const jitter = exponentialDelay * 0.2 * (Math.random() * 2 - 1);
    return exponentialDelay + jitter;
  }

  private isRetryableError(error: any): boolean{
    // Network errors, timeouts, and certain status codes are retryable
    if(error instanceof TypeError) return true; // Network errors
    if(error.message?.includes("timeout")) return true;
    const retryableStatusCodes = [408, 429, 500, 502, 503, 504];
    return retryableStatusCodes.includes(error.status);
  }

  private headersToObject(headers: Headers): Record<string, string>{
    const result: Record<string, string> = {};
    // Use forEach which is supported in all environments
    headers.forEach((value, key)=>{
      result[key] = value;
    });
    return result;
  }

  private async request(path: string, init?: RequestInit){
    let lastError: Error | null = null;

    for(let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const url = `${this.baseUrl}${path}`;
        console.log(`🔄 Vectorize API Request (attempt ${attempt}/${this.maxRetries}):`, {
          url,
          method: init?.method,
          bodyPreview: init?.body ?
            typeof init.body === "string" ? init.body.slice(0, 200) + "..." :
            JSON.stringify(init.body).slice(0, 200) + "..." :
            null
        });

        const controller = new AbortController();
        const timeoutId = setTimeout(()=>controller.abort(), this.timeout);

        // Use type assertion to handle Cloudflare-specific options
        const fetchOptions = {
          ...init,
          // Use custom headers if provided, otherwise use default headers
          headers: init?.headers || this.headers,
          signal: controller.signal,
          cf: this.skipTlsVerify ? { insecureHTTPParser: true } : undefined
        } as RequestInit;

        const response = await fetch(url, fetchOptions);

        clearTimeout(timeoutId);

        const responseData = await response.text();
        console.log(`📥 Vectorize API Response (attempt ${attempt}/${this.maxRetries}):`, {
          status: response.status,
          statusText: response.statusText,
          headers: this.headersToObject(response.headers),
          body: responseData.slice(0, 200) + "..."
        });

        if(!response.ok) {
          throw new Error(`HTTP ${response.status} ${response.statusText}\nResponse: ${responseData}`);
        }

        return JSON.parse(responseData);
      }catch(error: any) {
        lastError = error;

        if(attempt === this.maxRetries || !this.isRetryableError(error)) {
          throw new Error(`Vectorize API request failed after ${attempt} attempts: ${error.message}`);
        }

        const retryDelay = this.calculateRetryDelay(attempt);
        console.warn(`⚠️ Request failed (attempt ${attempt}/${this.maxRetries}). Retrying in ${retryDelay}ms...`, error);
        await this.sleep(retryDelay);
      }
    }

    throw lastError;
  }

  async createIndex(indexName: string, description: string){
    return this.request("/", {
      method: "POST",
      body: JSON.stringify({
        name: indexName,
        description,
        config: { preset: "@cf/baai/bge-base-en-v1.5" }
      })
    });
  }

  async getIndex(indexName: string){
    return this.request(`/${indexName}`, {
      method: "GET"
    });
  }

  async deleteIndex(indexName: string){
    return this.request(`/${indexName}`, {
      method: "DELETE"
    });
  }

  async upsert(indexName: string, vectors: Array<{
    id: string,
    values: number[],
    metadata?: Record<string, unknown>,
  }>){
    // Split vectors into smaller batches if needed
    const BATCH_SIZE = 10; // Reduce batch size to handle timeouts better
    const batches = [];

    for(let i = 0; i < vectors.length; i += BATCH_SIZE) {
      batches.push(vectors.slice(i, i + BATCH_SIZE));
    }

    const results = [];
    for(let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      console.log(`📤 Upserting vectors batch ${i + 1}/${batches.length}:`, {
        indexName,
        vectorCount: batch.length,
        firstVector: {
          id: batch[0]?.id,
          valueLength: Array.isArray(batch[0]?.values) ? batch[0].values.length : "not an array",
          // Don't log the full metadata, just the keys
          metadataKeys: batch[0]?.metadata ? Object.keys(batch[0]?.metadata) : []
        }
      });

      // Add validation before processing
      if(!batch.every((vector: { id: string, values: number[], metadata?: Record<string, unknown> })=>Array.isArray(vector.values)
      )) {
        throw new Error("Invalid vector format: values must be an array of numbers");
      }

      // Make sure each vector has the required fields
      const validVectors = batch.filter((vector: { id: string, values: number[], metadata?: Record<string, unknown> })=>{
        if(!vector.id || !Array.isArray(vector.values) || vector.values.length === 0) {
          console.warn(`⚠️ Invalid vector skipped:`, {
            hasId: !!vector.id,
            hasValues: Array.isArray(vector.values),
            valuesLength: Array.isArray(vector.values) ? vector.values.length : 0
          });
          return false;
        }
        return true;
      });

      // Only log basic info about the vectors
      if(validVectors.length > 0) {
        console.log(`🔍 Prepared ${validVectors.length} vectors for upserting`);
      }

      // We'll format the vectors directly for NDJSON instead of creating a JSON object with a vectors array

      // Use the correct endpoint and format for the Cloudflare Vectorize API
      // The endpoint should be /{index_name}/upsert according to the documentation
      // For upsert, we need to use application/x-ndjson content type
      const customHeaders = new Headers(this.headers);
      customHeaders.set("Content-Type", "application/x-ndjson");

      // For the Cloudflare Vectorize API, we need to format each vector as a separate JSON object on its own line
      // The format should be a series of JSON objects, one per line, without a wrapping array
      // This is the NDJSON (Newline Delimited JSON) format

      // Check if we have nested arrays in the values and log only once
      const hasNestedArrays = validVectors.length > 0 &&
        Array.isArray(validVectors[0].values) &&
        Array.isArray(validVectors[0].values[0]);

      if(hasNestedArrays) {
        console.log(`🔍 Detected nested arrays in vector values, will flatten them`);
      }

      const ndjsonBody = validVectors.map((vector: { id: string, values: number[], metadata?: Record<string, unknown> })=>{
        // The values should be a flat array of numbers, not a nested array
        // If it's a nested array, flatten it
        const flattenedValues = Array.isArray(vector.values) &&
          Array.isArray(vector.values[0]) ?
          vector.values[0] : vector.values;

        // Create a properly formatted object for each vector
        return JSON.stringify({
          id: vector.id,
          values: flattenedValues,
          metadata: vector.metadata || {}
        });
      }).join("\n");

      // Log a brief sample of the NDJSON body for debugging
      console.log(`🔍 NDJSON format: ${ndjsonBody.split("\n").length} vectors prepared`);

      const result = await this.request(`/${indexName}/upsert`, {
        method: "POST",
        headers: customHeaders,
        body: ndjsonBody
      });

      results.push(result);
    }

    return results;
  }

  async query(
    indexName: string,
    vector: number[],
    topK: number = 5,
    metadata?: Record<string, unknown>
  ){
    return this.request(`/${indexName}/query`, {
      method: "POST",
      body: JSON.stringify({
        vector,
        topK,
        metadata
      })
    });
  }

  async getIndexes(){
    return this.request("/", {
      method: "GET"
    }) as Promise<{
      name: string,
      description: string,
      created: string,
      updated: string,
      vectorDimensions: number,
      metric: string,
    }[]>;
  }

  async findOrCreateIndex(indexName: string, description: string){
    try {
      return await this.getIndex(indexName);
    }catch(error) {
      if(error instanceof Error && error.message.includes("404")) {
        return this.createIndex(indexName, description);
      }
      throw error;
    }
  }

  async getVectors(indexName: string, ids: string[]): Promise<string[]>{
    return this.request(`/${indexName}/vectors`, {
      method: "POST",
      body: JSON.stringify({ ids })
    }) as Promise<string[]>;
  }
}
