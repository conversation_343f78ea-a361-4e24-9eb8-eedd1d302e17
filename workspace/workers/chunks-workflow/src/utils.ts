import { GPTTokens } from "gpt-tokens";


export function countTokens(content: string): number{
  const value = new GPTTokens({ messages: [{ role: "user", content }], model: "gpt-4" });
  return value.usedTokens;
}

export function createVectorId(fileId: string, index: number): string{
  return `${fileId}-chunk_${index}`;
}

export function getTableName(whitelabelId: string): string{
  return `d1_chunks_${whitelabelId}`;
}

export function getRelevanceThreshold(processorConfig?: {
  relevanceThreshold?: number,
}): number{
  const DEFAULT_THRESHOLD = 0.3;
  return processorConfig?.relevanceThreshold ?? DEFAULT_THRESHOLD;
}