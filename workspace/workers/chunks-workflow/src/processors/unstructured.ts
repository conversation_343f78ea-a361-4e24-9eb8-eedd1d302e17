import { DocumentProcessor, ProcessorConfig } from "./types";

interface UnstructuredInitResponse {
  session_id: string,
  total_chunks: number,
}

interface ProcessedChunk {
  text: string,
  metadata: {
    type?: string,
    page_number?: number,
    coordinates?: any,
    [key: string]: any,
  },
}

export interface UnstructuredConfig {
  chunkingStrategy?: "by_title" | "by_similarity" | "by_page",
  maxCharacters?: number,
  splitPdfPage?: boolean,
  splitPdfConcurrencyLevel?: number,
  splitPdfAllowFailed?: boolean,
  minTokens?: number,
  maxTokens?: number,
}

export class UnstructuredProcessor implements DocumentProcessor {
  private workerUrl: string;
  private apiKey: string;
  private readonly useStreaming: boolean;

  /**
   * Constructor that supports multiple signatures for backward compatibility
   * @param apiKeyOrUrl API key or worker URL
   * @param workerUrlOrEnv Worker URL or environment object
   * @param envOrOptions Environment object or options
   * @param options Options object
   */
  constructor(
    apiKeyOrUrl: string,
    workerUrlOrEnv?: string | any,
    _envOrOptions?: any | { useStreaming?: boolean },
    options?: { useStreaming?: boolean }
  ){
    // Handle different constructor signatures
    let finalOptions: { useStreaming?: boolean };

    if(workerUrlOrEnv && typeof workerUrlOrEnv === "string") {
      // First signature: (apiKey, workerUrl, env?, options?)
      this.apiKey = apiKeyOrUrl;
      this.workerUrl = workerUrlOrEnv;
      finalOptions = options || {};
    } else {
      // Second signature: (workerUrl, options?)
      this.workerUrl = apiKeyOrUrl;
      this.apiKey = "";
      finalOptions = (typeof workerUrlOrEnv === 'object' && workerUrlOrEnv !== null)
        ? workerUrlOrEnv as { useStreaming?: boolean }
        : {};
    }

    // Extract options
    this.useStreaming = finalOptions.useStreaming ?? false;
  }

  async initializeSession(
    fileUrlOrConfig: string | UnstructuredConfig,
    configOrFileUrl?: UnstructuredConfig | string
  ): Promise<{ sessionId: string, totalChunks?: number }>{
    try {
      // Handle different parameter orders for backward compatibility
      let fileUrl: string;
      let config: UnstructuredConfig;

      if (typeof fileUrlOrConfig === 'string') {
        // New signature: (fileUrl, config)
        fileUrl = fileUrlOrConfig;
        config = (configOrFileUrl as UnstructuredConfig) || {};
      } else {
        // Old signature: (config, fileUrl)
        config = fileUrlOrConfig;
        fileUrl = configOrFileUrl as string;
      }
      const response = await fetch(`${this.workerUrl}/fileUrl/init`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...(this.apiKey ? { "Authorization": `Bearer ${this.apiKey}` } : {})
        },
        body: JSON.stringify({
          url: fileUrl,
          filename: typeof fileUrl === 'string' ? fileUrl.split("/").pop() || "document" : "document",
          config: {
            chunkingStrategy: config.chunkingStrategy || "by_title",
            maxCharacters: config.maxCharacters || 1024,
            splitPdfPage: config.splitPdfPage ?? true,
            splitPdfConcurrencyLevel: config.splitPdfConcurrencyLevel || 5,
            splitPdfAllowFailed: config.splitPdfAllowFailed ?? true,
            minTokens: config.minTokens,
            maxTokens: config.maxTokens
          }
        })
      });

      if(!response.ok) {
        const errorText = await response.text();
        throw new Error(`Worker initialization failed: ${errorText}`);
      }

      const { session_id, total_chunks } = await response.json() as UnstructuredInitResponse;
      console.log(`✅ Unstructured worker initialized: ${session_id} (${total_chunks} chunks)`);

      // Store the session ID for use in processBatch
      this.sessionId = session_id;
      return { sessionId: session_id, totalChunks: total_chunks };
    }catch(error) {
      console.error("Failed to initialize worker session:", error);
      throw error;
    }
  }

  // Store the session ID for use in processBatch
  private sessionId?: string;

  async processBatch(
    sessionIdOrBatchNumber: string | number,
    batchNumberOrConfig?: number | ProcessorConfig,
    batchSizeOrConfig?: number | ProcessorConfig
  ): Promise<Array<{ text: string, metadata?: any }> | null>{
    // Handle different parameter orders for backward compatibility
    let sessionId: string;
    let batchNumber: number;
    let batchSize: number = 50; // Default batch size

    if (typeof sessionIdOrBatchNumber === 'string') {
      // Old signature: (sessionId, batchNumber, batchSize)
      sessionId = sessionIdOrBatchNumber;
      batchNumber = batchNumberOrConfig as number;
      if (typeof batchSizeOrConfig === 'number') {
        batchSize = batchSizeOrConfig;
      }
    } else {
      // New signature: (batchNumber, config)
      if (!this.sessionId) {
        throw new Error("Session not initialized. Call initializeSession first.");
      }
      sessionId = this.sessionId;
      batchNumber = sessionIdOrBatchNumber as number;
    }
    try {
      const response = await fetch(`${this.workerUrl}/fileUrl/batch`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...(this.apiKey ? { "Authorization": `Bearer ${this.apiKey}` } : {})
        },
        body: JSON.stringify({
          session_id: sessionId,
          batch_number: batchNumber,
          batch_size: batchSize
        })
      });

      if(!response.ok) {
        const errorText = await response.text();
        if(response.status === 404) {
          throw new Error("Session expired");
        }
        throw new Error(`Batch processing failed: ${errorText}`);
      }

      const chunks = await response.json() as ProcessedChunk[];

      // Return null if no more chunks or null response
      if(!chunks) {
        return null;
      }

      return chunks.map(chunk=>({
        text: chunk.text,
        metadata: chunk.metadata
      }));
    }catch(error) {
      console.error("Failed to process batch:", error);
      throw error;
    }
  }

  /**
   * Process a document and return all chunks
   * @param fileUrl URL of the file to process
   * @param config Configuration for the processor
   * @returns Array of chunks with text and metadata
   */
  async process(
    fileUrl: string,
    config: UnstructuredConfig = {}
  ): Promise<Array<{ text: string, metadata?: any }>>{
    // Initialize session
    await this.initializeSession(fileUrl, config);

    // Process all batches
    const chunks: Array<{ text: string, metadata?: any }> = [];

    if (this.useStreaming) {
      console.log("Using streaming mode for document processing");
      // In streaming mode, we process batches one by one with smaller batch size
      let batchNumber = 0;
      let batch: Array<{ text: string, metadata?: any }> | null;

      do {
        batch = await this.processBatch(batchNumber);
        if(batch) {
          chunks.push(...batch);
        }
        batchNumber++;
      } while(batch !== null && batch.length > 0);
    } else {
      // In non-streaming mode, we process all batches at once
      let batchNumber = 0;
      let batch: Array<{ text: string, metadata?: any }> | null;

      do {
        batch = await this.processBatch(batchNumber);
        if(batch) {
          chunks.push(...batch);
        }
        batchNumber++;
      } while(batch !== null && batch.length > 0);
    }

    return chunks;
  }

  async dispose(): Promise<void>{
    // Nothing to clean up when using the worker
    this.sessionId = undefined;
  }
}
