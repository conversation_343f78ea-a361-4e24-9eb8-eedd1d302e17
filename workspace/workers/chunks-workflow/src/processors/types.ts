export interface DocumentProcessor {
  initializeSession(filePath: string, config: ProcessorConfig): Promise<{ sessionId: string, totalChunks?: number }>,
  processBatch(batchNumber: number, config: ProcessorConfig): Promise<Array<{ text: string, metadata?: any }> | null>,
  dispose(): Promise<void>,
}

export interface ProcessorConfig {
  minTokens?: number,
  maxTokens?: number,
  semantic_chunking?: boolean,
  embeddings_provider?: string,
  skipDeJunk?: boolean,
}
