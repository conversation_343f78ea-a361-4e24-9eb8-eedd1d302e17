import { WorkflowEntrypoint, WorkflowEvent, WorkflowStep } from "cloudflare:workers";
import { Env, RagVectorTextChunksStatus } from "../types";
import { createFixedStorageClient } from "../utils/fixed-storage-client";
import { OpenParseProcessor } from "../processors/openparse";
import { UnstructuredProcessor } from "../processors/unstructured";
import { NonRetryableError } from "cloudflare:workflows";

import {
  StepContext,
  InitialSetup,
  R2ValidationResult,
  ChunkData,
  WorkflowFile,
  VectorizeConfig,
  FileRecord,
  initializeWorkflow,
  ensureD1Table,
  validateAndGetR2File,
  uploadToR2,
  ensureFileRecord,
  initializeProcessing,
  processBatch,
  validateAndFilterChunks,
  storeChunksInD1,
  storeChunksInR2,
  vectorizeChunks,
  addChunksToFileRecord,
  updateFileStatus,
  linkFileToRag,
  upsertWorkflowMetadata
} from "./steps";

interface ChunksVectorizedEvent {
  files: WorkflowFile[],
  vectorizeConfig: VectorizeConfig,
  timestamp: string | Date,
  instanceId: string,
}

export class ChunksVectorizedWorkflow extends WorkflowEntrypoint<Env, ChunksVectorizedEvent> {
  private storageClient: any;

  constructor(ctx: ExecutionContext, protected readonly env: Env){
    super(ctx, env);
    // Initialize the storage client
    this.storageClient = createFixedStorageClient(env);
  }

  async run(event: WorkflowEvent<ChunksVectorizedEvent>, step: WorkflowStep): Promise<void>{
    const { files, vectorizeConfig } = event.payload;

    // Create context for steps
    const context: StepContext = {
      env: this.env,
      storageClient: this.storageClient
    };

    // Declare variables that need to be accessible in catch/finally blocks
    let initialSetup: InitialSetup | undefined;
    let r2Result: R2ValidationResult | undefined;
    let processor: any;
    let documentChunks: any[] = [];

    try {
      // Step 0: Normalize processor config and initialize metadata
      initialSetup = await initializeWorkflow(step, context, files[0]);

      // Step 1: Ensure D1 table
      await ensureD1Table(step, context, vectorizeConfig.whitelabelId);

      // Step 2: Validate and get R2 file metadata
      r2Result = await validateAndGetR2File(
        step,
        context,
        initialSetup.fileInfo,
        files
      );

      // Step 3: Handle file upload if needed
      if(!r2Result.hasExistingFile) {
        await uploadToR2(step, context, r2Result, files);
      }

      // Step 4: Create or get file record
      const fileRecord: FileRecord = await ensureFileRecord(
        step,
        context,
        initialSetup,
        r2Result,
        vectorizeConfig
      );

      // Add debug logging before processing
      console.log("🔄 Starting document processing with:", {
        fileRecord: {
          id: fileRecord?.data?._id,
          fileKey: fileRecord?.data?.fileKey,
          status: fileRecord?.data?.status
        },
        processorType: initialSetup.fileInfo.processor
      });

      // Initialize processor
      const processorType = initialSetup.fileInfo.processor?.toLowerCase() || "unstructured";
      console.log(`🔄 Initializing processor: ${processorType}`, {
        config: initialSetup.normalizedConfig,
        fileName: initialSetup.fileInfo.fileName
      });

      // IMPORTANT: In BARE METAL MODE, we should not have any special handling for LOCAL MODE.
      // The environment configuration should handle the differences between environments.
      console.log(`🔄 Environment detected: ${this.env.ENVIRONMENT}`);

      processor = processorType === "openparse"
        ? new OpenParseProcessor(
            this.env.OPENPARSE_API_KEY,
            this.env.OPENPARSE_API_URL,
            this.env,
            {
              useStreaming: true
            }
          )
        : new UnstructuredProcessor(
            this.env.UNSTRUCTURED_WORKER_URL,
            { useStreaming: true }
          );

      try {
        // Step 5: Initialize processing session
        await initializeProcessing(
          step,
          context,
          r2Result,
          initialSetup,
          processorType,
          processor
        );

        // Step 6: Process document in batches
        documentChunks = [];
        let batchNumber = 0;

        const hasMoreBatches = true;
        while(hasMoreBatches) {
          // Process batch
          const batch = await processBatch(
            step,
            context,
            r2Result,
            initialSetup,
            processor,
            batchNumber
          );

          if(!batch) {
            console.log(`🏁 No more chunks to process after batch ${batchNumber}`);
            break;
          }

          documentChunks.push(...batch);
          console.log(`📊 Chunks accumulated: ${documentChunks.length} (added ${batch.length})`);

          batchNumber++;
        }
      } finally {
        // Ensure we clean up resources
        console.log(`📊 pre-processor.dispose - cleaning up resources`);
        await processor.dispose();
        console.log(`📊 post-processor.dispose - resources cleaned up`);
      }

      // Add final count logging
      console.log(`📊 Total chunks received: ${documentChunks.length}`);

      // Step 7: Validate and filter chunks
      const filteredChunks: ChunkData[] = await validateAndFilterChunks(
        step,
        context,
        initialSetup,
        documentChunks
      );

      // Step 8: Store chunks in D1
      await storeChunksInD1(
        step,
        context,
        filteredChunks,
        vectorizeConfig.whitelabelId
      );

      // Step 9: Store chunks in R2 as JSON
      await storeChunksInR2(
        step,
        context,
        filteredChunks,
        vectorizeConfig,
        initialSetup
      );

      // Step 10: Vectorize chunks
      await vectorizeChunks(
        step,
        context,
        filteredChunks,
        vectorizeConfig,
        initialSetup
      );

      // Step 11: Set file status to EDITING before adding chunks
      await updateFileStatus(
        step,
        context,
        initialSetup.fileInfo.fileId,
        RagVectorTextChunksStatus.EDITING,
        vectorizeConfig
      );

      // Step 12: Add chunks to file record
      await addChunksToFileRecord(
        step,
        context,
        filteredChunks,
        vectorizeConfig,
        initialSetup
      );

      // Step 13: Update file status to COMPLETED
      // Use the updateFileStatus function with built-in retry mechanism
      console.log(`🔄 Updating file status to COMPLETED: ${initialSetup.fileInfo.fileId}`);

      // Use the same updateFileStatus function that we used for EDITING
      await updateFileStatus(
        step,
        context,
        initialSetup.fileInfo.fileId,
        RagVectorTextChunksStatus.COMPLETED,
        vectorizeConfig
      );

      // Step 14: Link file to RAG if ragId is provided
      if(vectorizeConfig.ragId) {
        await linkFileToRag(
          step,
          context,
          vectorizeConfig.ragId,
          vectorizeConfig.whitelabelId,
          initialSetup.fileInfo.fileId,
          vectorizeConfig.auth0Token
        );
      }

      // Update workflow metadata with success status
      initialSetup.metadata.status = RagVectorTextChunksStatus.COMPLETED;
      await upsertWorkflowMetadata(step, context, initialSetup.metadata);

      // Clean up processor resources
      if(processor) {
        await step.do("cleanupProcessor", async ()=>{
          try {
            console.log("🧹 Cleaning up processor resources");
            await processor.dispose();
            console.log("✅ Processor resources cleaned up successfully");
          }catch(cleanupError) {
            console.warn(`⚠️ Error cleaning up processor: ${cleanupError.message}`);
            // Don't rethrow - we want to continue with the workflow
          }
        });
      }

    }catch(error) {
      console.error("❌ Workflow error:", error);

      // Update file status to ERROR if we have a file ID
      if(initialSetup?.fileInfo?.fileId) {
        try {
          await updateFileStatus(
            step,
            context,
            initialSetup.fileInfo.fileId,
            RagVectorTextChunksStatus.FAILED_TO_CHUNK,
            vectorizeConfig
          );
        }catch(statusError) {
          console.error("❌ Error updating file status to ERROR:", statusError);
          // Don't rethrow - we want to continue with the cleanup
        }
      }

      // Update workflow metadata with error status
      if(initialSetup?.metadata) {
        try {
          initialSetup.metadata.status = RagVectorTextChunksStatus.FAILED_TO_CHUNK;
          initialSetup.metadata.error = error.message || String(error);
          await upsertWorkflowMetadata(step, context, initialSetup.metadata, error);
        }catch(metadataError) {
          console.error("❌ Error updating workflow metadata:", metadataError);
          // Don't rethrow - we want to continue with the cleanup
        }
      }

      // Clean up processor resources in error case
      if(processor) {
        await step.do("cleanupProcessorOnError", async ()=>{
          try {
            console.log("🧹 Cleaning up processor resources after error");
            await processor.dispose();
            console.log("✅ Processor resources cleaned up successfully after error");
          }catch(cleanupError) {
            console.warn(`⚠️ Error cleaning up processor after error: ${cleanupError.message}`);
            // Don't rethrow - we want to continue with the error handling
          }
        });
      }

      // Rethrow non-retryable errors
      if(error instanceof NonRetryableError) {
        throw error;
      }

      // Rethrow other errors to trigger retry
      throw error;
    }
  }
}
