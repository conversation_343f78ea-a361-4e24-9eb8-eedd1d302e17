name = "chunks-workflow"
main = "src/index.ts"
compatibility_flags = ["nodejs_compat"]
compatibility_date = "2024-09-23"
logpush = true

[observability]
enabled = true

[placement]
mode = "smart"

[dev]
port = 8789
ip = "0.0.0.0"
local_protocol = "http"

# Add AI binding for all environments
[ai]
binding = "AI"

[vars]
ENVIRONMENT = "development"
OPENPARSE_API_URL = "http://local-open-parse:8084"
API_HOST = "http://local-api:8080"
CLOUDFLARE_ACCOUNT_ID="14a6fa23390363382f378b5bd4a0f849"

# D1 Database
# [[d1_databases]]
# binding = "DB"
# database_name = "d1-doc-elements"
# database_id = "d1-doc-elements"
migrations_dir = "migrations"

# R2 bucket
# [[r2_buckets]]
# binding = "R2"
# bucket_name = "chunks-vectorized"

[env.local]
# routes = [
#   { pattern = "rag-workflow.local.divinci.app", custom_domain = true }
# ]

# Local environment
[env.local.vars]
ENVIRONMENT = "local"
API_HOST = "http://local-api:8080"
CLOUDFLARE_AI_API_URL = "https://api.cloudflare.com/client/v4"
# Fix the OpenParse URL to use the correct port (8084)
OPENPARSE_API_URL = "http://local-open-parse:8084"
CLOUDFLARE_ACCOUNT_ID="14a6fa23390363382f378b5bd4a0f849"
CLOUDFLARE_API_TOKEN = "****************************************"
UNSTRUCTURED_WORKER_URL = "http://local-rag-chunker-unstructured:8789"
ALLOWED_ORIGINS = "http://localhost:8080,http://localhost:8789,http://localhost:8791,http://127.0.0.1:8080,http://127.0.0.1:8789,http://127.0.0.1:8791,https://api.unstructuredapp.io,http://localhost:9000,http://localhost:9001,http://127.0.0.1:9000,http://127.0.0.1:9001,http://**********:9000,http://**********:9001,http://local-minio:9000,127.0.0.1:9000"
CLOUDFLARE_D1_API_URL = "http://local-d1-rag:8787"
CLOUDFLARE_WORKER_X_AUTH_DEV = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.4ygtkAA4RmHtGAT5jHKzpr_3HgpaVSVKQ-IqzFJA7TI2"
# IS_LOCAL=true
# R2_BUCKET_NAME = "rag-origin-files-local"
# CRITICAL: Use the Docker service name for MinIO in local development
# Port 9000 is for the API, port 9001 is for the web UI
# Always include the protocol (http://) in the URL
# NEVER use localhost for the bucket URL in this config
R2_BUCKET_URL = "http://local-minio:9000"
# MinIO credentials for local development
R2_ACCESS_KEY_ID="EKzhr0JMbJwLFVlE08Om"
R2_SECRET_ACCESS_KEY="ropI7tOATZSjVpdRnZz59A25xpzGe4yxAWoINO0M"

# [[env.local.tail_consumers]]
# service = "chunks-workflow_tail-local"

# Local environment KV
# [[env.local.kv_namespaces]]
# binding = "CHUNKS_KV"
# id = "your-local-kv-namespace-id"

# [[env.local.queues.producers]]
# queue = "chunking-queue-local"
# binding = "CHUNKING_QUEUE"

# [[env.local.queues.consumers]]
# queue = "chunking-queue-local"
# max_batch_size = 100  # Increased from 1
# max_batch_timeout = 30
# max_retries = 3

[[env.local.workflows]]
name = "chunks-vectorized-local"
binding = "CHUNKS_VECTORIZED"
class_name = "ChunksVectorizedWorkflow"

# Add AI binding for dev environment
[env.local.ai]
binding = "AI"

[[env.local.r2_buckets]]
binding = "R2"
bucket_name = "rag-origin-files-local"
preview_bucket_name = "rag-origin-files-preview"

# [[env.local.d1_databases]]
# binding = "DB"
# database_name = "d1-doc-elements-dev"
# database_id = "ec1ddea6-6668-413d-b05c-54920a3f4539"

[[env.local.d1_databases]]
binding = "DB"
database_name = "d1-doc-elements-local"
database_id = "40161e7b-fbc6-4fa3-87d0-d58307d281b0"
preview_database_id = "d1-doc-local"
migrations_dir = "migrations"

# Development environment configuration
[env.dev]
routes = [
  { pattern = "rag-workflow.dev.divinci.app", custom_domain = true }
]
[[env.dev.workflows]]
name = "chunks-vectorized-dev"
binding = "CHUNKS_VECTORIZED"
class_name = "ChunksVectorizedWorkflow"

# Add AI binding for dev environment
[env.dev.ai]
binding = "AI"

[[env.dev.r2_buckets]]
binding = "R2"
bucket_name = "whitelabel-vector-index-dev"

# [[env.dev.kv_namespaces]]
# binding = "CHUNKS_KV"
# id = "a8f840059d08414c8597a3de14424c46"

# # Dev environment queues
# [[env.dev.queues.consumers]]
# queue = "chunking-queue-dev"
# max_batch_size = 100  # Increased from 1
# max_batch_timeout = 30
# max_retries = 3

# [[env.dev.queues.producers]]
# queue = "chunking-queue-dev"
# binding = "CHUNKING_QUEUE"

[[env.dev.d1_databases]]
binding = "DB"
database_name = "d1-doc-elements-dev"
database_id = "ec1ddea6-6668-413d-b05c-54920a3f4539"
migrations_dir = "migrations"

[env.dev.vars]
ENVIRONMENT = "dev"
API_HOST = "https://api.dev.divinci.app"
CLOUDFLARE_AI_API_URL = "https://api.cloudflare.com/client/v4"
OPENPARSE_API_URL = "https://open-parse.dev.divinci.app"
CLOUDFLARE_ACCOUNT_ID = "14a6fa23390363382f378b5bd4a0f849"
CLOUDFLARE_API_TOKEN = "****************************************"
UNSTRUCTURED_WORKER_URL = "https://rag-chunker-unstructured.dev.divinci.app"
ALLOWED_ORIGINS = "https://*.dev.divinci.app"
CLOUDFLARE_D1_API_URL = "https://d1.dev.divinci.app"
# R2_BUCKET_NAME = "rag-origin-files-dev"
R2_BUCKET_URL = "https://rag-files.dev.divinci.app"
# R2_ACCESS_KEY_ID="7a4b9ed4e9f2d02de1d6484e6b433f02"
# R2_SECRET_ACCESS_KEY="8f28017d6a8e9135a027cd28117015c7805dd6b663d6788af56bb44196fae7a3"

[[env.dev.tail_consumers]]
service = "chunks-workflow_tail-dev"

# Staging environment configuration
[env.stage]
routes = [
  { pattern = "rag-workflow.stage.divinci.app", custom_domain = true }
]
[[env.stage.workflows]]
name = "chunks-vectorized-stage"
binding = "CHUNKS_VECTORIZED"
class_name = "ChunksVectorizedWorkflow"

# Add AI binding for dev environment
[env.stage.ai]
binding = "AI"

[[env.stage.r2_buckets]]
binding = "R2"
bucket_name = "rag-origin-files-stage"

# Stage environment KV
# [[env.stage.kv_namespaces]]
# binding = "CHUNKS_KV"
# id = "c73a4a2936754cf797a5a269b798a7b6"

# # Stage environment queues
# [[env.stage.queues.consumers]]
# queue = "chunking-queue-stage"
# max_batch_size = 100  # Increased from 1
# max_batch_timeout = 30
# max_retries = 3

# [[env.stage.queues.producers]]
# queue = "chunking-queue-stage"
# binding = "CHUNKING_QUEUE"

[[env.stage.d1_databases]]
binding = "DB"
database_name = "d1-doc-elements-stage"
database_id = "93f7422a-a0ac-4790-898d-7c03ae85ee4c"
migrations_dir = "migrations"

[env.stage.vars]
migrations_dir = "migrations"
ENVIRONMENT = "stage"
API_HOST = "https://api.stage.divinci.app"
CLOUDFLARE_AI_API_URL = "https://api.cloudflare.com/client/v4"
OPENPARSE_API_URL = "https://open-parse.stage.divinci.app"
CLOUDFLARE_ACCOUNT_ID = "14a6fa23390363382f378b5bd4a0f849"
CLOUDFLARE_API_TOKEN = "****************************************"
UNSTRUCTURED_WORKER_URL = "https://rag-chunker-unstructured.stage.divinci.app"
ALLOWED_ORIGINS = "https://*.stage.divinci.app"
CLOUDFLARE_D1_API_URL = "https://d1.stage.divinci.app"
# R2_BUCKET_NAME = "rag-origin-files-stage"
R2_BUCKET_URL = "https://rag-files.stage.divinci.app"
# R2_ACCESS_KEY_ID="7a4b9ed4e9f2d02de1d6484e6b433f02"
# R2_SECRET_ACCESS_KEY="8f28017d6a8e9135a027cd28117015c7805dd6b663d6788af56bb44196fae7a3"

[[env.stage.tail_consumers]]
service = "chunks-workflow_tail-stage"

# Production environment
[env.production]
routes = [
  { pattern = "rag-workflow.divinci.app", custom_domain = true }
]
[[env.production.workflows]]
name = "chunks-vectorized-production"
binding = "CHUNKS_VECTORIZED"
class_name = "ChunksVectorizedWorkflow"

[[env.production.r2_buckets]]
binding = "R2"
bucket_name = "whitelabel-vector-index"

[[env.production.d1_databases]]
binding = "DB"
database_name = "d1-doc-elements-prod"
database_id = "a76d6335-2643-4ada-8762-ebcd68e5aa95"
migrations_dir = "migrations"

# Add AI binding for production environment
[env.production.ai]
binding = "AI"

[env.production.vars]
ENVIRONMENT = "production"
API_HOST = "https://api.divinci.app"
CLOUDFLARE_AI_API_URL = "https://api.cloudflare.com/client/v4"
OPENPARSE_API_URL = "https://open-parse.divinci.app"
UNSTRUCTURED_WORKER_URL = "https://rag-chunker-unstructured.divinci.app"
ALLOWED_ORIGINS = "https://*.divinci.app"
CLOUDFLARE_D1_API_URL = "https://d1.divinci.app"
# R2_BUCKET_NAME = "rag-origin-files-prod"
R2_BUCKET_URL = "https://rag-files.divinci.app"
# R2_ACCESS_KEY_ID="7a4b9ed4e9f2d02de1d6484e6b433f02"
# R2_SECRET_ACCESS_KEY="8f28017d6a8e9135a027cd28117015c7805dd6b663d6788af56bb44196fae7a3"

[[env.production.tail_consumers]]
service = "chunks-workflow_tail-production"

# Production environment queues
# [[env.production.queues.consumers]]
# queue = "chunking-queue-prod"
# max_batch_size = 100  # Increased from 1
# max_batch_timeout = 30
# max_retries = 3

# [[env.production.queues.producers]]
# queue = "chunking-queue-prod"
# binding = "CHUNKING_QUEUE"

# # Production environment KV
# [[env.production.kv_namespaces]]
# binding = "CHUNKS_KV"
# id = "6c4b3d11cb8f4eb3a1938506ef34b5a0"
