#!/bin/bash
# Template for service mTLS entrypoint script
# This script should be customized for each service

set -e

# Configuration
SERVICE_NAME="$1"  # Pass the service name as the first argument
CERT_DIR="/etc/ssl"
CA_CERT="$CERT_DIR/certs/ca.crt"
SERVER_CERT="$CERT_DIR/certs/server.crt"
SERVER_KEY="$CERT_DIR/private/server.key"

# Function to check certificate validity
check_certificate() {
  local cert_file=$1
  local cert_name=$2
  
  if [ ! -f "$cert_file" ]; then
    echo "❌ $cert_name not found at $cert_file"
    return 1
  fi
  
  echo "✅ Found $cert_name at $cert_file"
  echo "📄 Certificate content (first few lines):"
  head -3 "$cert_file"
  
  # Check certificate format
  if ! grep -q "BEGIN CERTIFICATE" "$cert_file"; then
    echo "⚠️ WARNING: $cert_name does not appear to be in PEM format!"
    echo "Checking certificate format..."
    
    # Display detailed information about the certificate
    echo "Certificate size: $(wc -c < "$cert_file") bytes"
    echo "First 50 characters: $(head -c 50 "$cert_file" | hexdump -C)"
    
    return 1
  fi
  
  return 0
}

# Function to check private key validity
check_private_key() {
  local key_file=$1
  local key_name=$2
  
  if [ ! -f "$key_file" ]; then
    echo "❌ $key_name not found at $key_file"
    return 1
  fi
  
  echo "✅ Found $key_name at $key_file"
  echo "📄 Key content (first few lines):"
  head -3 "$key_file"
  
  # Check key format
  if ! grep -q "BEGIN PRIVATE KEY\|BEGIN RSA PRIVATE KEY" "$key_file"; then
    echo "⚠️ WARNING: $key_name does not appear to be in PEM format!"
    echo "Checking key format..."
    
    # Display detailed information about the key
    echo "Key size: $(wc -c < "$key_file") bytes"
    echo "First 50 characters: $(head -c 50 "$key_file" | hexdump -C)"
    
    return 1
  fi
  
  return 0
}

# Check for required certificates
echo "🔍 Checking for mTLS certificates..."
MTLS_ENABLED="1"

# Check CA certificate
if ! check_certificate "$CA_CERT" "CA certificate"; then
  MTLS_ENABLED="0"
fi

# Check server certificate
if ! check_certificate "$SERVER_CERT" "Server certificate"; then
  MTLS_ENABLED="0"
fi

# Check server key
if ! check_private_key "$SERVER_KEY" "Server key"; then
  MTLS_ENABLED="0"
fi

# Set environment variables for mTLS
if [ "$MTLS_ENABLED" = "1" ]; then
  echo "✅ mTLS is enabled for $SERVICE_NAME"
  export MTLS_ENABLED="true"
  export MTLS_CA_CERT="$CA_CERT"
  export MTLS_SERVER_CERT="$SERVER_CERT"
  export MTLS_SERVER_KEY="$SERVER_KEY"
else
  echo "⚠️ mTLS is disabled for $SERVICE_NAME due to missing or invalid certificates"
  export MTLS_ENABLED="false"
fi

# Service-specific startup commands
case "$SERVICE_NAME" in
  "ffmpeg")
    echo "🚀 Starting ffmpeg service..."
    exec npm run start
    ;;
  "open-parse")
    echo "🚀 Starting open-parse service..."
    exec gunicorn --bind "0.0.0.0:$PORT" \
      --workers 2 \
      --threads=${THREADS:-50} \
      --worker-class=gthread \
      --worker-tmp-dir=/dev/shm \
      --timeout 120 \
      --access-logfile - \
      --error-logfile - \
      --log-level info \
      app:app
    ;;
  "pyannote")
    echo "🚀 Starting pyannote service..."
    exec gunicorn --bind "0.0.0.0:$PORT" \
      --workers 2 \
      --threads=${THREADS:-8} \
      --worker-class=gthread \
      --worker-tmp-dir=/dev/shm \
      --timeout 120 \
      --access-logfile - \
      --error-logfile - \
      --log-level info \
      wsgi:app
    ;;
  *)
    echo "❌ Unknown service: $SERVICE_NAME"
    exit 1
    ;;
esac
