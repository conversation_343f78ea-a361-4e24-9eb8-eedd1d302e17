import { ChildProcess, spawn } from "node:child_process";
import { Readable, Writable } from "node:stream";
import { pipeline } from "node:stream/promises";

export function spawnChild(processName: string, spawnArgs: Array<string>){
  return new Promise<ChildProcess>((res, rej)=>{
    const childProcess = spawn(processName, spawnArgs);

    let sl: (()=>any);
    let el: ((e: any)=>any);
    childProcess.on("spawn", sl = ()=>{cleanup();res(childProcess);});
    childProcess.on("error", el = (e)=>{cleanup();rej(e);});

    function cleanup(){
      childProcess.off("spawn", sl);
      childProcess.off("error", el);
    }
  });
}

export function handleBadExit(child: ChildProcess){
  const stderr = child.stderr;
  if(!stderr){
    throw new Error("Missing required stderr stream");
  }
  const { promise, resolve, reject } = Promise.withResolvers<void>();

  // Capture errors from stderr
  let errorMessage = "";
  stderr.on("data", (data)=>{
    const chunk = data.toString();
    errorMessage += chunk;
    // Log stderr in real-time for better debugging
    console.log(`Process stderr: ${chunk.trim()}`);
  });

  // Also capture stdout for debugging
  if (child.stdout) {
    child.stdout.on("data", (data) => {
      const chunk = data.toString();
      // Only log non-empty stdout
      if (chunk.trim()) {
        console.log(`Process stdout: ${chunk.trim()}`);
      }
    });
  }

  child.on("close", (code)=>{
    if(code === 0) return resolve();

    // Create a more detailed error message
    let detailedError = `Process failed with code ${code}`;

    if (errorMessage.trim()) {
      detailedError += `:\n${errorMessage.trim()}`;
    }

    // Add hints for common ffmpeg errors
    if (errorMessage.includes("Invalid data found when processing input")) {
      detailedError += "\n\nHint: The input file appears to be corrupted or not a valid media file.";
    } else if (errorMessage.includes("No such file or directory")) {
      detailedError += "\n\nHint: The input file could not be found or accessed.";
    } else if (errorMessage.includes("Permission denied")) {
      detailedError += "\n\nHint: There was a permission issue accessing the file.";
    }

    reject(new Error(detailedError));
  });

  return promise;
}

export function collectOutputHandleExit(child: ChildProcess){
  if(!child.stdout){
    throw new Error("Missing required stdout stream");
  }
  if(!child.stderr){
    throw new Error("Missing required stderr stream");
  }
  const { promise, resolve, reject } = Promise.withResolvers<string>();
  let stdoutData = "";
  let stderrData = "";

  // Collect ffprobe's standard output
  child.stdout.on("data", (chunk)=>{
    stdoutData += chunk;
  });

  // Collect ffprobe's error output (if any)
  child.stderr.on("data", (chunk)=>{
    stderrData += chunk;
  });

  // When ffprobe finishes, parse the duration from stdout
  child.on("close", (code)=>{
    if(code !== 0) {
      return reject(new Error(`Process failed with code ${code}:\n${stderrData}`));
    }
    resolve(stdoutData);
  });

  return promise;
}

export async function streamToProcessToStream(
  source: Readable, child: ChildProcess, destination: Writable
){
  try {
    const { stdin, stdout, stderr } = child;
    if(!stdin || !stdout || !stderr){
      throw new Error("Failed to spawn");
    }

    // Collect stderr output for better error reporting
    let stderrOutput = '';
    stderr.on('data', (data) => {
      const chunk = data.toString();
      stderrOutput += chunk;
      console.log(`FFmpeg stderr: ${chunk.trim()}`);
    });

    // Also capture stdout for debugging
    stdout.on('data', (data) => {
      const chunk = data.toString();
      if (chunk.trim()) {
        console.log(`FFmpeg stdout: ${chunk.trim()}`);
      }
    });

    // Set up a timeout to kill the process if it takes too long
    const timeoutMs = 5 * 60 * 1000; // 5 minutes
    const timeout = setTimeout(() => {
      console.error(`FFmpeg process timed out after ${timeoutMs/1000} seconds`);
      try { child.kill('SIGKILL'); } catch(e) { console.warn("Error killing process on timeout", e); }
    }, timeoutMs);

    // Add error handlers to the streams
    source.on('error', (err) => {
      console.error(`Source stream error: ${err.message}`);
    });

    stdin.on('error', (err) => {
      console.error(`stdin stream error: ${err.message}`);
    });

    stdout.on('error', (err) => {
      console.error(`stdout stream error: ${err.message}`);
    });

    destination.on('error', (err) => {
      console.error(`Destination stream error: ${err.message}`);
    });

    try {
      await Promise.all([
        pipeline(source, stdin).catch((err)=>{
          switch(err.code){
            case "EPIPE":{
              console.warn("Child closed stdin early, ignoring EPIPE.");
              return;
            }
            case "ERR_STREAM_PREMATURE_CLOSE": {
              console.warn("Child closed stdin early, ignoring ERR_STREAM_PREMATURE_CLOSE.");
              return;
            }
            default:
              console.error(`Pipeline error (source to stdin): ${err.message}`);
              if (stderrOutput) {
                console.error(`FFmpeg stderr output: ${stderrOutput}`);
                // Enhance the error with the stderr output
                err.ffmpegError = stderrOutput;
              }
              throw err;
          }
        }),
        pipeline(stdout, destination).catch((err) => {
          console.error(`Pipeline error (stdout to destination): ${err.message}`);
          if (stderrOutput) {
            console.error(`FFmpeg stderr output: ${stderrOutput}`);
            // Enhance the error with the stderr output
            err.ffmpegError = stderrOutput;
          }
          throw err;
        }),
        handleBadExit(child).catch((err) => {
          console.error(`Process exit error: ${err.message}`);
          if (stderrOutput && !err.message.includes(stderrOutput)) {
            console.error(`FFmpeg stderr output: ${stderrOutput}`);
            // Create a new error that includes the stderr output
            const enhancedError = new Error(`${err.message}\nFFmpeg output: ${stderrOutput}`);
            enhancedError.stack = err.stack;
            throw enhancedError;
          }
          throw err;
        }),
      ]);
    } finally {
      clearTimeout(timeout);
    }
  } catch (error) {
    console.error("Error in streamToProcessToStream:", error);
    throw error;
  } finally {
    try { source.destroy(); }catch(e){ console.warn("Ignoring source cancel Error", e); }
    try { child.kill(); }catch(e){ console.warn("Ignoring ChildProcess kill Error", e); }
    try { destination.destroy(); }catch(e){ console.warn("Ignoring destination destroy Error", e); }
  }
}
