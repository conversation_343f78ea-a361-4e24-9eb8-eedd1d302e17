import { Router  } from "express";
import { storeAudioProgress } from "../services/redis";
import { jsonBody } from "../utils/req-body-json";

export const router = Router();

router.get("/", function(req, res, next){
  try {
    res.statusCode = 200;
    res.json({
      "hello": "world",
      whoami: "an audio utility worker"
    });
  }catch(e){
    next(e);
  }
});

// Add a new endpoint to update progress
router.post("/progress", async function(req, res, next){
  try {
    const body = await jsonBody(req) as any;

    // Validate required fields
    if (!body.audioId || !body.step || body.progress === undefined) {
      res.statusCode = 400;
      return res.json({
        error: "Missing required fields: audioId, step, progress"
      });
    }

    // Store progress in Redis
    await storeAudioProgress(
      body.audioId as string,
      body.step as string,
      parseFloat(body.progress as string),
      (body.ttl as number) || 3600
    );

    res.statusCode = 200;
    res.json({
      success: true,
      message: `Progress updated for audio ${body.audioId}`
    });
  } catch(e) {
    next(e);
  }
});

router.get("/health", function(req, res, next){
  try {
    res.statusCode = 200;
    res.json({
      status: "ok",
      service: "ffmpeg",
      mtls: process.env.MTLS_ENABLED === "true" ? "enabled" : "disabled"
    });
  }catch(e){
    next(e);
  }
});

import { castObject, castBody, TypeCastConfig } from "../utils/cast-object";
import { HTTP_ERRORS_WITH_CONTEXT } from "../utils/http-errors";

import { SliceAudioArgs, sliceAudio } from "./slice-audio";
const SLICE_BODY_TYPES: TypeCastConfig<SliceAudioArgs> = {
  start: "number", end: "number", Bucket: "string", Key: "string"
};

function createBodyErrorMessage(schema: JSON_Object){
  return `Expecting a body that looks like ${JSON.stringify(schema)}`;

}

router.post("/slice", async function(req, res, next){
  try {
    const body = await jsonBody(req);

    const args = castBody<SliceAudioArgs>(
      SLICE_BODY_TYPES, body as any, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        createBodyErrorMessage(SLICE_BODY_TYPES)
      )
    );

    const { Bucket, Key } = await sliceAudio(args);

    res.statusCode = 200;
    res.json({ Bucket, Key });

  }catch(e){
    next(e);
  }
});

import { AudioDurationArgs, getAudioDuration } from "./audio-duration";
const DURATION_BODY_TYPES: TypeCastConfig<AudioDurationArgs> = {
  Bucket: "string", Key: "string"
};
router.post("/get-duration", async function(req, res, next){
  try {
    const body = await jsonBody(req);
    const args = castBody<AudioDurationArgs>(
      DURATION_BODY_TYPES, body as any, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        createBodyErrorMessage(DURATION_BODY_TYPES)
      )
    );

    const duration = await getAudioDuration(args);

    res.statusCode = 200;
    res.json({ duration });

  }catch(e){
    next(e);
  }
});


import { SupportedFileArgs, canHandleFilename } from "./convert-to-mp3";
const CAN_CONVERT_BODY_TYPES: TypeCastConfig<SupportedFileArgs> = {
  filename: "string"
};
router.post("/can-convert", async function(req, res, next){
  try {
    const body = await jsonBody(req);

    const args = castBody<SupportedFileArgs>(
      CAN_CONVERT_BODY_TYPES, body as any, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        createBodyErrorMessage(CAN_CONVERT_BODY_TYPES)
      )
    );

    const handleResponse = canHandleFilename(args);

    res.statusCode = 200;
    res.json(handleResponse);

  }catch(e){
    next(e);
  }
});

import { ConvertMP3Args, convertToMP3 } from "./convert-to-mp3";
import { JSON_Object } from "../utils/type-json";
const CONVERTMP3_BODY_TYPES: TypeCastConfig<ConvertMP3Args> = {
  Bucket: "string", Key: "string"
};
const CONVERTMP3_FULL_BODY_TYPE = {
  source: CONVERTMP3_BODY_TYPES,
  destination: CONVERTMP3_BODY_TYPES,
};
router.post("/convert-to-mp3", async function(req, res, next){
  // Note: This endpoint now converts to FLAC instead of MP3, but we keep the endpoint name for backward compatibility
  try {
    const body = castObject(
      await jsonBody(req) as any,
      HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        createBodyErrorMessage(CONVERTMP3_FULL_BODY_TYPE)
      )
    );

    const source = castBody<ConvertMP3Args>(
      CONVERTMP3_BODY_TYPES, body.source as any,
      HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        createBodyErrorMessage(CONVERTMP3_FULL_BODY_TYPE)
      )
    );
    const destination = castBody<ConvertMP3Args>(
      CONVERTMP3_BODY_TYPES, body.destination as any,
      HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        createBodyErrorMessage(CONVERTMP3_FULL_BODY_TYPE)
      )
    );

    const { Bucket, Key } = await convertToMP3(source, destination);

    res.statusCode = 200;
    res.json({ Bucket, Key });

  }catch(e){
    next(e);
  }
});
