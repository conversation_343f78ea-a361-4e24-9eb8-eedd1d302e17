import { setupEnv } from "./utils/env";
import { resolve as pathResolve } from "node:path";
setupEnv({ envPath: pathResolve(__dirname, "../env"), quiet: true });

import { Server } from "node:http";
import express, { Request, Response } from "express";

import { router } from "./app";
import { HTTP_ERRORS } from "./utils/http-errors";
import { createServer, verifyClientCert } from "./mtls-server";

Promise.resolve().then(async function(){
  const app = express();

  // Add mTLS client certificate verification middleware
  app.use(verifyClientCert);

  app.use(router);

  app.use(function(req, res, next){
    next(HTTP_ERRORS.NOT_FOUND);
  });

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  app.use(function(err: any, req: Request, res: Response, next: (e?: any)=>any){
    console.error("HTTP Error:", req.method, req.url, err);
    res.statusCode = err.statusCode || 500;

    res.json({
      message: err.message,
      context: err.context,
    });
  });

  // Create server with mTLS if enabled
  const server = createServer(app);

  const port = getHTTPPort();
  await listen(server, port);

  // eslint-disable-next-line no-console
  console.log("🚀 Server Listening on port", port);

});

const POSSIBLE_ENV_VARS = [
  "DIVINCI_AUDIO_SPLITTER_FFMPEG_HTTP_PORT",
  "HTTP_PORT",
];
function getHTTPPort(){
  for(const ENV_NAME of POSSIBLE_ENV_VARS){
    const value = process.env[ENV_NAME];
    if(!value) continue;
    const port = Number.parseInt(value);
    if(Number.isNaN(port)){
      throw new Error(`Bad Environment Variable "${ENV_NAME}" ${value}`);
    }
    return port;
  }
  throw new Error("No Environment Variable for HTTP Port");
}

function listen(server: Server, port: number){
  return new Promise<void>(function(res, rej){
    let success: (()=>any);
    let error: ((e?: any)=>any);

    server.on("listening", success = ()=>{
      cleanup(); res();
    });
    server.on("error", error = (e)=>{
      cleanup(); rej(e);
    });

    server.listen(port);

    function cleanup(){
      server.off("listening", success);
      server.off("error", error);
    }
  });
}