FROM python:3.11.11-bookworm

# Install system dependencies for PyMuPDF
RUN apt-get update && \
    apt-get install -y \
    swig \
    tesseract-ocr \
    && rm -rf /var/lib/apt/lists/*

# Create and set the working directory
WORKDIR /home/<USER>/app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --upgrade pip
RUN pip install pymupdf==1.23.3
RUN pip install -r requirements.txt

# Copy the application code
COPY . .

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/home/<USER>/app
ENV LOG_LEVEL=DEBUG
ENV OPENPARSE_LOG_LEVEL=DEBUG

# Create temp directory for file processing
RUN mkdir -p /openparse-tmp

# Expose the port the app runs on
EXPOSE 5000

# Copy and set the entrypoint script
# COPY docker-entrypoint.sh /usr/local/bin/
# RUN chmod +x /usr/local/bin/docker-entrypoint.sh
# ENTRYPOINT ["docker-entrypoint.sh"]

# Default command
CMD ["python", "app.py"]
