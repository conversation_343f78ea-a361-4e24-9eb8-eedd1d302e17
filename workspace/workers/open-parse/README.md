# OpenParse

OpenParse is a document processing service that converts various document formats (PDF, DOCX, etc.) into semantically meaningful text chunks for use in RAG (Retrieval-Augmented Generation) systems.

## Features

- **Document Processing**: Convert documents to text chunks
- **Semantic Chunking**: Create semantically meaningful chunks
- **Batch Processing**: Process multiple files in a single request
- **Remote File Processing**: Process files from remote URLs
- **Session Management**: Manage processing sessions for large documents
- **mTLS Support**: Secure server-to-server communication

## Getting Started

### Prerequisites

- Python 3.9+
- Docker (for containerized deployment)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/Divinci-AI/server.git
   cd server/workspace/workers/open-parse
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   ```bash
   export ENV_FOLDER=/path/to/env/folder
   export OPENPARSE_HTTP_PORT=8084
   ```

### Running Locally

```bash
python app.py
```

### Running with Docker

```bash
docker compose -f docker/local.yml up local-open-parse
```

## API Reference

See the [API Reference](docs/api-reference.md) for detailed documentation of all endpoints.

### Key Endpoints

- `GET /`: Root endpoint that returns basic service information
- `POST /`: Main document processing endpoint
- `POST /batch`: Batch processing endpoint for multiple files
- `POST /folder`: Folder processing endpoint for ZIP archives
- `POST /fileUrl/init`: Initialize remote file processing
- `POST /fileUrl/batch`: Retrieve a batch of chunks from a processing session
- `POST /cleanup/{session_id}`: Clean up a session by ID
- `POST /session/{session_id}/cleanup`: Alternative endpoint for session cleanup

## Session Management

OpenParse uses in-memory session storage to manage document processing sessions. Sessions are automatically cleaned up after 1 hour of inactivity.

### Manual Session Cleanup

You can manually clean up sessions using the following endpoints:

- `POST /cleanup/{session_id}`: Primary cleanup endpoint
- `POST /session/{session_id}/cleanup`: Alternative cleanup endpoint

Both endpoints remove the session data from memory and return a success response.

## mTLS Support

OpenParse supports mutual TLS (mTLS) for secure server-to-server communication. When enabled, both the client and server authenticate each other using X.509 certificates.

### Enabling mTLS

1. Set the `MTLS_ENABLED` environment variable to `true`
2. Configure certificate paths:
   - `MTLS_CA_CERT`: Path to the CA certificate
   - `MTLS_SERVER_CERT`: Path to the server certificate
   - `MTLS_SERVER_KEY`: Path to the server private key

Health endpoints (`/health` and `/_ah/health`) are exempt from mTLS verification.

## Configuration

OpenParse can be configured using environment variables:

- `OPENPARSE_HTTP_PORT`: HTTP port (default: 8084)
- `ENV_FOLDER`: Path to environment files
- `ENVIRONMENT`: Environment (development, staging, production)
- `MTLS_ENABLED`: Enable mTLS (true/false)
- `MTLS_CA_CERT`: Path to CA certificate
- `MTLS_SERVER_CERT`: Path to server certificate
- `MTLS_SERVER_KEY`: Path to server private key
- `OPENPARSE_CORS_ORIGINS`: Comma-separated list of allowed CORS origins

## Document Processing Configuration

When processing documents, you can provide a configuration object with the following options:

```json
{
  "semantic_chunking": false,
  "embeddings_provider": "cloudflare",
  "minTokens": 64,
  "maxTokens": 1024,
  "chunkOverlap": 200,
  "useTokens": true
}
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
