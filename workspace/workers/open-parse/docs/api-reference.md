# OpenParse API Reference

This document provides a comprehensive reference for the OpenParse API endpoints, including request/response formats and authentication methods.

## Table of Contents

1. [Authentication](#authentication)
2. [Health Endpoints](#health-endpoints)
3. [Document Processing Endpoints](#document-processing-endpoints)
4. [Remote File Processing Endpoints](#remote-file-processing-endpoints)
5. [Session Management Endpoints](#session-management-endpoints)

## Authentication

OpenParse supports two authentication methods:

### Bearer Token Authentication

For standard API access, use a Bearer token in the Authorization header:

```
Authorization: Bearer <your-api-key>
```

### Mutual TLS (mTLS)

For secure server-to-server communication, OpenParse supports mutual TLS authentication. When enabled, both the client and server authenticate each other using X.509 certificates.

To enable mTLS:

1. Set the `MTLS_ENABLED` environment variable to `true`
2. Configure certificate paths:
   - `MTLS_CA_CERT`: Path to the CA certificate
   - `MTLS_SERVER_CERT`: Path to the server certificate
   - `MTLS_SERVER_KEY`: Path to the server private key

Health endpoints (`/health` and `/_ah/health`) are exempt from mTLS verification.

## Health Endpoints

### GET /_ah/health

Health check endpoint for Cloud Run.

**Response:**
```json
{
  "status": "healthy"
}
```

### GET /health

Health check endpoint with mTLS status.

**Response:**
```json
{
  "status": "ok",
  "service": "open-parse",
  "mtls": "enabled" // or "disabled"
}
```

## Document Processing Endpoints

### GET /

Root endpoint that returns basic service information.

**Response:**
```json
{
  "hello": "world",
  "whoami": "a doc processor"
}
```

### POST /

Main document processing endpoint. Accepts a file upload and processes it using OpenParse.

**Request:**
- Form data with `file` field containing the document
- Optional `config` field with JSON configuration:
  ```json
  {
    "semantic_chunking": false,
    "embeddings_provider": "cloudflare",
    "minTokens": 64,
    "maxTokens": 1024,
    "chunkOverlap": 200,
    "useTokens": true
  }
  ```

**Response:**
- JSON array stream of processed document chunks

### POST /batch

Batch processing endpoint for multiple files.

**Request:**
- Form data with `files[]` field containing multiple documents

**Response:**
- JSON array with processing results for each file

### POST /folder

Folder processing endpoint. Accepts a ZIP file containing multiple documents and processes all files.

**Request:**
- Form data with `folder` field containing a ZIP archive

**Response:**
- JSON array with processing results for each file in the ZIP

## Remote File Processing Endpoints

### POST /fileUrl/init

Remote file processing initialization endpoint. Starts processing a file from a remote URL.

**Request:**
```json
{
  "url": "https://example.com/document.pdf",
  "filename": "document.pdf",
  "config": {
    "semantic_chunking": false,
    "embeddings_provider": "cloudflare",
    "minTokens": 125,
    "maxTokens": 250,
    "chunkOverlap": 45,
    "useTokens": true
  }
}
```

**Response:**
```json
{
  "total_chunks": 42,
  "session_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### POST /fileUrl/batch

Retrieves a batch of chunks from a processing session.

**Request:**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "batch_number": 0,
  "batch_size": 50
}
```

**Response:**
- JSON array of chunks for the requested batch

### POST /fileUrl/stream

Streams all chunks from a processing session.

**Request:**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "batch_size": 50
}
```

**Response:**
- NDJSON stream of chunk batches

## Session Management Endpoints

### POST /cleanup/{session_id}

Explicitly clean up a session by ID. Removes the session data from the app config.

**Request:**
- POST to `/cleanup/{session_id}`

**Response:**
```json
{
  "success": true,
  "message": "Session {session_id} cleaned up"
}
```

**Error Responses:**
- 404 Not Found: If the session doesn't exist
  ```json
  {
    "success": false,
    "message": "Session not found"
  }
  ```
- 500 Internal Server Error: If there's an error during cleanup
  ```json
  {
    "success": false,
    "error": "Error message"
  }
  ```

### POST /session/{session_id}/cleanup

Alternative endpoint for session cleanup. This is an alias for `/cleanup/{session_id}` to support different client implementations.

**Request:**
- POST to `/session/{session_id}/cleanup`

**Response:**
- Same as `/cleanup/{session_id}`

## Error Handling

All endpoints return appropriate HTTP status codes:

- 200 OK: Request successful
- 400 Bad Request: Invalid request parameters
- 404 Not Found: Resource not found
- 500 Internal Server Error: Server-side error

Error responses include a JSON object with an `error` field containing a description of the error.
