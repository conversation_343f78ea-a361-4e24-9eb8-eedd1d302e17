#!/bin/bash
# Devcontainer postStart script: Generate SSH key, setup submodules, and configure CORS for GitHub Codespaces

set -e

# Source Codespaces environment variables if present
if [ -f "$(dirname "$0")/.env.codespaces" ]; then
  set -a
  . "$(dirname "$0")/.env.codespaces"
  set +a
fi

WORKSPACE_ROOT="/workspaces/server"
CORS_ENV_PATH="$WORKSPACE_ROOT/private-keys/local/cors.env"

# Determine an SSH directory we can write to. Prefer $HOME/.ssh, but if the
# current user cannot create $HOME (this happens in some recovery/container
# edge-cases) fall back to a workspace-local path so the script doesn't fail
# with "mkdir: cannot create directory '/home/<USER>': Permission denied".
SSH_DIR="$HOME/.ssh"
if ! mkdir -p "$SSH_DIR" 2>/dev/null; then
  SSH_DIR="$WORKSPACE_ROOT/.ssh"
  mkdir -p "$SSH_DIR"
fi
KEY_PATH="$SSH_DIR/id_ed25519"

# Function to update CORS configuration with current Codespace hostname
update_cors_config() {
  if [ -n "$CODESPACE_NAME" ]; then
    echo "🌐 Updating CORS configuration for Codespace: $CODESPACE_NAME"
    
    # Check if cors.env exists
    if [ ! -f "$CORS_ENV_PATH" ]; then
      echo "❌ CORS config file not found at: $CORS_ENV_PATH"
      echo "Please ensure private-keys submodule is properly initialized"
      return 1
    fi
    
    # Get current Codespace hostname
    CODESPACE_HOST="$CODESPACE_NAME.app.github.dev"
    
    # Define the ports that need CORS access
    CORS_PORTS="8080,8081,8082,8083,9080"
    
    # Build the new CORS origins for this Codespace
    CODESPACE_ORIGINS=""
    for port in ${CORS_PORTS//,/ }; do
      if [ -n "$CODESPACE_ORIGINS" ]; then
        CODESPACE_ORIGINS="$CODESPACE_ORIGINS,"
      fi
      CODESPACE_ORIGINS="$CODESPACE_ORIGINS$CODESPACE_NAME-$port.app.github.dev"
    done
    
    # Read current CORS_FULL_ORIGINS
    CURRENT_ORIGINS=$(grep "^CORS_FULL_ORIGINS=" "$CORS_ENV_PATH" | cut -d'=' -f2- | tr -d '"')
    
    # Remove any existing entries for this or other Codespaces (to avoid duplicates)
    CLEANED_ORIGINS=$(echo "$CURRENT_ORIGINS" | sed 's/,[^,]*\.app\.github\.dev[^,]*//g' | sed 's/^[^,]*\.app\.github\.dev[^,]*,//g' | sed 's/^[^,]*\.app\.github\.dev[^,]*$//g')
    
    # Add the new Codespace origins
    if [ -n "$CLEANED_ORIGINS" ]; then
      NEW_ORIGINS="$CLEANED_ORIGINS,$CODESPACE_ORIGINS"
    else
      NEW_ORIGINS="$CODESPACE_ORIGINS"
    fi
    
    # Update the cors.env file
    sed -i "s|^CORS_FULL_ORIGINS=.*|CORS_FULL_ORIGINS=\"$NEW_ORIGINS\"|" "$CORS_ENV_PATH"
    
    echo "✅ Updated CORS configuration with Codespace origins:"
    echo "   $CODESPACE_ORIGINS"
  else
    echo "⚠️  CODESPACE_NAME not set, skipping CORS configuration update"
  fi
}

# Function to wait for user to add SSH key
wait_for_ssh_key() {
  echo "⏳ Waiting for SSH key to be added to GitHub..."
  echo "   Testing SSH connection to GitHub every 10 seconds..."
  
  local max_attempts=30  # 5 minutes max
  local attempt=0
  
  while [ $attempt -lt $max_attempts ]; do
    if ssh -T ************** 2>&1 | grep -q "successfully authenticated"; then
      echo "✅ SSH key is working! GitHub authentication successful."
      return 0
    fi
    
    attempt=$((attempt + 1))
    echo "   Attempt $attempt/$max_attempts - still waiting..."
    sleep 10
  done
  
  echo "⚠️  Timeout waiting for SSH key. You may need to add it manually."
  return 1
}

# Generate SSH key if it doesn't exist
if [ ! -f "$KEY_PATH" ]; then
  echo "🔑 Generating new SSH key for Codespaces..."
  ssh-keygen -t ed25519 -C "codespace-$(hostname)-$(date +%Y-%m-%d)" -f "$KEY_PATH" -N ''
fi

# Configure SSH (write config next to the key in our chosen SSH_DIR)
cat > "$SSH_DIR/config" << EOF
Host github.com
  HostName github.com
  User git
  IdentityFile $KEY_PATH
  StrictHostKeyChecking no
EOF

PUB_KEY=$(cat "$KEY_PATH.pub")

cat <<EOM

============================================================
🚀 GITHUB CODESPACES SETUP

Your SSH public key for GitHub submodule access:

$PUB_KEY

📋 REQUIRED: Add this key to your GitHub account:
   👉 https://github.com/settings/ssh/new

Or use the GitHub CLI (if authenticated):
   gh ssh-key add "$KEY_PATH.pub" --title "Codespace $(hostname) $(date +%Y-%m-%d)"

Or use the GitHub API:
   curl -H "Authorization: token <YOUR_PERSONAL_ACCESS_TOKEN>" \\
        -H "Accept: application/vnd.github+json" \\
        https://api.github.com/user/keys \\
        -d '{"title":"Codespace $(hostname) $(date +%Y-%m-%d)","key":"$PUB_KEY"}'

============================================================

EOM

# Check if private-keys submodule is already available
if [ ! -d "$WORKSPACE_ROOT/local" ] || [ ! -f "$CORS_ENV_PATH" ]; then
  echo "📦 Private keys submodule not found. Setting up submodules..."
  
  # Wait for user to add SSH key
  wait_for_ssh_key
  
  # Initialize and update submodules
  echo "🔄 Initializing git submodules..."
  cd "$WORKSPACE_ROOT"
  
  # Configure git to use SSH for submodules
  git config url."**************:".insteadOf "https://github.com/"
  
  # Initialize submodules
  if git submodule sync && git submodule update --init --recursive --remote; then
    echo "✅ Submodules initialized successfully"
  else
    echo "❌ Failed to initialize submodules. Please check your SSH key setup."
    exit 1
  fi
else
  echo "✅ Private keys submodule already available"
fi

# Update CORS configuration
update_cors_config

# Create a script to easily restart services with updated CORS
cat > "$WORKSPACE_ROOT/restart-services.sh" << 'EOF'
#!/bin/bash
# Restart all services with updated configuration

echo "🔄 Restarting all services..."

# Kill existing processes
pkill -f "pnpm start" || true
pkill -f "node.*dist" || true

# Wait a moment for processes to clean up
sleep 2

# Start services using the existing script
exec /workspaces/server/start-codespace.sh
EOF

chmod +x "$WORKSPACE_ROOT/restart-services.sh"

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📋 Next steps:"
echo "   1. Your CORS configuration has been updated for this Codespace"
echo "   2. Run: ./start-codespace.sh (or ./restart-services.sh) to start all services"
echo "   3. Your services will be available at:"
echo "      • Web Client: https://$CODESPACE_NAME-8080.app.github.dev"
echo "      • Public API: https://$CODESPACE_NAME-9080.app.github.dev"
echo "      • API Live: https://$CODESPACE_NAME-8081.app.github.dev"
echo "      • API Webhook: https://$CODESPACE_NAME-8083.app.github.dev"
echo ""

# Optionally auto-start services when running inside Codespaces/devcontainer
if [ "${AUTO_START_SERVICES:-false}" = "true" ]; then
  echo "🚦 AUTO_START_SERVICES is enabled — starting services in background"
  # Start services in background and capture logs
  nohup /workspaces/server/start-codespace.sh > /workspaces/server/start.log 2>&1 &
  disown
  echo "Logs: /workspaces/server/start.log"
fi
