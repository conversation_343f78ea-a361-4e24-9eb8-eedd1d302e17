{"name": "Divinci Server - TypeScript Node.js", "build": {"dockerfile": "Dockerfile", "context": ".."}, "features": {"ghcr.io/devcontainers/features/docker-outside-of-docker:1": {"moby": false, "dockerDashComposeVersion": "v2"}}, "customizations": {"vscode": {"extensions": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-playwright.playwright", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-azuretools.vscode-docker", "GitHub.copilot", "GitHub.copilot-chat"], "settings": {"typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.workingDirectories": ["workspace/clients", "workspace/servers", "workspace/resources"], "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true, "**/build/**": true, "**/.git/**": true}}}}, "forwardPorts": [8080, 9080, 8081, 8082, 8083, 27017, 6379], "portsAttributes": {"8080": {"label": "Web Client", "onAutoForward": "notify", "visibility": "public", "protocol": "https"}, "9080": {"label": "Public API", "onAutoForward": "notify", "visibility": "public", "protocol": "https"}, "8081": {"label": "Public API Live", "onAutoForward": "notify", "visibility": "public", "protocol": "https"}, "8082": {"label": "Public API Live (Alt)", "onAutoForward": "notify", "visibility": "public", "protocol": "https"}, "8083": {"label": "Webhook API", "onAutoForward": "notify", "visibility": "public", "protocol": "https"}, "27017": {"label": "MongoDB", "onAutoForward": "silent", "visibility": "private"}, "6379": {"label": "Redis", "onAutoForward": "silent", "visibility": "private"}}, "postCreateCommand": "pnpm install", "postStartCommand": "/bin/bash .devcontainer/postStart.sh", "containerEnv": {"COMPOSE_BAKE": "true", "NODE_ENV": "development", "ENVIRONMENT": "development", "PIDS_DIR": "/workspaces/server/run", "AUTO_START_SERVICES": "true"}, "remoteUser": "node"}