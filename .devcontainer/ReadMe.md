# GitHub Codespace Development Container

This directory contains the configuration for GitHub Codespaces development environment for the Divinci Server monorepo.

## What's Included

### Base Image

- **mcr.microsoft.com/devcontainers/typescript-node:1-22**
  - Node.js 22 with TypeScript support
  - Essential development tools (git, curl, wget, etc.)
  - Non-root user with sudo access

### Architecture

- **Docker Compose Setup**: Uses a simplified Docker Compose approach to avoid complex buildx issues
- **Docker Socket Mounting**: Enables running Docker commands within the codespace
- **Initialization Script**: Automated setup via postCreate command in devcontainer.json

### VS Code Extensions

- TypeScript and JavaScript support
- Tailwind CSS IntelliSense
- Prettier code formatting
- ESLint linting
- Playwright testing support
- Docker extension
- GitHub Copilot (if available)

### Port Forwarding

The following ports are automatically forwarded:

- **8080**: Web Client (main application)
- **9080**: Public API

### Remote Services Configuration

- **Uses Cloudflare R2 Development Environment**: No local MinIO required
- **Uses Remote Redis**: Connects to `redis.dev.divinci.app:6379`
- **Uses Remote MongoDB**: Connects to MongoDB Atlas development cluster
- **Environment**: Set to `development` to use cloud development services

## Setup Process

When you create a new codespace, the following happens automatically:

1. **Container Creation**: The TypeScript Node.js container is created with Docker socket access
2. **Dependencies Installation**: `pnpm install` runs to install all monorepo dependencies
3. **Services Startup**: `COMPOSE_BAKE=true docker compose -f docker/fast-local.yml up -d` starts only the web client and API containers
4. **Cloud Connections**: The API automatically connects to remote Redis, MongoDB, and R2 services

## Accessing the Application

Once the codespace is ready and all services are running:

1. **Web Application**: Click on the forwarded port 8080 or visit the URL provided by Codespaces
2. **API Endpoints**: Access the public API via port 9080
3. **Remote Services**:
   - **Storage**: Cloudflare R2 development environment
   - **Database**: MongoDB Atlas development cluster
   - **Cache**: Remote Redis development instance

## Development Workflow

### Running Commands

```bash
# Install dependencies
pnpm install

# Run tests
pnpm test

# Start services manually (if needed)
COMPOSE_BAKE=true docker compose -f docker/fast-local.yml up --build

# Stop services
docker compose -f docker/fast-local.yml down
```

### Working with the Monorepo

The workspace is organized as:

- `workspace/clients/` - Frontend applications
- `workspace/servers/` - Backend services
- `workspace/resources/` - Shared resources and utilities

### Environment Variables

The codespace automatically sets:

- `COMPOSE_BAKE=true` - Enables Docker Compose baking
- `NODE_ENV=development` - Development environment
- `ENVIRONMENT=development` - Forces use of cloud development services
- `REDIS_DOMAIN_HOSTNAME=redis.dev.divinci.app` - Remote Redis endpoint
- `MONGO_DOMAIN_HOSTNAME=serverlessinstance0.c4pobzg.mongodb.net` - MongoDB Atlas cluster
- Proper PNPM configuration

## Troubleshooting

### Services Not Starting

If services fail to start, check:

```bash
# Check service status
docker compose -f docker/fast-local.yml ps

# View logs
docker compose -f docker/fast-local.yml logs

# Restart services
docker compose -f docker/fast-local.yml restart
```

### Port Conflicts

If you encounter port conflicts, you can modify the port mappings in `docker/fast-local.yml` or use different local ports.

### Performance

For better performance in Codespaces:

- Use the largest available machine type for resource-intensive operations
- Consider using prebuilds for faster startup times

## Key Differences from Complex Setups

This configuration uses a simplified approach:

- **No Docker Buildx**: Avoids buildx-related issues in Codespaces
- **Direct Docker Socket**: Uses socket mounting instead of Docker-in-Docker features
- **Initialization Script**: All setup logic is in a single, readable bash script
- **Compose-based**: Leverages Docker Compose for service orchestration

## Customization

To customize this development environment:

1. Edit `.devcontainer/devcontainer.json` for container configuration
<!-- 2. Modify `.devcontainer/docker-compose.yml` for service setup -->
2. Update postCreate command in devcontainer.json` for initialization logic
3. Adjust VS Code settings and extensions as needed

## Support

For issues with the development environment:

1. Check the Codespace logs in VS Code
2. Review Docker container logs
3. Ensure all required environment files are present in `private-keys/local/`
4. Check the initialization script output for any errors

### Notes about SSH key location

In some Codespaces/container recovery modes the default home directory may not be writable for the `node` user. The `postStart.sh` script will now fall back to placing generated SSH keys under `.devcontainer/.ssh` inside the workspace (e.g. `/workspaces/server/.ssh`) if `$HOME/.ssh` cannot be created. If you don't find your SSH key in `$HOME/.ssh`, check the workspace `.ssh` directory.

`/workspaces/server/scripts/dev-web-client-codespaces.sh`
