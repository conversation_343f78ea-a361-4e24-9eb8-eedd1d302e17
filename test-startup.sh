#!/bin/bash
# Automated startup test script for Codespace services
# This script starts all services and tests that they are responding correctly

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="$SCRIPT_DIR"
START_LOG="$LOG_DIR/start.log"
TEST_RESULTS_LOG="$LOG_DIR/test-startup-results.log"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "🧪 Automated Startup Test Script"
echo "================================"
echo ""

# Function to log results
log_result() {
  echo "$1" | tee -a "$TEST_RESULTS_LOG"
}

# Clear previous test results
> "$TEST_RESULTS_LOG"

# Step 1: Start services
echo "📋 Step 1: Starting all services..."
log_result "$(date): Starting services"

if [ -f "$SCRIPT_DIR/start-codespace.sh" ]; then
  # Run start script in background and capture output
  "$SCRIPT_DIR/start-codespace.sh" > "$START_LOG" 2>&1 &
  START_PID=$!

  echo "   Started start-codespace.sh (PID: $START_PID)"
  log_result "Started start-codespace.sh (PID: $START_PID)"

  # Wait for the script to complete (with timeout)
  TIMEOUT=300 # 5 minutes
  ELAPSED=0
  while kill -0 "$START_PID" 2>/dev/null; do
    if [ $ELAPSED -ge $TIMEOUT ]; then
      echo -e "   ${RED}✗ Timeout waiting for start script${NC}"
      log_result "FAIL: Timeout waiting for start script after ${TIMEOUT}s"
      exit 1
    fi
    sleep 5
    ELAPSED=$((ELAPSED + 5))
    # Show progress
    if [ $((ELAPSED % 30)) -eq 0 ]; then
      echo "   ... still waiting ($ELAPSED seconds elapsed)"
    fi
  done

  echo -e "   ${GREEN}✓ Start script completed${NC}"
  log_result "SUCCESS: Start script completed"
else
  echo -e "   ${RED}✗ start-codespace.sh not found${NC}"
  log_result "FAIL: start-codespace.sh not found"
  exit 1
fi

echo ""

# Step 2: Wait for ports to be listening
echo "📋 Step 2: Waiting for services to bind to ports..."
SERVICES=(
  "8080:web-client"
  "9080:public-api"
  "8081:public-api-live"
  "8083:public-api-webhook"
)

ALL_PORTS_OK=true
for service in "${SERVICES[@]}"; do
  PORT="${service%%:*}"
  NAME="${service##*:}"

  echo "   Checking $NAME on port $PORT..."

  # Wait up to 30 seconds for port to be listening
  TIMEOUT=30
  ELAPSED=0
  PORT_LISTENING=false

  while [ $ELAPSED -lt $TIMEOUT ]; do
    if ss -ltnp 2>/dev/null | grep -E ":${PORT}\\b" >/dev/null 2>&1; then
      PORT_LISTENING=true
      break
    fi
    sleep 1
    ELAPSED=$((ELAPSED + 1))
  done

  if [ "$PORT_LISTENING" = true ]; then
    echo -e "   ${GREEN}✓ $NAME is listening on port $PORT${NC}"
    log_result "SUCCESS: $NAME listening on port $PORT"
  else
    echo -e "   ${RED}✗ $NAME failed to bind to port $PORT${NC}"
    log_result "FAIL: $NAME not listening on port $PORT after ${TIMEOUT}s"
    ALL_PORTS_OK=false
  fi
done

if [ "$ALL_PORTS_OK" = false ]; then
  echo ""
  echo -e "${RED}Some services failed to start. Check logs for details.${NC}"
  exit 1
fi

echo ""

# Step 3: Test HTTP responses
echo "📋 Step 3: Testing HTTP responses..."
TEST_CASES=(
  "https://localhost:8080/:web-client:200"
  "https://localhost:9080/:public-api:200"
  "https://localhost:8081/:public-api-live:200"
  "https://localhost:8083/:public-api-webhook:200"
)

ALL_TESTS_PASSED=true
for test_case in "${TEST_CASES[@]}"; do
  URL="${test_case%%:*}"
  TEMP="${test_case#*:}"
  NAME="${TEMP%%:*}"
  EXPECTED_STATUS="${TEMP##*:}"

  echo "   Testing $NAME at $URL..."

  # Try HTTPS with -k flag to allow self-signed certs
  HTTP_STATUS=$(curl -sk -o /dev/null -w "%{http_code}" "$URL" 2>/dev/null || echo "000")

  if [ "$HTTP_STATUS" = "$EXPECTED_STATUS" ]; then
    echo -e "   ${GREEN}✓ $NAME responded with HTTP $HTTP_STATUS${NC}"
    log_result "SUCCESS: $NAME responded with HTTP $HTTP_STATUS"
  else
    echo -e "   ${RED}✗ $NAME returned HTTP $HTTP_STATUS (expected $EXPECTED_STATUS)${NC}"
    log_result "FAIL: $NAME returned HTTP $HTTP_STATUS (expected $EXPECTED_STATUS)"
    ALL_TESTS_PASSED=false
  fi
done

echo ""

# Step 4: Check service logs for errors
echo "📋 Step 4: Checking service logs for errors..."
LOG_FILES=(
  "$LOG_DIR/public-api.log:public-api"
  "$LOG_DIR/public-api-live.log:public-api-live"
  "$LOG_DIR/public-api-webhook.log:public-api-webhook"
  "$LOG_DIR/web-client.log:web-client"
)

for log_entry in "${LOG_FILES[@]}"; do
  LOG_FILE="${log_entry%%:*}"
  SERVICE_NAME="${log_entry##*:}"

  if [ -f "$LOG_FILE" ]; then
    # Check for common error patterns (excluding expected debug/warning messages)
    ERROR_COUNT=$(grep -i "error" "$LOG_FILE" | grep -v "TypeScript errors ignored" | grep -v "ℹ️" | wc -l || echo "0")

    if [ "$ERROR_COUNT" -eq 0 ]; then
      echo -e "   ${GREEN}✓ No errors found in $SERVICE_NAME log${NC}"
      log_result "SUCCESS: No errors in $SERVICE_NAME log"
    else
      echo -e "   ${YELLOW}⚠ Found $ERROR_COUNT potential errors in $SERVICE_NAME log${NC}"
      log_result "WARNING: Found $ERROR_COUNT potential errors in $SERVICE_NAME log"
    fi
  else
    echo -e "   ${YELLOW}⚠ Log file not found: $LOG_FILE${NC}"
    log_result "WARNING: Log file not found for $SERVICE_NAME"
  fi
done

echo ""

# Final summary
echo "================================"
if [ "$ALL_PORTS_OK" = true ] && [ "$ALL_TESTS_PASSED" = true ]; then
  echo -e "${GREEN}✅ All startup tests passed!${NC}"
  log_result "$(date): ALL TESTS PASSED"
  echo ""
  echo "Services are running at:"
  if [ -n "$CODESPACE_NAME" ]; then
    echo "  • Web Client:     https://$CODESPACE_NAME-8080.app.github.dev"
    echo "  • Public API:     https://$CODESPACE_NAME-9080.app.github.dev"
    echo "  • API Live:       https://$CODESPACE_NAME-8081.app.github.dev"
    echo "  • API Webhook:    https://$CODESPACE_NAME-8083.app.github.dev"
  else
    echo "  • Web Client:     https://localhost:8080"
    echo "  • Public API:     https://localhost:9080"
    echo "  • API Live:       https://localhost:8081"
    echo "  • API Webhook:    https://localhost:8083"
  fi
  echo ""
  echo "Test results: $TEST_RESULTS_LOG"
  exit 0
else
  echo -e "${RED}❌ Some startup tests failed${NC}"
  log_result "$(date): SOME TESTS FAILED"
  echo ""
  echo "Check logs for details:"
  echo "  - Start log:      $START_LOG"
  echo "  - Test results:   $TEST_RESULTS_LOG"
  echo "  - Service logs:   $LOG_DIR/*.log"
  exit 1
fi
